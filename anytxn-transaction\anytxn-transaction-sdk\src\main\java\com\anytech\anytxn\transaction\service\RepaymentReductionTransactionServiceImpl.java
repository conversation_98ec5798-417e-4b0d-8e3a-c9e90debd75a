package com.anytech.anytxn.transaction.service;


import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.transaction.domain.dto.LimitControlUnitDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO;
import com.anytech.anytxn.rule.limit.mapper.broadcast.LimitUnitDefiMapper;
import com.anytech.anytxn.rule.limit.model.LimitUnitDefi;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.base.account.enums.InterestRateFlagEnum;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.InterestLogHistoryDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.PaymentAllocationHistoryMapper;
import com.anytech.anytxn.business.dao.transaction.model.InterestLogHistory;
import com.anytech.anytxn.business.dao.transaction.model.PaymentAllocationHistory;
import com.anytech.anytxn.limit.base.constant.LimitConstant;
import com.anytech.anytxn.limit.base.domain.dto.LimitCtrlUnitDTO;
import com.anytech.anytxn.limit.base.domain.dto.LimitPreOccupiedReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitUnitReqDTO;
import com.anytech.anytxn.limit.service.LimitPreOccupiedService;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestBearingDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSettlementDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.ParmBalancePricingTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedResDTO;
import com.anytech.anytxn.parameter.base.account.service.IInterestBearingService;
import com.anytech.anytxn.parameter.base.account.service.IInterestSettlementService;
import com.anytech.anytxn.parameter.base.account.service.IParmBalancePricingTableService;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedControlService;
import com.anytech.anytxn.parameter.base.account.service.IPaymentAllocatedService;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductInfoDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAcctProductMainInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.bo.PaymentAllocationAndBalanceBO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.domain.dto.PaymentAllocationHistoryDTO;
import com.anytech.anytxn.transaction.base.enums.AllocationTypeEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.InterestIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.InterestProssingIndicatorEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.enums.TransactionRepDetailEnum;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import com.anytech.anytxn.transaction.base.service.IInterestAccureCalcService;
import com.anytech.anytxn.transaction.base.service.IRejectTransactionService;
import com.anytech.anytxn.transaction.base.service.IRepaymentReductionTransactionService;
import com.anytech.anytxn.transaction.base.service.ITransactionAllocationService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedCacheAccountBalanceService;
import com.anytech.anytxn.transaction.service.prepare.RuleExecute;
import com.anytech.anytxn.transaction.service.transcommon.TransTraceLogService;
import com.anytech.anytxn.transaction.base.utils.TransPartitionKeyHelper;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 还款还原入账处理
 *
 * <AUTHOR>
 * @date 2018-09-03
 */
@Service
public class RepaymentReductionTransactionServiceImpl implements IRepaymentReductionTransactionService {
    private static final Logger logger = LoggerFactory.getLogger(RepaymentReductionTransactionServiceImpl.class);
    @Autowired
    private PaymentAllocationHistoryMapper paymentAllocationHistoryMapper;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;
    @Autowired
    private AccountBalanceInfoMapper accountBalanceInfoMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private ITransactionTypeService transactionTypeService;
    @Autowired
    private IGlAmsService glAmsService;
    @Autowired
    private IRejectTransactionService rejectTransactionService;
    @Autowired
    private IPaymentAllocatedService paymentAllocatedService;
    @Autowired
    private IParmBalancePricingTableService parmBalancePricingTableService;
    @Autowired
    private IInterestSettlementService interestSettlementService;
    @Autowired
    private IPaymentAllocatedControlService paymentAllocatedControlService;
    @Autowired
    private OuterLimitInit outerLimitInit;
    @Autowired
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Autowired
    private TxnRecordedCacheAccountBalanceService txnRecordedCacheAccountBalanceService;
    @Autowired
    private IInterestAccureCalcService interestAccureCalcService;

    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private IInterestBearingService interestBearingService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private ITransactionAllocationService transactionAllocationService;
    @Autowired
    private TransactionRoutingRuleHandle transactionRoutingRuleHandle;
    @Autowired
    private LimitUnitDefiMapper limitUnitDefiMapper;
    @Autowired
    private LimitPreOccupiedService limitPreOccupiedService;
    @Autowired
    private TransTraceLogService transTraceLogService;
    @Autowired
    private IAcctProductMainInfoService acctProductMainInfoService;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    @Override
    public void accessRepaymentReduction(RecordedMidBO recordedMidBO) {
        logger.info("Repayment reduction processing started");
        //统计账户更新
        updateAcctStatisticInfo(recordedMidBO);
        //还款分配历史的获取
        List<PaymentAllocationHistoryDTO> paymentAllocationHistories = getPaymentAllocationHistory(recordedMidBO);
        //还款分配历史溢缴款记录标志初始化
        initPaymentAllocationOverflowPayFlag(paymentAllocationHistories);
        //内转处理
        netOutProcess(recordedMidBO, paymentAllocationHistories, calAccountOverFlowFlag(recordedMidBO)
                ,calOverFlowPaymentBalance(recordedMidBO));
        //更新还款分配流水表中的还款还原标志
        updatePaymentAllocationHistoryReversalIndicator(recordedMidBO);
        //更新已入账交易明细的冲减/还原标志
        updatePostedTransactionReversalIndicator(recordedMidBO);
        //更新管理账户的还款金额
        updateAccountPaymentAmount(recordedMidBO,paymentAllocationHistories.get(0).getBillingDate());
        logger.info("Repayment reduction processing completed");
    }

    /**
     *  更新管理账户的还款金额
     * @param recordedMidBO 入账BO
     * @param originalBillDate
     */
    public void updateAccountPaymentAmount(RecordedMidBO recordedMidBO, LocalDate originalBillDate){


        AccountManagementInfoDTO accountManagementInfoDTO = txnRecordedAccountCacheService.accountManagementInfoDTOSelectByPrimaryKey(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        if(null == accountManagementInfoDTO){
            return;
        }
        LocalDate lastStatementDate = accountManagementInfoDTO.getLastStatementDate();


        if(null == lastStatementDate){
            return;
        }
        //如果原还款交易已出账单，不更新，退出
        if(originalBillDate.isBefore(lastStatementDate)){
            logger.debug("This repayment has been billed, no payment amount processing needed");
            return;
        }
        //根据上一账单日查询上一账单
        AccountStatementInfo lastStatement =  accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate( accountManagementInfoDTO.getAccountManagementId(), lastStatementDate);
        if(null == lastStatement){
            return;
        }

        //只有在出过账单，并且原还款交易在未出账单的情况下才进行字段的更新
        BigDecimal txnBillingAmount = recordedMidBO.recorded.getTxnBillingAmount();
        //宽限期后的还款交易，不用更新宽限期内还款金额
        if(null != lastStatement.getInterestWaiveGraceDate() && originalBillDate.isAfter(lastStatement.getInterestWaiveGraceDate())){
            logger.debug("Repayment transaction after grace period, no need to update grace period payment amount");
        }else {
            if(null != accountManagementInfoDTO.getTotalGracePaymentAmount() && accountManagementInfoDTO.getTotalGracePaymentAmount().compareTo(BigDecimal.ZERO) >0){
                BigDecimal totalGracePayAmtSub= accountManagementInfoDTO.getTotalGracePaymentAmount().subtract(txnBillingAmount);
                accountManagementInfoDTO.setTotalGracePaymentAmount(totalGracePayAmtSub.compareTo(BigDecimal.ZERO) <0 ? BigDecimal.ZERO : totalGracePayAmtSub);
            }
        }

        //现在这个字段暂时未生效。通过页面实时计算。待设计梳理流程。目前库里都是0和null
        if(null != accountManagementInfoDTO.getTotalPaymentAmount() && accountManagementInfoDTO.getTotalPaymentAmount().compareTo(BigDecimal.ZERO) >0){
            BigDecimal totalPayAmtSub = accountManagementInfoDTO.getTotalPaymentAmount().subtract(txnBillingAmount);
            accountManagementInfoDTO.setTotalPaymentAmount(totalPayAmtSub.compareTo(BigDecimal.ZERO) <0 ? BigDecimal.ZERO : totalPayAmtSub);
        }

        //账单剩余未还金额。更新为+还款还原金额，且不能超过账单期末欠款总金额
        if(null != accountManagementInfoDTO.getStatementDueAmount() && null != lastStatement.getPaymentAmount() && accountManagementInfoDTO.getStatementDueAmount().compareTo(BigDecimal.ZERO) >0){
            BigDecimal newStatementDueAmount = accountManagementInfoDTO.getStatementDueAmount().add(txnBillingAmount);
            accountManagementInfoDTO.setStatementDueAmount(newStatementDueAmount.compareTo(lastStatement.getPaymentAmount()) <0 ? newStatementDueAmount : lastStatement.getPaymentAmount());

        }

        txnRecordedAccountCacheService.accountManagementInfoUpdateByPrimaryKey(BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class));

    }


    /**
     * 更新统计账户信息
     *
     * @param recordedMidBO 入账BO
     */
    private void updateAcctStatisticInfo(RecordedMidBO recordedMidBO) {
        AccountStatisticsInfo statisticsInfo =txnRecordedAccountCacheService.accountStatisticsInfoSelectOverPaymentAsi(recordedMidBO.recorded.getTxnAccountManageId());
        if (statisticsInfo != null) {
            statisticsInfo.setDebitTotalAmount(statisticsInfo.getDebitTotalAmount().add
                    (recordedMidBO.recorded.getTxnBillingAmount()));
            statisticsInfo.setCycleToDateDebitAmount(statisticsInfo.getCycleToDateDebitAmount().add
                    (recordedMidBO.recorded.getTxnBillingAmount()));
            statisticsInfo.setLastActivityAmount(recordedMidBO.recorded.getTxnBillingAmount());
            statisticsInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
            statisticsInfo.setUpdateTime(LocalDateTime.now());
            try {
                txnRecordedAccountCacheService.accountStatisticsInfoUpdateByPrimaryKeySelective(statisticsInfo);

            } catch (Exception e) {
                logger.error("Failed to update account statistics info", e);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);
            }
        } else {
            String customId = recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId();
            int partition = TransPartitionKeyHelper.getPartitionKeyInt(customId);
            AccountStatisticsInfo acctStatisticsInfo = new AccountStatisticsInfo();
            acctStatisticsInfo.setStatisticsId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            acctStatisticsInfo.setAccountManagementId(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
            acctStatisticsInfo.setCustomerId(customId);
            acctStatisticsInfo.setTransactionTypeCode(TransactionConstants.TRANSACTION_TYPE_OVERFLOW);
            acctStatisticsInfo.setOrganizationNumber(recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber());
            acctStatisticsInfo.setCurrency(recordedMidBO.accountBO.getAccountManagementInfo().getCurrency());
            acctStatisticsInfo.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
            acctStatisticsInfo.setBalance(recordedMidBO.recorded.getTxnBillingAmount());
            acctStatisticsInfo.setStatementBalance(BigDecimal.ZERO);
            acctStatisticsInfo.setLastActivityDate(recordedMidBO.recorded.getTxnBillingDate());
            acctStatisticsInfo.setLastActivityAmount(recordedMidBO.recorded.getTxnBillingAmount());
            acctStatisticsInfo.setCycleToDateDebitAmount(recordedMidBO.recorded.getTxnBillingAmount());
            acctStatisticsInfo.setCycleToDateCreditAmount(BigDecimal.ZERO);
            acctStatisticsInfo.setLastCycleDebitAmount(BigDecimal.ZERO);
            acctStatisticsInfo.setLastCycleCreditAmount(BigDecimal.ZERO);
            acctStatisticsInfo.setDebitTotalAmount(recordedMidBO.recorded.getTxnBillingAmount());
            acctStatisticsInfo.setCreditTotalAmount(BigDecimal.ZERO);
            acctStatisticsInfo.setPartitionKey(partition);
            acctStatisticsInfo.setCreateTime(LocalDateTime.now());
            acctStatisticsInfo.setUpdateTime(LocalDateTime.now());
            acctStatisticsInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
            acctStatisticsInfo.setVersionNumber(1L);
            try {
                txnRecordedAccountCacheService.accountStatisticsInfoInsertSelective(acctStatisticsInfo);

            } catch (Exception e) {
                logger.error("Failed to insert account statistics info", e);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR, e);
            }
        }
    }

    /**
     * 更新已入账交易明细的冲减/还原标志
     *
     * @param recordedMidBO 接口数据
     */
    private void updatePostedTransactionReversalIndicator(RecordedMidBO recordedMidBO) {

        txnRecordedAccountCacheService.postedTransactionUpdateReversalIndicatorByAccountManagementIdAndGlobalFlowNumber(recordedMidBO.recorded.getTxnAccountManageId(),
                recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber(), "Y");
    }

    /**
     * 更新还款分配流水表中的还款还原标志
     *
     * @param recordedMidBO 接口变量
     */
    private void updatePaymentAllocationHistoryReversalIndicator(
            RecordedMidBO recordedMidBO) {

        txnRecordedAccountCacheService.paymentAllocationHistoryUpdateReversalIndicatorByOriginalGlobalFlowNumberAndAccountManageId(
                "Y", recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber(),
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId()
        );

    }

    /**
     * 还款分配历史的获取
     * @param recordedMidBO 接口变量
     * @return 还款分配历史的集合
     */
    private List<PaymentAllocationHistoryDTO> getPaymentAllocationHistory(RecordedMidBO recordedMidBO) {

        List<PaymentAllocationHistory> paymentAllocationHistoryList = txnRecordedAccountCacheService.
                paymentAllocationHistoryListByTxnOriginalGlobalFlowNumberAndTxnAccountManageId(recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber(),
                        recordedMidBO.recorded.getTxnAccountManageId());
        //TODO
//        List<PaymentAllocationHistoryDTO> paymentAllocationDTOHistoryList = null;

        return BeanMapping.copyList(paymentAllocationHistoryList, PaymentAllocationHistoryDTO.class);
    }

    /**
     * 设置还款分配流水记录的溢缴款标志
     *
     * @param paymentAllocationHistories 还款分配流水记录
     */
    private int initPaymentAllocationOverflowPayFlag(List<PaymentAllocationHistoryDTO> paymentAllocationHistories) {
        int paymentAllocationOverflowPayFlag = 0;
        if (paymentAllocationHistories.size() == 1 && TransactionConstants.TRANSACTION_TYPE_OVERFLOW
                .equals(paymentAllocationHistories.get(0).getTransactionBalanceType())) {
            paymentAllocationOverflowPayFlag = 1;
        }
        if (paymentAllocationHistories.size() > 1 && paymentAllocationHistories.stream().anyMatch(
                paymentAllocationHistory -> isOverflow(paymentAllocationHistory.getTransactionBalanceType()))) {
            paymentAllocationOverflowPayFlag = 2;
        }
        return paymentAllocationOverflowPayFlag;
    }


    /**
     * 编辑当前账户溢缴款标志
     *
     * @param recordedMidBO 接口变量
     */
    public int calAccountOverFlowFlag(RecordedMidBO recordedMidBO) {
        AccountBalanceInfo accountBalanceInfo = accountBalanceInfoSelfMapper.selectOverPaymentAbi(
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        int accountOverflowPayFlag;
        if ( accountBalanceInfo==null  || accountBalanceInfo.getBalance().equals(BigDecimal.ZERO)) {
            accountOverflowPayFlag = 0;
        } else if (accountBalanceInfo.getBalance().compareTo(recordedMidBO.recorded.getTxnBillingAmount()) >= 0) {
            accountOverflowPayFlag = 1;
        } else {
            accountOverflowPayFlag = 2;
        }
        return accountOverflowPayFlag;
    }

    /**
     * 计算溢缴款账户更新金额
     *
     * @param recordedMidBO 接口数据
     * @return 溢缴款账户跟新金额
     */
    private BigDecimal calOverFlowPaymentBalance(RecordedMidBO recordedMidBO) {
        BigDecimal overFlowPaymentBalance;
        AccountBalanceInfo accountBalanceInfo = accountBalanceInfoSelfMapper.selectOverPaymentAbi(
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        //无溢缴款
        if ( accountBalanceInfo==null  || accountBalanceInfo.getBalance().equals(BigDecimal.ZERO)) {
            overFlowPaymentBalance = BigDecimal.ZERO;
            return overFlowPaymentBalance;
            //有溢缴款
        } else if (accountBalanceInfo.getBalance().compareTo(recordedMidBO.recorded.getTxnBillingAmount()) >= 0) {
            overFlowPaymentBalance = recordedMidBO.recorded.getTxnBillingAmount();
        } else {
            overFlowPaymentBalance = accountBalanceInfo.getBalance();
        }
        //溢缴款交易账户的更新
        updateAccountBalanceInfoWithOverflow(recordedMidBO,accountBalanceInfo, overFlowPaymentBalance);
        //余额变动会计流水
        AccountantGlamsDTO tAmsGlams = glAmsService.buildGlAms(recordedMidBO, BeanMapping.copy(accountBalanceInfo, AccountBalanceInfoDTO.class),overFlowPaymentBalance);
        //存储会计流水
        glAmsService.saveGlAms(tAmsGlams);
        //更新溢缴款统计账户信息
        updateAccountStaticsWithOverflow(recordedMidBO, overFlowPaymentBalance);
        return overFlowPaymentBalance;
    }

    /**
     * 更新溢缴款交易账户
     *
     * @param accountBalanceInfo     溢缴款交易账户
     * @param overflowPaymentBalance 账户更新金额
     */
    public void updateAccountBalanceInfoWithOverflow(RecordedMidBO recordedMidBO, AccountBalanceInfo accountBalanceInfo,
                                                     BigDecimal overflowPaymentBalance) {
        BigDecimal beforeValue = accountBalanceInfo.getBalance();
        BigDecimal amount = accountBalanceInfo.getAmount().subtract(overflowPaymentBalance);
        accountBalanceInfo.setAmount(amount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : amount);
        BigDecimal balance = accountBalanceInfo.getBalance().subtract(overflowPaymentBalance);
        accountBalanceInfo.setBalance(balance.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : balance);
        accountBalanceInfo.setUpdateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateBy("ADMIN");
//        accountBalanceInfoMapper.updateByPrimaryKey(accountBalanceInfo);
        txnRecordedCacheAccountBalanceService.updateByPrimaryKey(accountBalanceInfo);
        transTraceLogService.updateAccountBalance(recordedMidBO,beforeValue,accountBalanceInfo);
    }

    /**
     * 更新溢缴款统计账户信息
     *
     * @param recordedMidBO             接口信息
     * @param overflowPaymentBalance 溢缴款余额
     */
    public void updateAccountStaticsWithOverflow(RecordedMidBO recordedMidBO, BigDecimal overflowPaymentBalance) {
        AccountStatisticsInfo accountStatisticsInfo = accountStatisticsInfoSelfMapper.selectOverPaymentAsi(
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        BigDecimal balance = accountStatisticsInfo.getBalance().subtract(overflowPaymentBalance);
        accountStatisticsInfo.setBalance(balance.compareTo(BigDecimal.ZERO) >= 0 ? balance : BigDecimal.ZERO);
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy("ADMIN");
//        accountStatisticsInfoMapper.updateByPrimaryKeySelective(accountStatisticsInfo);
        txnRecordedAccountCacheService.accountStatisticsInfoUpdateByPrimaryKeySelective(accountStatisticsInfo);

    }


    /**
     * 内转处理
     * 这里逻辑有点乱
     * 注意这里有两个对还款流水的循环，
     * 首先循环1是用账户的溢缴款冲减还款还原的金额，比如溢缴款金额10，需要还原的金额为100，则先用10去抵消
     * 然后循环2是还原剩余的金额，如上例中的90
     *
     * @param recordedMidBO                 接口数据
     * @param paymentAllocationHistories 还款分配历史
     * @param accountOverflowPayFlag     当前账户溢缴款标志
     * @param overflowPaymentBalance     溢缴款标志
     */
    public void netOutProcess(RecordedMidBO recordedMidBO,
                              List<PaymentAllocationHistoryDTO> paymentAllocationHistories,
                              int accountOverflowPayFlag,
                              BigDecimal overflowPaymentBalance) {
        int adjustSeq = 0;
        //交易账户有溢缴款，优先用溢缴款去冲抵
        List<AccountBalanceInfoDTO> accountBalanceListForLimitAll = new ArrayList<>();
        AccountBalanceInfoDTO changeBalanceDTOForLimitUpdateOverflow = new AccountBalanceInfoDTO();

        if (accountOverflowPayFlag != 0) {
            AccountManagementInfoDTO accountManagementInfo = recordedMidBO.accountBO.getAccountManagementInfo();
            String financeStatus = accountManagementInfo.getFinanceStatus();
            int financeFlag;
            if (TransactionConstants.NON_NORMAL_FINANCE_STATUS.equals(financeStatus)) {
                financeFlag = 0;
            } else {
                financeFlag = 1;
            }
            PaymentAllocatedResDTO paramPmtAllocatedDefinitionConfig =
                    getParameterConfig(recordedMidBO, accountManagementInfo, financeFlag);
            checkParameter(paramPmtAllocatedDefinitionConfig);

            AccountBalanceInfo accountBalanceInfo = txnRecordedCacheAccountBalanceService.selectOv999(accountManagementInfo.getAccountManagementId());

            LocalDate lastStatementDate = accountManagementInfo.getLastStatementDate();
            List<PaymentAllocationAndBalanceBO> paymentAllocationAndBalanceBOList = setPriority(recordedMidBO
                    , paramPmtAllocatedDefinitionConfig, lastStatementDate, paymentAllocationHistories);
            Collections.sort(paymentAllocationAndBalanceBOList);
            // 写冲减还款历史
            BigDecimal remainderAmount = overflowPaymentBalance;
            BigDecimal adjustAmount;
            //循环1。用溢缴款冲减

            BeanMapping.copy(accountBalanceInfo,changeBalanceDTOForLimitUpdateOverflow);
            changeBalanceDTOForLimitUpdateOverflow.setBalance(overflowPaymentBalance);
            accountBalanceListForLimitAll.add(changeBalanceDTOForLimitUpdateOverflow);
            LimitControlUnitDTO limitControlUnitBO =searchLimitControl(recordedMidBO.recorded.getLimitControlUnits(),changeBalanceDTOForLimitUpdateOverflow.getTransactionTypeCode());
            changeBalanceDTOForLimitUpdateOverflow.setLimitUnitCode(limitControlUnitBO.getLimitUnitCode());
            changeBalanceDTOForLimitUpdateOverflow.setLimitUnitVersion(limitControlUnitBO.getLimitUnitVersion());
            for (PaymentAllocationAndBalanceBO allocationAndBalanceBO : paymentAllocationAndBalanceBOList) {
                PaymentAllocationHistoryDTO paymentAllocationHistory = allocationAndBalanceBO.getPaymentAllocationHistory();
                if (remainderAmount.compareTo(paymentAllocationHistory.getAllocatedAmount()) >= 0) {
                    remainderAmount = remainderAmount.subtract(paymentAllocationHistory.getAllocatedAmount());
                    paymentAllocationHistory.setAllocatedAmount(BigDecimal.ZERO);
                    adjustAmount = paymentAllocationHistory.getAllocatedAmount();
                    addPaymentAllocationHistoryWithOverFlow(paymentAllocationHistory, adjustSeq++, recordedMidBO,
                            adjustAmount, accountBalanceInfo);
                } else if (!remainderAmount.equals(BigDecimal.ZERO)) {
                    adjustAmount = remainderAmount;
                    paymentAllocationHistory.setAllocatedAmount(paymentAllocationHistory.getAllocatedAmount()
                            .subtract(adjustAmount));
                    addPaymentAllocationHistoryWithOverFlow(paymentAllocationHistory, adjustSeq++, recordedMidBO,
                            adjustAmount, accountBalanceInfo);
                    remainderAmount = BigDecimal.ZERO;
                }
            }

        }

        //账户无溢缴款或处理完毕，进入循环2
        List<AccountBalanceInfoDTO> accountBalanceListForLimit2 =foreachPaymentAllocationHistoryWithNoOverflow(recordedMidBO, paymentAllocationHistories, adjustSeq);
        accountBalanceListForLimitAll.addAll(accountBalanceListForLimit2);
        outerLimitInit.paymentReductionBalanceChangeInit(recordedMidBO,accountBalanceListForLimitAll);
    }

    /**
     * 循环处理溢缴款不存在或溢缴款为零的还款分配记录
     *
     * @param recordedMidBO 接口参数
     * @param adjustSeq  调整顺序号
     * @return 需要更新的额度信息
     */
    private List<AccountBalanceInfoDTO> foreachPaymentAllocationHistoryWithNoOverflow(RecordedMidBO recordedMidBO,
                                                                                      List<PaymentAllocationHistoryDTO> paymentAllocationHistories,
                                                                                      int adjustSeq) {
        List<AccountBalanceInfoDTO> accountBalanceInfoDtos = new ArrayList<>();
        for (PaymentAllocationHistoryDTO paymentAllocationHistory : paymentAllocationHistories) {
            AccountBalanceInfoDTO accountBalanceInfoDTO = paymentAllocationAmountGtZero(recordedMidBO, paymentAllocationHistory, adjustSeq++);
            if(accountBalanceInfoDTO != null){
                accountBalanceInfoDtos.add(accountBalanceInfoDTO);
            }
        }
        return accountBalanceInfoDtos;
        //组合外部额度接口

    }

    /**
     * 处理后，还款分配流水大于零的处理
     * 因为还款分配时调用交易路由规则查到的额度管控单元筛除了分配金额为0的额度管控单元 还款还原需要用到被筛除的额度管控单元
     * 所以重新调用路由规则获取额度管控单元
     *
     * @param recordedMidBO               接口参数
     * @param paymentAllocationHistory 还款分配流水
     */
    private AccountBalanceInfoDTO paymentAllocationAmountGtZero(RecordedMidBO recordedMidBO,
                                                                PaymentAllocationHistoryDTO paymentAllocationHistory,
                                                                int adjustSeq) {
        if (paymentAllocationHistory.getAllocatedAmount().compareTo(BigDecimal.ZERO) > 0) {
            AccountBalanceInfoDTO accountBalanceInfoNew;
            String limitUnitCode=null;
            String limitUnitVersion=null;
	        AccountBalanceInfo accountBalanceInfo = txnRecordedCacheAccountBalanceService.selectByPrimaryKey(paymentAllocationHistory.getTransactionBalanceId());
	        if (isOverflow(paymentAllocationHistory.getTransactionBalanceType())) {
                //溢缴款交易账户
                String transactionTypeCode = gainTransactionTypeCode(recordedMidBO);
	            accountBalanceInfoNew = buildAccountBalanceInfoWithOverflow(recordedMidBO, paymentAllocationHistory,
                        transactionTypeCode);
                logger.info("Calling transaction routing rule allocation");
                //额度分配金额为空，调用交易路由规则、额度接口获取分配的金额
                List<LimitControlUnitDTO> limitControlUnits = new ArrayList<>();
                boolean b = limitAmountAllocation(recordedMidBO, limitControlUnits);
                if(b && CollectionUtils.isNotEmpty(limitControlUnits)){
                    LimitControlUnitDTO limitControlUnitBO =searchLimitControl(limitControlUnits,transactionTypeCode);
                    limitUnitCode = limitControlUnitBO.getLimitUnitCode();
                    limitUnitVersion = limitControlUnitBO.getLimitUnitVersion();
                }

            } else {
	            //非溢缴款交易账户
                accountBalanceInfoNew = buildAccountBalanceInfoWithNoOverflow(paymentAllocationHistory, accountBalanceInfo, recordedMidBO);
                limitUnitCode = accountBalanceInfo.getLimitUnitCode();
                limitUnitVersion = accountBalanceInfo.getLimitUnitVersion();
            }

	        //还款还原利息回算
            String interestAccrualTableId = accountBalanceInfo.getInterestAccrualTableId();
            if (StringUtils.isNotEmpty(interestAccrualTableId)) {
                InterestBearingDTO interestBearingDTO = interestBearingService.findByOrgAndTableId(accountBalanceInfo.getOrganizationNumber(), interestAccrualTableId);
                if (interestBearingDTO.getInterestBackdateOption().equals(1)) {
                    logger.info("Interest backdate calculation - repayment reduction interest backdate flag: [{}]", interestBearingDTO.getInterestBackdateOption());
                    //回算利率 = 根据新交易账户中的利率参数，计算后的日利率
                    BigDecimal interestRateDay = null;
                    InterestBearingDTO newInterestBearingDTO = null;
	                if (StringUtils.isNotBlank(accountBalanceInfo.getInterestAccrualTableId())) {
                        newInterestBearingDTO = interestBearingService.findByOrgAndTableId(accountBalanceInfo.getOrganizationNumber(), accountBalanceInfoNew.getInterestAccrualTableId());
                        if (newInterestBearingDTO == null) {
                            logger.error("Interest calculation parameters not found");
                            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
                        }
                        logger.info("Current interest calculation parameters: [{}]", newInterestBearingDTO.getInterestType());
	                    interestRateDay = interestAccureCalcService.
                                calcDailyInterestRate(newInterestBearingDTO, null);
                    }
                    
                    logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", OrgNumberUtils.getOrg());
                    OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
                    logger.info("OrganizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "not found");
                    LocalDate transactionDate = paymentAllocationHistory.getTransactionDate();
                    LocalDate accruedThruDay = organizationInfo.getAccruedThruDay();
                    int daySums = Integer.parseInt(accruedThruDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"))) - Integer.parseInt(transactionDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))) + 1;
                    //回算天数 = MIN【（机构的当前累计日期 - 还款分配流水表中的交易日期 + 1），还款还原利息回算天数参数值】
                    long numberOfDays;
                    if (null == interestBearingDTO.getInterestBackdateMaxDays() || interestBearingDTO.getInterestBackdateMaxDays().equals(0)){
                        numberOfDays = daySums;
                    }else {
                        numberOfDays = Math.min(daySums, interestBearingDTO.getInterestBackdateMaxDays());
                    }

                    //回算后累积利息 = 新交易账户的累积利息 + 回算本金 * 回算利率 * 回算天数
                    BigDecimal previousInterest = accountBalanceInfoNew.getBalance().multiply(BigDecimal.valueOf(numberOfDays)).multiply(interestRateDay);
                    BigDecimal accrueInterestNew = accountBalanceInfoNew.getAccrueInterest().add(previousInterest);

                    //回算后利息累积日期 = 机构的当前累计日期
                    accountBalanceInfoNew.setInterestAccrueDate(accruedThruDay);
                    accountBalanceInfoNew.setAccrueInterest(accrueInterestNew);
                    InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
                    interestLogHistoryDto.setCurrentInterestRate(accountBalanceInfo.getCurrentEffectInterestRate());
                    interestLogHistoryDto.setAccrueInterestBeforeUpdate(accountBalanceInfoNew.getBalance());
                    interestLogHistoryDto.setAccrueInterestAfterUpdate(accrueInterestNew);
                    interestLogHistoryDto.setCurrentAccrueDays(numberOfDays);
                    interestLogHistoryDto.setCurrentAccrueBalance(accountBalanceInfoNew.getBalance());
                    interestLogHistoryDto.setProssingIndicator(InterestProssingIndicatorEnum.B.getCode());
					if (numberOfDays != 0){
						//记录利息历史
						AccountBalanceInfoDTO accountBalanceInfoDTO = BeanMapping.copy(accountBalanceInfoNew, AccountBalanceInfoDTO.class);
						createInterestLogHistory(accountBalanceInfoDTO, recordedMidBO.accountBO.getAccountManagementInfo(), interestLogHistoryDto);
					}
                }
            }

            //管控单元版本
            accountBalanceInfoNew.setLimitUnitCode(limitUnitCode);
            accountBalanceInfoNew.setLimitUnitVersion(limitUnitVersion);

            addPaymentAllocationHistory(recordedMidBO, accountBalanceInfoNew,
                    paymentAllocationHistory.getAllocatedAmount(), adjustSeq);
            //利息月汇总，原始交易id
            accountBalanceInfoNew.setOriginalTxnBalanceId(accountBalanceInfoNew.getTransactionBalanceId());
            //固定利率标识（有两个值：0-利率固定和 1-利率可变）
            accountBalanceInfoNew.setInterestRateFlag(InterestRateFlagEnum.VARIABLE.getCode());
            logger.info("Calling txnRecordedCacheAccountBalanceService.insert: accountBalanceInfoNew={}", accountBalanceInfoNew);
            txnRecordedCacheAccountBalanceService.insert(BeanMapping.copy(accountBalanceInfoNew, AccountBalanceInfo.class));
            logger.info("TxnRecordedCacheAccountBalanceService.insert completed");
            //余额变动会计流水
            logger.info("Calling glAmsService.buildGlAms: recordedMidBO={}, accountBalanceInfoNew={}, balance={}", recordedMidBO, accountBalanceInfoNew, accountBalanceInfoNew.getBalance());
            AccountantGlamsDTO tAmsGlams = glAmsService.buildGlAms(recordedMidBO, accountBalanceInfoNew, accountBalanceInfoNew.getBalance());
            logger.info("GlAmsService.buildGlAms completed: tAmsGlams={}", tAmsGlams);
            logger.info("Calling txnRecordedAccountCacheService.saveGlAms: tAmsGlams={}", tAmsGlams);
            txnRecordedAccountCacheService.saveGlAms(tAmsGlams);
            logger.info("TxnRecordedAccountCacheService.saveGlAms completed");
            //统计账户的更新
            updateStaticInfo(accountBalanceInfoNew, recordedMidBO);
            return accountBalanceInfoNew;
        }
        return null;
    }
    private LimitControlUnitDTO searchLimitControl(List<LimitControlUnitDTO> limitControlUnitBOList,String transactionTypeCode){
        return limitControlUnitBOList.stream().filter(e->transactionTypeCode.equals(e.getTransactionType()) ).findFirst().orElse(limitControlUnitBOList.get(0));
    }

    /**
     * 额度的金额分配
     * @param recordedMidBO RecordedBO
     * @param limitControlUnits List<LimitControlUnitBO>
     */
    private boolean limitAmountAllocation(RecordedMidBO recordedMidBO, List<LimitControlUnitDTO> limitControlUnits) {
        AccountManagementInfoDTO accountManagementInfo = recordedMidBO.accountBO.getAccountManagementInfo();
        //调用交易路由规则
        TransactionCtrlUnitDTO transactionCtrlUnitDTO = transactionRoutingRuleHandle.transactionRoutingRule(recordedMidBO);
        List<TransactionCtrlUnitDebitDTO> transactionCtrlUnitDebits = transactionCtrlUnitDTO.getTransactionCtrlUnitDebits();
        if(transactionCtrlUnitDebits == null){
            logger.error("Debit transaction but matched routing rule has empty limit control units, program may be interrupted! Please check routing rule configuration");
        }
        //额度预占用
        LimitPreOccupiedReqDTO preOccupiedDTO = new LimitPreOccupiedReqDTO();
        preOccupiedDTO.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
        //账户币种
        preOccupiedDTO.setAccountCurrency(accountManagementInfo.getCurrency());
        //交易金额
        preOccupiedDTO.setTransactionAmount(recordedMidBO.recorded.getTxnBillingAmount());
        //管理账户ID
        preOccupiedDTO.setAccountManagementId(accountManagementInfo.getAccountManagementId());
        //账产品编号
        preOccupiedDTO.setAccountProductCode(accountManagementInfo.getProductNumber());
        //借贷方向
        preOccupiedDTO.setDebitCreditIndicator(recordedMidBO.transactionCodeResDTO.getDebitCreditIndicator());
        //客户号
        preOccupiedDTO.setCustomerId(accountManagementInfo.getCustomerId());
        List<LimitCtrlUnitDTO> ctrlUnitDTOS = new ArrayList<>();
        for (TransactionCtrlUnitDebitDTO transactionCtrlUnitDebit : transactionCtrlUnitDebits) {
            LimitCtrlUnitDTO limitCtrlUnitDTO = new LimitCtrlUnitDTO();
            limitCtrlUnitDTO.setLimitUnitCode(transactionCtrlUnitDebit.getLimitCtrlUnitId());
            //查询管控单元编号
            LimitUnitDefi limitUnitDefi = limitUnitDefiMapper.selectByUnitCode(transactionCtrlUnitDebit.getLimitCtrlUnitId(), recordedMidBO.recorded.getOrganizationNumber());
            limitCtrlUnitDTO.setLimitUnitVersion(String.valueOf(limitUnitDefi.getActiveVersion()));
            limitCtrlUnitDTO.setTransactionType(transactionCtrlUnitDebit.getTransactionTypeCode());
            limitCtrlUnitDTO.setLimitUnitId(limitUnitDefi.getUnitId());
            limitCtrlUnitDTO.setLimitUseOrder(transactionCtrlUnitDebit.getNumber());
            limitCtrlUnitDTO.setCurrency(limitUnitDefi.getCurrency());
            ctrlUnitDTOS.add(limitCtrlUnitDTO);
        }
        //额度计算更新单元
        preOccupiedDTO.setCtrlUnitDTOS(ctrlUnitDTOS);
        //调用额度预占用接口
        logger.warn("Limit pre-occupation interface parameters: {}", JSON.toJSONString(preOccupiedDTO));
        List<CalLimitUnitReqDTO> calLimitUnitReqDTOS = limitPreOccupiedService.limitPreOccupied(preOccupiedDTO);
        logger.warn("Limit pre-occupation returned data: {}", JSON.toJSONString(calLimitUnitReqDTOS));
        if(CollectionUtils.isEmpty(calLimitUnitReqDTOS)){
            logger.error("Limit pre-occupation interface returned empty data");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.LIMIT_PRE_OCCUPE_NULL);
        }
        //如果额度预占用接口返回的notExist标识（该客户下不存在此额度管控单元下的额度节点标识）为false，入账拒绝
        for (CalLimitUnitReqDTO calLimitUnitReqDTO : calLimitUnitReqDTOS){
            if (calLimitUnitReqDTO.isNotExist()){
                //入账拒绝，拒绝原因为：（该客户下不存在此额度管控单元下的额度节点）
                logger.error("Limit node does not exist under this limit control unit for this customer");
                CustAccountBO.threadCustAccountBO.get().getTransRecordResultBO().setResult(1);
                //todo update by 2022/10/10
                CustAccountBO.threadCustAccountBO.get().getTransRecordResultBO().setTransactionException(AnyTxnHttpResponse.fail(AnyTxnTransactionRespCodeEnum.D_LIMIT_TYPE_CODE_NOT_EXIST_REJECT.getCode(), AnyTxnTransactionRespCodeEnum.D_LIMIT_TYPE_CODE_NOT_EXIST_REJECT.getMsg()));
                logger.info("Calling rejectTransactionService.rejectTransaction: reasonCode={}", AnyTxnTransactionRespCodeEnum.D_LIMIT_TYPE_CODE_NOT_EXIST_REJECT.getCode());
                rejectTransactionService.rejectTransaction(recordedMidBO.recorded, AnyTxnTransactionRespCodeEnum.D_LIMIT_TYPE_CODE_NOT_EXIST_REJECT.getCode());
                logger.info("RejectTransactionService.rejectTransaction completed");
                return false;
            }
        }
        for (CalLimitUnitReqDTO calLimitUnitReqDTO : calLimitUnitReqDTOS) {
            if (LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(calLimitUnitReqDTO.getLimitUnitCode())) {
                continue;
            }
            LimitControlUnitDTO limitControlUnit = new LimitControlUnitDTO();
            limitControlUnit.setLimitUnitCode(calLimitUnitReqDTO.getLimitUnitCode());
            limitControlUnit.setLimitUnitVersion(calLimitUnitReqDTO.getLimitUnitVersion());
            limitControlUnit.setTransactionType(calLimitUnitReqDTO.getTransactionType());
            limitControlUnit.setAmount(calLimitUnitReqDTO.getOverdrawAmount());
            limitControlUnit.setLimitUseOrder(calLimitUnitReqDTO.getLimitUseOrder());
            limitControlUnits.add(limitControlUnit);
            //查询交易类型参数
            buildTransactionType(recordedMidBO.transactionTypeResMap, calLimitUnitReqDTO.getTransactionType(),accountManagementInfo.getOrganizationNumber());
        }
        return true;
    }

    /**
     * 加载交易类型
     *
     * @param transTypeResMap Map<String,TransactionTypeResDTO>
     * @param transType String
     * @param organizationNumber String
     *
     */
    private void buildTransactionType(Map<String,TransactionTypeResDTO> transTypeResMap, String transType, String organizationNumber) {
        TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(organizationNumber, transType);
        if (transactionType == null) {
            if (logger.isDebugEnabled()) {
                logger.debug("Failed to get transaction type, transaction rejected! Organization: {}, Transaction type: {}", organizationNumber,transType);
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TRANSACTION_TYPE_REJECT);
        }
        if(null == transTypeResMap){
            transTypeResMap = new HashMap<>();
        }
        transTypeResMap.put(transType,transactionType);
    }

    /**
     * 记录利息历史
     * @param accountBalanceInfo  交易账户
     * @param accountManagementInfo  管理账户
     * @param interestLogHistoryDto  利息更新流水
     */
    private void createInterestLogHistory(AccountBalanceInfoDTO accountBalanceInfo, AccountManagementInfoDTO accountManagementInfo, InterestLogHistoryDTO interestLogHistoryDto) {
        //赋值
        createInterestLogHistoryCommonProperty(accountBalanceInfo, accountManagementInfo, interestLogHistoryDto);
        try {
            txnRecordedAccountCacheService.interestLogHistoryInsertSelective(BeanMapping.copy(interestLogHistoryDto, InterestLogHistory.class));
        } catch (Exception e) {
            logger.error("Failed to call [{}] insert into database table [{}], error message [{}]",
                    "createCardBasicInfo", "INTEREST_LOG_HISTORY", e.getMessage());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR, e);
        }
    }

    private void createInterestLogHistoryCommonProperty(AccountBalanceInfoDTO accountBalanceInfo, AccountManagementInfoDTO accountManagementInfo,
                                                        InterestLogHistoryDTO interestLogHistoryDto) {
        String interestLogId = sequenceIdGen.generateId(TenantUtils.getTenantId());
        String mid = accountBalanceInfo.getAccountManagementId();
        interestLogHistoryDto.setInterestLogId(interestLogId);
        interestLogHistoryDto.setAccountManagementId(accountBalanceInfo.getAccountManagementId());
        interestLogHistoryDto.setOrganizationNumber(accountBalanceInfo.getOrganizationNumber());
        interestLogHistoryDto.setCustomerId(accountManagementInfo.getCustomerId());
        interestLogHistoryDto.setTransactionBalanceId(accountBalanceInfo.getTransactionBalanceId());
        interestLogHistoryDto.setTransactionTypeCode(accountBalanceInfo.getTransactionTypeCode());
        interestLogHistoryDto.setAccrueDate(accountBalanceInfo.getInterestAccrueDate());
        interestLogHistoryDto.setCurrentInterestTableId(accountBalanceInfo.getInterestAccrualTableId());
        interestLogHistoryDto.setPartitionKey(PartitionKeyUtils.partitionKey(accountManagementInfo.getCustomerId()));
        interestLogHistoryDto.setCreateTime(LocalDateTime.now());
        interestLogHistoryDto.setUpdateTime(LocalDateTime.now());
        interestLogHistoryDto.setUpdateBy("admin");
        interestLogHistoryDto.setVersionNumber(1L);
    }


    /**
     * 新增还款分配记录
     *
     * @param recordedMidBO         接口数据
     * @param accountBalanceInfo 交易级账户
     */
    private void addPaymentAllocationHistory(RecordedMidBO recordedMidBO,
                                             AccountBalanceInfoDTO accountBalanceInfo,
                                             BigDecimal adjustAmount,
                                             int adjustSeq) {
        PaymentAllocationHistory paymentAllocationHistory = new PaymentAllocationHistory();
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        String mid = recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId();
        int partition = TransPartitionKeyHelper.getPartitionKeyInt(mid);
        paymentAllocationHistory.setPaymentAllocationId(id);
        paymentAllocationHistory.setGlobalFlowNumber(recordedMidBO.recorded.getTxnGlobalFlowNumber());
        paymentAllocationHistory.setOriginalGlobalFlowNumber(recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber());
        paymentAllocationHistory.setAllocationSequenceNumber((long) adjustSeq);
        paymentAllocationHistory.setAccountManagementId(recordedMidBO.recorded.getTxnAccountManageId());
        paymentAllocationHistory.setOrganizationNumber(
                recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber());
        paymentAllocationHistory.setCustomerId(recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId());
        paymentAllocationHistory.setTransactionCode(recordedMidBO.recorded.getTxnTransactionCode());
        paymentAllocationHistory.setTransactionCode(recordedMidBO.recorded.getTxnTransactionCode());
        paymentAllocationHistory.setTransactionDate(recordedMidBO.recorded.getTxnTransactionDate().toLocalDate());
        paymentAllocationHistory.setBillingAmount(recordedMidBO.recorded.getTxnBillingAmount());
        paymentAllocationHistory.setBillingDate(recordedMidBO.recorded.getTxnBillingDate());
        paymentAllocationHistory.setBillingCurrency(recordedMidBO.recorded.getTxnBillingCurrency());
        paymentAllocationHistory.setTransactionBalanceId(accountBalanceInfo.getTransactionBalanceId());
        paymentAllocationHistory.setTransactionBalanceType(accountBalanceInfo.getTransactionTypeCode());
        paymentAllocationHistory.setTransactionBalanceId(accountBalanceInfo.getTransactionBalanceId());
        paymentAllocationHistory.setTransactionBalanceType(accountBalanceInfo.getTransactionTypeCode());
        paymentAllocationHistory.setAllocatedAmount(adjustAmount);
        paymentAllocationHistory.setInterestParmId(accountBalanceInfo.getInterestAccrualTableId());
        paymentAllocationHistory.setParentTrasactionBalanceId("");
        paymentAllocationHistory.setInstallmentOrderNumber("");
        paymentAllocationHistory.setCreditLimitNodeId("");
        paymentAllocationHistory.setCreateDate(accountBalanceInfo.getCreateDate());
        paymentAllocationHistory.setStatus(accountBalanceInfo.getStatus());
        paymentAllocationHistory.setInterestIndicator(accountBalanceInfo.getInterestIndicator());
        paymentAllocationHistory.setAllocationType(AllocationTypeEnum.DECREASE.getCode());
        paymentAllocationHistory.setPartitionKey(partition);
        paymentAllocationHistory.setCreateTime(LocalDateTime.now());
        paymentAllocationHistory.setUpdateTime(LocalDateTime.now());
        paymentAllocationHistory.setUpdateBy(TransactionConstants.DEFAULT_USER);
        paymentAllocationHistory.setVersionNumber(1L);
        paymentAllocationHistoryMapper.insert(paymentAllocationHistory);
    }

    /**
     * 获取交易类型码
     *
     * @param recordedMidBO 接口数据
     * @return string
     */
    private String gainTransactionTypeCode(RecordedMidBO recordedMidBO) {
        TransactionTypeResDTO parameterConfig = transactionTypeService.findByOrgAndFinanceFlag(
                recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber(), "1");
        checkParameter(parameterConfig);
        return parameterConfig.getTransactionTypeCode();
    }


    /**
     * 交易类型是否为溢缴款类型
     *
     * @param transactionType 交易类型
     * @return boolean
     */
    private boolean isOverflow(String transactionType) {
        return transactionType.equals(TransactionConstants.TRANSACTION_TYPE_OVERFLOW);
    }
    /*private boolean isOverflow(String orgNum,String transactionType) {
        TransactionTypeResDTO transactionTypeResDTO = transactionTypeService.findTransactionType(orgNum,transactionType);
        return Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(),transactionTypeResDTO.getBalanceLoanDirection());
    }*/


    /**
     * 读取还款分配定义表
     *
     * @param recordedMidBO            接口参数
     * @param accountManagementInfo 账户管理Id
     * @return PaymentAllocatedResDTO
     */
    private PaymentAllocatedResDTO getParameterConfig(RecordedMidBO recordedMidBO,
                                                      AccountManagementInfoDTO accountManagementInfo,
                                                      int state) {
        logger.info("Calling acctProductMainInfoService.findByOrgAndProductNum: org={}, productNumber={}", recordedMidBO.getOrganizationNumber(), recordedMidBO.getProductInfoResDTO().getProductNumber());
        AcctProductInfoDTO byOrgAndProductNum = acctProductMainInfoService.findByOrgAndProductNum(recordedMidBO.getOrganizationNumber(), recordedMidBO.getProductInfoResDTO().getProductNumber());
        logger.info("AcctProductMainInfoService.findByOrgAndProductNum completed: result={}", byOrgAndProductNum != null ? "found" : "not found");
        if (byOrgAndProductNum == null){
            logger.warn("Account product parameters not found for organization: {}, product number: {}", recordedMidBO.getOrganizationNumber(), recordedMidBO.getProductInfoResDTO().getProductNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.REPAYMENT_RULE_NOT_EXIST);
        }
        String attribute = byOrgAndProductNum.getAcctProductMainInfoResDTO().getAttribute();
        //此处暂定时读取产品上的，后续会修改为编辑规则因子，调用规则引擎，或者还款分配定义表ID）
        //此处已修改为调用规则因子 2020-08-05
        //todo 借贷分离，还款还原取到交易类型，待业务确认，先赋值null
        String paymentSequenceTableId = RuleExecute.executeRule(recordedMidBO,TransactionConstants.RULE_TYPE_REPAY_ORDER,null,attribute);
        /*if (state == 1) {
            paymentSequenceTableId =
                    recordedBO.productInfoResDTO.getDelinqPymtSequenceTableId();
        } else {
            paymentSequenceTableId =
                    recordedBO.productInfoResDTO.getNormalPymtSequenceTableId();

        }*/


        return paymentAllocatedService.findByOrgAndTableId(
                accountManagementInfo.getOrganizationNumber(),
                paymentSequenceTableId);
    }

    /**
     * 新增一条还款分配记录
     *
     * @param paymentAllocationHistory 还款分配记录
     * @param allocationSequenceNumber 还款序号
     * @param recordedMidBO               接口信息
     */
    private void addPaymentAllocationHistoryWithOverFlow(PaymentAllocationHistoryDTO paymentAllocationHistory,
                                                         int allocationSequenceNumber, RecordedMidBO recordedMidBO,
                                                         BigDecimal adjustAmount, AccountBalanceInfo accountBalanceInfo) {
        PaymentAllocationHistory newPaymentAllocationHistory = new PaymentAllocationHistory();
        newPaymentAllocationHistory.setAccountManagementId(recordedMidBO.recorded.getTxnAccountManageId());
        newPaymentAllocationHistory.setOrganizationNumber(
                recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber());
        newPaymentAllocationHistory.setCustomerId(recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId());
        //自动生成一个新的id
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        String mid = recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId();
        int partition = TransPartitionKeyHelper.getPartitionKeyInt(mid);
        newPaymentAllocationHistory.setPaymentAllocationId(id);
        newPaymentAllocationHistory.setOriginalGlobalFlowNumber(recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber());
        paymentAllocationHistory.setGlobalFlowNumber(recordedMidBO.recorded.getTxnGlobalFlowNumber());
        newPaymentAllocationHistory.setAllocationSequenceNumber((long) allocationSequenceNumber);
        newPaymentAllocationHistory.setReversalIndicator("N");
        newPaymentAllocationHistory.setPartitionKey(partition);
        newPaymentAllocationHistory.setCreateTime(LocalDateTime.now());
        newPaymentAllocationHistory.setUpdateTime(LocalDateTime.now());
        newPaymentAllocationHistory.setVersionNumber(2L);
        newPaymentAllocationHistory.setUpdateBy(TransactionConstants.DEFAULT_USER);
        newPaymentAllocationHistory.setTransactionCode(recordedMidBO.recorded.getTxnTransactionCode());
        newPaymentAllocationHistory.setTransactionDate(recordedMidBO.recorded.getTxnTransactionDate().toLocalDate());
        newPaymentAllocationHistory.setBillingAmount(recordedMidBO.recorded.getTxnBillingAmount());
        newPaymentAllocationHistory.setBillingDate(recordedMidBO.recorded.getTxnBillingDate());
        newPaymentAllocationHistory.setBillingCurrency(recordedMidBO.recorded.getTxnBillingCurrency());
        newPaymentAllocationHistory.setTransactionBalanceId(accountBalanceInfo.getTransactionBalanceId());
        newPaymentAllocationHistory.setTransactionBalanceType(accountBalanceInfo.getTransactionTypeCode());
        newPaymentAllocationHistory.setAllocatedAmount(adjustAmount);
        newPaymentAllocationHistory.setInterestParmId(accountBalanceInfo.getInterestAccrualTableId());
        newPaymentAllocationHistory.setParentTrasactionBalanceId("");
        newPaymentAllocationHistory.setInstallmentOrderNumber("");
        newPaymentAllocationHistory.setCreditLimitNodeId("");
        newPaymentAllocationHistory.setAllocationType(AllocationTypeEnum.DECREASE.getCode());
        newPaymentAllocationHistory.setCreateDate(accountBalanceInfo.getCreateDate());
        newPaymentAllocationHistory.setStatus(accountBalanceInfo.getStatus());
        newPaymentAllocationHistory.setInterestIndicator(accountBalanceInfo.getInterestIndicator());
        txnRecordedAccountCacheService.paymentAllocationHistoryInsertSelective(newPaymentAllocationHistory);
    }


    /**
     * 生成一条溢缴款类型交易记录
     *
     * @param recordedMidBO               接口数据
     * @param paymentAllocationHistory 还款分配记录
     * @param transactionTypeCode      交易类型
     */
    private AccountBalanceInfoDTO buildAccountBalanceInfoWithOverflow(RecordedMidBO recordedMidBO,
                                                                      PaymentAllocationHistoryDTO paymentAllocationHistory,
                                                                      String transactionTypeCode) {
        AccountBalanceInfoDTO accountBalanceInfo = new AccountBalanceInfoDTO();
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        accountBalanceInfo.setTransactionBalanceId(id);
        accountBalanceInfo.setAccountManagementId(paymentAllocationHistory.getAccountManagementId());
        accountBalanceInfo.setTransactionTypeCode(transactionTypeCode);
        accountBalanceInfo.setOrganizationNumber(paymentAllocationHistory.getOrganizationNumber());
        accountBalanceInfo.setCurrency(paymentAllocationHistory.getBillingCurrency());
        accountBalanceInfo.setCreateMethod("1");
        accountBalanceInfo.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
        accountBalanceInfo.setAmount(paymentAllocationHistory.getAllocatedAmount());
        accountBalanceInfo.setBalance(paymentAllocationHistory.getAllocatedAmount());
        accountBalanceInfo.setAccrueInterest(BigDecimal.ZERO);
        accountBalanceInfo.setAggregateAmount(BigDecimal.ZERO);
        accountBalanceInfo.setInterestAccrueDate(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        accountBalanceInfo.setLastInterestAccrueDate(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        accountBalanceInfo.setLastInterestBillDate(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        accountBalanceInfo.setLastCycleBillInterest(BigDecimal.ZERO);
        accountBalanceInfo.setInterestStartDate(recordedMidBO.recorded.getTxnBillingDate());
        accountBalanceInfo.setInterestIndicator("0");
        accountBalanceInfo.setTransactionFeeTableId("");
        //通过固定消费类型查询余额定价参数表获取最低还款比例id
        logger.info("Calling parmBalancePricingTableService.findByIndex: org={}, tableId={}, transactionTypeCode={}", paymentAllocationHistory.getOrganizationNumber(), recordedMidBO.productInfoResDTO.getBalancePricingTableId(), "40000");
        ParmBalancePricingTableResDTO balancePricingTableId = parmBalancePricingTableService.findByIndex(paymentAllocationHistory.getOrganizationNumber(),
                recordedMidBO.productInfoResDTO.getBalancePricingTableId(), "40000");
        logger.info("ParmBalancePricingTableService.findByIndex completed: result={}", balancePricingTableId != null ? "found" : "not found");
        if (Objects.isNull(balancePricingTableId)) {
            logger.error("Balance pricing parameters illegal, balance pricing parameters not found for organization: {}, BalancePricingTableId: {}, transactionTypeCode: {}",
                    paymentAllocationHistory.getOrganizationNumber(), recordedMidBO.productInfoResDTO.getBalancePricingTableId(), "40000");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.ILLEGAL_NOT_EXIST);
        }
        accountBalanceInfo.setTransactionMinimumPaymentId(balancePricingTableId.getMinimumPaymentTableId());
        accountBalanceInfo.setInterestAccrualTableId(balancePricingTableId.getInterestBearingTableId());
        accountBalanceInfo.setInterestSettleTableId(balancePricingTableId.getInterestSettlementTableId());
        accountBalanceInfo.setBalanceNetoutTableId(balancePricingTableId.getBalanceInwardTransferTableId());
        logger.info("Calling interestBearingService.findByOrgAndTableId: org={}, tableId={}", paymentAllocationHistory.getOrganizationNumber(), accountBalanceInfo.getInterestAccrualTableId());
        InterestBearingDTO interestBearingDTO = interestBearingService.findByOrgAndTableId(paymentAllocationHistory.getOrganizationNumber(), accountBalanceInfo.getInterestAccrualTableId());
        logger.info("InterestBearingService.findByOrgAndTableId completed: result={}", interestBearingDTO != null ? "found" : "not found");
        logger.info("Calling interestAccureCalcService.calcDailyInterestRate");
        BigDecimal interestRate = interestAccureCalcService.calcDailyInterestRate(interestBearingDTO, null);
        logger.info("InterestAccureCalcService.calcDailyInterestRate completed: interestRate={}", interestRate);
        accountBalanceInfo.setCurrentEffectInterestRate(interestRate);
        accountBalanceInfo.setParentTransactionBalanceId("");
        accountBalanceInfo.setCreditLimitNodeId("");
        accountBalanceInfo.setInstallmentOrderNumber("");
        accountBalanceInfo.setStatus("1");
        //未出账单
        accountBalanceInfo.setStatementIndicator("0");
        //非超长免息期
        accountBalanceInfo.setLargeGraceIndicator("0");
        //实际出账单日
        LocalDate actualStatementDate = calculateActualStatementDate(recordedMidBO.organizationInfoResDTO.getNextProcessingDay(),
                recordedMidBO.accountBO.getAccountManagementInfo().getCycleDay());
        accountBalanceInfo.setActualStatementDate(actualStatementDate);

        //实际免息结息日
        if (  balancePricingTableId.getInterestSettlementTableId()==null || "".equals(balancePricingTableId.getInterestSettlementTableId())) {
            accountBalanceInfo.setActualInterestBillingDate(actualStatementDate);
        } else {
            logger.info("Calling interestSettlementService.findByOrgAndTableId: org={}, tableId={}", paymentAllocationHistory.getOrganizationNumber(), balancePricingTableId.getInterestSettlementTableId());
            InterestSettlementDTO interestSettlementDTO = interestSettlementService.findByOrgAndTableId(paymentAllocationHistory.getOrganizationNumber(),
                    balancePricingTableId.getInterestSettlementTableId());
            logger.info("InterestSettlementService.findByOrgAndTableId completed: result={}", interestSettlementDTO != null ? "found" : "not found");
            if (Objects.isNull(interestSettlementDTO)) {
                //将交易写入拒绝交易表 拒绝原因为：0032（利率参数不存在）
                logger.info("Writing to rejection table, rejection reason: {}", "0032（结息参数不存在）");
                CustAccountBO.threadCustAccountBO.get().getTransRecordResultBO().setResult(1);
                logger.info("Calling rejectTransactionService.rejectTransaction: reasonCode=0032");
                rejectTransactionService.rejectTransaction(recordedMidBO.recorded, "0032");
                logger.info("RejectTransactionService.rejectTransaction completed");
                return null;
            } else {
                /*if ("0".equals(interestSettlementDTO.getGraceOption())) {
                    accountBalanceInfo.setActualInterestBillingDate(actualStatementDate);
                }
                if ("1".equals(interestSettlementDTO.getGraceOption())) {
                    accountBalanceInfo.setActualInterestBillingDate(actualStatementDate.plusMonths(1));
                }*/
                accountBalanceInfo.setActualInterestBillingDate(actualStatementDate);
            }
        }
        accountBalanceInfo.setCustomerId(recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId());
        //利息月汇总，原始交易id,赋值为新创建的交易账户id
        accountBalanceInfo.setOriginalTxnBalanceId(accountBalanceInfo.getTransactionBalanceId());
        accountBalanceInfo.setAbsStatus("N");
        accountBalanceInfo.setPartitionKey(PartitionKeyUtils.IntPartitionKey(accountBalanceInfo.getCustomerId()));
        accountBalanceInfo.setCreateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        accountBalanceInfo.setVersionNumber(1L);

        //TODO
        //资金源Id
        accountBalanceInfo.setFundId("**********");
        //卡产品编号
        accountBalanceInfo.setCardProductNumber("000000");
        //商户编号
        accountBalanceInfo.setMerchantId("**********00000");
        //渠道子Id
        accountBalanceInfo.setSubChannelId("**********");
        //渠道ID
        accountBalanceInfo.setChannelId("**********");

        return accountBalanceInfo;
    }

    /**
     * 会计变更交易账户
     */
    private void glChange(PaymentAllocationHistoryDTO paymentAllocationHistory, AccountBalanceInfoDTO accountBalanceInfo) {
        logger.info("Calling transactionTypeService.findTransactionType: org={}, transactionTypeCode={}", accountBalanceInfo.getOrganizationNumber(), accountBalanceInfo.getTransactionTypeCode());
        TransactionTypeResDTO transactionType =
                transactionTypeService.findTransactionType(accountBalanceInfo.getOrganizationNumber(),
                        accountBalanceInfo.getTransactionTypeCode());
        logger.info("TransactionTypeService.findTransactionType completed: result={}", transactionType != null ? "found" : "not found");
        if (StringUtils.equals(transactionType.getBalType(), "3")) {
            accountBalanceInfo.setInterestIndicator(InterestIndicatorEnum.TAX.getCode());
        } else if (StringUtils.equals(transactionType.getBalType(), "2")) {
            if (StringUtils.equals(paymentAllocationHistory.getInterestIndicator(), "1")) {
                accountBalanceInfo.setInterestIndicator("1");
            } else if (StringUtils.equals(paymentAllocationHistory.getInterestIndicator(), "0")) {
                accountBalanceInfo.setInterestIndicator("0");
            } else {
                accountBalanceInfo.setInterestIndicator("2");
            }
        }

    }

    /**
     * 实际出账单日 （实际出账单日=下一个账单日）
     *
     * @param nextProcessingDay 机构层下一处理日
     * @param cycleDate         管理账户的账单日
     * @return 实际出账单日
     */
    private LocalDate calculateActualStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month = 0;
        int year = 0;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        LocalDate actualStatementDate = LocalDate.of(year, month, cycleDate);
        return actualStatementDate;
    }

    /**
     * 实际免息结息日
     *
     * @param orgNum          机构号
     * @param interestTableId 利率参数表id
     * @return LocalDate      实际免息结息日
     *//*
    private LocalDate actualInterestBillingDate(RecordedBO recordedBO,
                                                String orgNum,
                                                String interestTableId,
                                                LocalDate actualStatementDate) {
        LocalDate actualInterestBillingDate = null;
        InterestResDTO paramInterest = interestService.findByOrgAndTableId(orgNum, interestTableId);
        if (Objects.isNull(paramInterest)) {
            //将交易写入拒绝交易表 拒绝原因为：0032（利率参数不存在）
            log.info("写入拒绝表,拒绝原因{}", "0032（利率参数不存在）");
            rejectTransactionService.rejectTransaction(recordedBO.recorded, AnyTxnTransactionRespCode.D_PARM_INTEREST_NOT_EXIST.getCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_PARM_INTEREST_NOT_EXIST);
        } else {
            if ("0".equals(paramInterest.getGraceOption())) {
                actualInterestBillingDate = actualStatementDate;
            }
            if ("1".equals(paramInterest.getGraceOption())) {
                actualInterestBillingDate = actualStatementDate.plusMonths(1);
            }
        }
        return actualInterestBillingDate;
    }*/

    /**
     * 更新统计信息表
     *
     * @param accountBalanceInfo 交易账户
     * @param recordedMidBO         接口数据
     */
    public void updateStaticInfo(AccountBalanceInfoDTO accountBalanceInfo, RecordedMidBO recordedMidBO) {
        if (accountBalanceInfo == null) {
            logger.error("Original transaction balance account does not exist");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_ACC_BALANCE_NOT_EXIST);
        }
      /*  AccountStatisticsInfo accountStatisticsInfo = accountStatisticsInfoSelfMapper.selectByIdAndType(
                recordedBO.accountBO.getAccountManagementInfo().getAccountManagementId(),
                accountBalanceInfo.getTransactionTypeCode());*/
        AccountStatisticsInfo accountStatisticsInfo = txnRecordedAccountCacheService.accountStatisticsInfoSelectByIdAndType(
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId(), accountBalanceInfo.getTransactionTypeCode());
        if (Objects.isNull(accountStatisticsInfo)) {
            addAnAccountStaticsInfo(recordedMidBO, accountBalanceInfo);
        } else {
            accountStatisticsInfo.setBalance(accountStatisticsInfo.getBalance().add(accountBalanceInfo.getBalance()));
            txnRecordedAccountCacheService.accountStatisticsInfoUpdateByPrimaryKeySelective(accountStatisticsInfo);
        }

    }

    /**
     * 新增一条统计账户信息
     *
     * @param recordedMidBO         接口数据
     * @param accountBalanceInfo 账户余额信息
     */
    private void addAnAccountStaticsInfo(RecordedMidBO recordedMidBO, AccountBalanceInfoDTO accountBalanceInfo) {
        AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
        AccountManagementInfoDTO accountManagementInfo = recordedMidBO.accountBO.getAccountManagementInfo();
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());

        String customId = accountManagementInfo.getCustomerId();
        int partition = TransPartitionKeyHelper.getPartitionKeyInt(customId);
        accountStatisticsInfo.setStatisticsId(id);
        accountStatisticsInfo.setAccountManagementId(accountManagementInfo.getAccountManagementId());
        accountStatisticsInfo.setCustomerId(customId);
        accountStatisticsInfo.setTransactionTypeCode(accountBalanceInfo.getTransactionTypeCode());
        accountStatisticsInfo.setOrganizationNumber(accountBalanceInfo.getOrganizationNumber());
        accountStatisticsInfo.setCurrency(accountManagementInfo.getCurrency());
        accountStatisticsInfo.setCreateDate(recordedMidBO.organizationInfoResDTO.getNextProcessingDay());
        accountStatisticsInfo.setBalance(accountBalanceInfo.getBalance());
        accountStatisticsInfo.setStatementBalance(BigDecimal.ZERO);
        //字段更新
        accountStatisticsInfo.setLastStatementDate(accountManagementInfo.getLastStatementDate());
        accountStatisticsInfo.setLastActivityDate(TransactionConstants.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreditTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setDebitTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setPartitionKey(partition);
        accountStatisticsInfo.setCreateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        accountStatisticsInfo.setVersionNumber(0L);
        txnRecordedAccountCacheService.accountStatisticsInfoInsertSelective(accountStatisticsInfo);
    }

    /**
     * 生成一条非溢缴款的账户交易信息
     *
     * @param paymentAllocationHistory 还款分配记录
     * @param accountBalanceInfo       账户交易信息
     * @param recordedMidBO               接口数据
     */
    private AccountBalanceInfoDTO  buildAccountBalanceInfoWithNoOverflow(PaymentAllocationHistoryDTO paymentAllocationHistory,
                                                                        AccountBalanceInfo accountBalanceInfo,
                                                                        RecordedMidBO recordedMidBO) {
        AccountBalanceInfoDTO accountBalanceInfoNew = BeanMapping.copy(accountBalanceInfo,AccountBalanceInfoDTO.class);
        accountBalanceInfoNew.setTransactionBalanceId(sequenceIdGen.generateId(TenantUtils.getTenantId()));

        //交易账户id、余额：按照还款还原规则赋值
        accountBalanceInfoNew.setOriginalTxnBalanceId(accountBalanceInfoNew.getTransactionBalanceId());
        accountBalanceInfoNew.setBalance(paymentAllocationHistory.getAllocatedAmount());

        accountBalanceInfoNew.setAccountManagementId(accountBalanceInfo.getAccountManagementId());
        accountBalanceInfoNew.setTransactionTypeCode(accountBalanceInfo.getTransactionTypeCode());
        accountBalanceInfoNew.setOrganizationNumber(accountBalanceInfo.getOrganizationNumber());
        accountBalanceInfoNew.setCurrency(accountBalanceInfo.getCurrency());
        //创建方式：赋值为1；
        accountBalanceInfoNew.setCreateMethod("1");

        //创建日期、起息日：赋值为还款还原交易的入账日期
        accountBalanceInfoNew.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
        accountBalanceInfoNew.setInterestStartDate(recordedMidBO.recorded.getTxnBillingDate());

        accountBalanceInfoNew.setAmount(paymentAllocationHistory.getAllocatedAmount());

        //累积利息、利息积数、上个账单日入账利息：赋值为0；
        accountBalanceInfoNew.setAccrueInterest(BigDecimal.ZERO);
        accountBalanceInfoNew.setAggregateAmount(BigDecimal.ZERO);
        accountBalanceInfoNew.setLastCycleBillInterest(BigDecimal.ZERO);

        //利息累积日期: 当前机构的当前累计日期
        logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("OrganizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "not found");
        accountBalanceInfoNew.setInterestAccrueDate(organizationInfo.getAccruedThruDay());
        //上一利息累积日期、上一结息日：赋值为初始值0001-01-01；
        accountBalanceInfoNew.setLastInterestAccrueDate(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        accountBalanceInfoNew.setLastInterestBillDate(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        accountBalanceInfoNew.setInterestAccrualTableId(accountBalanceInfo.getInterestAccrualTableId());

        //通过交易账户的transaction_balance_type查询余额定价参数表获取最低还款比例id
        ParmBalancePricingTableResDTO balancePricingTableId = parmBalancePricingTableService.findByIndex(
                accountBalanceInfo.getOrganizationNumber(),
                recordedMidBO.productInfoResDTO.getBalancePricingTableId(),
                accountBalanceInfo.getTransactionTypeCode());
        if (Objects.isNull(balancePricingTableId)) {
            logger.error("Balance pricing parameters illegal, data not found, orgNum: {}, BalancePricingTableId: {}, TransactionTypeCode: {}", accountBalanceInfo.getOrganizationNumber(),
                    recordedMidBO.productInfoResDTO.getBalancePricingTableId(),
                    accountBalanceInfo.getTransactionTypeCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_BALANCE_PRICING_ILLEGAL);
        }
        accountBalanceInfoNew.setTransactionMinimumPaymentId(balancePricingTableId.getMinimumPaymentTableId());

        //已出账单标志、超长免息期标志：赋值为0；
        accountBalanceInfoNew.setStatementIndicator("0");
        accountBalanceInfoNew.setLargeGraceIndicator("0");

        //TODO
        //实际出账单日、实际免息结息日：基于新交易账户的创建日期，根据计算规则，重新计算（可参考交易入账时的赋值规则）
        // 基于新交易账户的创建日期   ??????
        LocalDate actualStatementDate = calculateActualStatementDate(recordedMidBO.organizationInfoResDTO.getNextProcessingDay(), recordedMidBO.accountBO.getAccountManagementInfo().getCycleDay());
        accountBalanceInfoNew.setActualStatementDate(actualStatementDate);

        //实际免息结息日
        if ( accountBalanceInfo.getInterestSettleTableId()==null  || "".equals(accountBalanceInfo.getInterestSettleTableId())) {
            accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate);
        } else {
            InterestSettlementDTO interestSettleParam = interestSettlementService.findByOrgAndTableId(paymentAllocationHistory.getOrganizationNumber(),
                    accountBalanceInfo.getInterestSettleTableId());
            if (Objects.isNull(interestSettleParam)) {
                //将交易写入拒绝交易表 拒绝原因为：0032（利率参数不存在）
                logger.info("Writing to reject transaction table, reject reason: 0032 (interest parameter does not exist)");
                CustAccountBO.threadCustAccountBO.get().getTransRecordResultBO().setResult(1);
                rejectTransactionService.rejectTransaction(recordedMidBO.recorded, "0032");
                return null;
            } else {
                /*if ("0".equals(interestSettleParam.getGraceOption())) {
                    accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate);
                }
                if ("1".equals(interestSettleParam.getGraceOption())) {
                    accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate.plusMonths(1));
                }*/
                accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate);
            }
        }

        //数据待定
        accountBalanceInfoNew.setAbsStatus("N");
        accountBalanceInfoNew.setParentTransactionBalanceId(null);
        accountBalanceInfoNew.setCreditLimitNodeId(accountBalanceInfo.getCreditLimitNodeId());
        accountBalanceInfoNew.setInstallmentOrderNumber(accountBalanceInfo.getInstallmentOrderNumber());
        accountBalanceInfoNew.setStatus(accountBalanceInfo.getStatus());
        //数据待定
        accountBalanceInfoNew.setCustomerId(recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId());
        accountBalanceInfoNew.setPartitionKey(PartitionKeyUtils.IntPartitionKey(accountBalanceInfoNew.getCustomerId()));
        accountBalanceInfoNew.setCreateTime(LocalDateTime.now());
        accountBalanceInfoNew.setUpdateTime(LocalDateTime.now());
        //数据待定
        accountBalanceInfoNew.setVersionNumber(1L);
        accountBalanceInfoNew.setUpdateBy(TransactionConstants.DEFAULT_USER);
        //资金源Id
        accountBalanceInfoNew.setFundId(accountBalanceInfo.getFundId());
        //卡产品编号
        accountBalanceInfoNew.setCardProductNumber(accountBalanceInfo.getCardProductNumber());
        //商户编号
        accountBalanceInfoNew.setMerchantId(accountBalanceInfo.getMerchantId());
        //渠道子Id
        accountBalanceInfoNew.setSubChannelId(accountBalanceInfo.getSubChannelId());
        //渠道ID
        accountBalanceInfoNew.setChannelId(accountBalanceInfo.getChannelId());


        /*accountBalanceInfoNew.setAccountManagementId(accountBalanceInfo.getAccountManagementId());
        accountBalanceInfoNew.setTransactionTypeCode(accountBalanceInfo.getTransactionTypeCode());
        accountBalanceInfoNew.setOrganizationNumber(accountBalanceInfo.getOrganizationNumber());
        accountBalanceInfoNew.setCurrency(accountBalanceInfo.getCurrency());
        accountBalanceInfoNew.setCreateMethod("1");
        accountBalanceInfoNew.setCreateDate(recordedBO.recorded.getTxnBillingDate());
        accountBalanceInfoNew.setAmount(paymentAllocationHistory.getAllocatedAmount());
        accountBalanceInfoNew.setAccrueInterest(BigDecimal.ZERO);
        accountBalanceInfoNew.setAggregateAmount(BigDecimal.ZERO);
        accountBalanceInfoNew.setInterestAccrueDate(recordedBO.recorded.getTxnBillingDate().plusDays(-1));
        accountBalanceInfoNew.setLastInterestAccrueDate(recordedBO.recorded.getTxnBillingDate().plusDays(-1));
        accountBalanceInfoNew.setInterestStartDate(recordedBO.recorded.getTxnBillingDate());
        accountBalanceInfoNew.setLastInterestBillDate(TransactionConstants.INITIAL_DATE);
        accountBalanceInfoNew.setTransactionInterestTableId(accountBalanceInfo.getTransactionInterestTableId());
        //通过交易账户的transaction_balance_type查询余额定价参数表获取最低还款比例id
        ParmBalancePricingTableResDTO balancePricingTableId = parmBalancePricingTableService.findByIndex(
                accountBalanceInfo.getOrganizationNumber(),
                recordedBO.productInfoResDTO.getBalancePricingTableId(),
                accountBalanceInfo.getTransactionTypeCode());
        if (Objects.isNull(balancePricingTableId)) {
            log.error("余额定价参数非法,数据不存在,orgNum:{},BalancePricingTableId:{},TransactionTypeCode:{}", accountBalanceInfo.getOrganizationNumber(),
                    recordedBO.productInfoResDTO.getBalancePricingTableId(),
                    accountBalanceInfo.getTransactionTypeCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_BALANCE_PRICING_ILLEGAL);
        }
        accountBalanceInfoNew.setTransactionMinimumPaymentId(balancePricingTableId.getMinimumPaymentTableId());

        //未出账单
        accountBalanceInfoNew.setStatementIndicator("0");
        //非超长免息期
        accountBalanceInfoNew.setLargeGraceIndicator("0");
        //实际出账单日
        LocalDate actualStatementDate = calculateActualStatementDate(recordedBO.organizationInfoResDTO.getNextProcessingDay(),
                recordedBO.accountBO.getAccountManagementInfo().getCycleDay());
        accountBalanceInfoNew.setActualStatementDate(actualStatementDate);
        //实际免息结息日
        if ( accountBalanceInfo.getTransactionInterestTableId()==null  || "".equals(accountBalanceInfo.getTransactionInterestTableId())) {
            accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate);
        } else {
            InterestResDTO paramInterest = interestService.findByOrgAndTableId(paymentAllocationHistory.getOrganizationNumber(),
                    accountBalanceInfo.getTransactionInterestTableId());
            if (Objects.isNull(paramInterest)) {
                //将交易写入拒绝交易表 拒绝原因为：0032（利率参数不存在）
                log.info("写入拒绝表,拒绝原因{}", "0032（利率参数不存在）");
                rejectTransactionService.rejectTransaction(recordedBO.recorded, "0032");
                return null;
            } else {
                if ("0".equals(paramInterest.getGraceOption())) {
                    accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate);
                }
                if ("1".equals(paramInterest.getGraceOption())) {
                    accountBalanceInfoNew.setActualInterestBillingDate(actualStatementDate.plusMonths(1));
                }
            }
        }

        //数据待定
        accountBalanceInfoNew.setAbsStatus("N");
        accountBalanceInfoNew.setParentTransactionBalanceId(null);
        accountBalanceInfoNew.setCreditLimitNodeId(accountBalanceInfo.getCreditLimitNodeId());
        accountBalanceInfoNew.setInstallmentOrderNumber(accountBalanceInfo.getInstallmentOrderNumber());
        accountBalanceInfoNew.setStatus(accountBalanceInfo.getStatus());
        //数据待定
        accountBalanceInfoNew.setCustomerId(recordedBO.accountBO.getAccountManagementInfo().getCustomerId());
        accountBalanceInfoNew.setPartitionKey(PartitionKeyUtils.IntPartitionKey(accountBalanceInfoNew.getCustomerId()));
        accountBalanceInfoNew.setCreateTime(LocalDateTime.now());
        accountBalanceInfoNew.setUpdateTime(LocalDateTime.now());
        //数据待定
        accountBalanceInfoNew.setVersionNumber(1L);
        accountBalanceInfoNew.setUpdateBy(TransactionConstants.DEFAULT_USER);
        //资金源Id
        accountBalanceInfoNew.setFundId(accountBalanceInfo.getFundId());
        //卡产品编号
        accountBalanceInfoNew.setCardProductNumber(accountBalanceInfo.getCardProductNumber());
        //商户编号
        accountBalanceInfoNew.setMerchantId(accountBalanceInfo.getMerchantId());
        //渠道子Id
        accountBalanceInfoNew.setSubChannelId(accountBalanceInfo.getSubChannelId());
        //渠道ID
        accountBalanceInfoNew.setChannelId(accountBalanceInfo.getChannelId());*/
        glChange(paymentAllocationHistory, accountBalanceInfoNew);
        return accountBalanceInfoNew;
    }

    /**
     * 设置还款优先级
     *
     * @param recordedMidBO                        recordedBO
     * @param paramPmtAllocatedDefinitionConfig 还款分配定义表
     * @param lastStatementDate                 上一账单日
     * @return List<PaymentAllocationAndBalanceBO>
     */
    public List<PaymentAllocationAndBalanceBO> setPriority(RecordedMidBO recordedMidBO,
                                                           PaymentAllocatedResDTO paramPmtAllocatedDefinitionConfig,
                                                           LocalDate lastStatementDate,
                                                           List<PaymentAllocationHistoryDTO> paymentAllocationHistories) {
        List<PaymentAllocationAndBalanceBO> paymentAllocationAndBalanceBOList =
                new ArrayList<>();
        for (PaymentAllocationHistoryDTO paymentAllocationHistory : paymentAllocationHistories) {
            AccountBalanceInfo accountBalanceInfo = accountBalanceInfoMapper.selectByPrimaryKey(
                    paymentAllocationHistory.getTransactionBalanceId());
            PaymentAllocationAndBalanceBO paymentAllocationAndBalanceBO = new PaymentAllocationAndBalanceBO(
                    paymentAllocationHistory, BeanMapping.copy(accountBalanceInfo, AccountBalanceInfoDTO.class));
            fixStatementFlag(recordedMidBO, lastStatementDate, paymentAllocationHistory, paymentAllocationAndBalanceBO);
            PaymentAllocatedControlResDTO priority = gainPriorityParameterConfig(recordedMidBO,
                    paramPmtAllocatedDefinitionConfig,
                    accountBalanceInfo,
                    paymentAllocationAndBalanceBO);
            checkParameter(priority);
            Integer priorityValue = priority.getPriority();
            paymentAllocationAndBalanceBO.setPriority(priorityValue);
            paymentAllocationAndBalanceBOList.add(paymentAllocationAndBalanceBO);
        }
        return paymentAllocationAndBalanceBOList;
    }

    /**
     * 获取优先级参数
     *
     * @param recordedMidBO                        接口参数
     * @param paramPmtAllocatedDefinitionConfig 还款定义参数
     * @param accountBalanceInfo                交易账户
     * @param paymentAllocationAndBalanceBO     还款结果
     * @return
     */
    private PaymentAllocatedControlResDTO gainPriorityParameterConfig(RecordedMidBO recordedMidBO,
                                                                      PaymentAllocatedResDTO paramPmtAllocatedDefinitionConfig,
                                                                      AccountBalanceInfo accountBalanceInfo,
                                                                      PaymentAllocationAndBalanceBO paymentAllocationAndBalanceBO) {

        String transactionTypeCode = accountBalanceInfo.getTransactionTypeCode();

        if ( accountBalanceInfo.getAbsStatus()==null ) {
            accountBalanceInfo.setAbsStatus("N");
        }

        return paymentAllocatedControlService.findByMultipleConditions(
                recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber(),
                paramPmtAllocatedDefinitionConfig.getTableId(),
                paymentAllocationAndBalanceBO.getStatementFlag().toString(), transactionTypeCode,
                accountBalanceInfo.getLargeGraceIndicator(),
                accountBalanceInfo.getAbsStatus());
    }

    /**
     * 设置已出账单标志
     *
     * @param recordedMidBO                    接口参数
     * @param lastStatementDate             上账单日
     * @param paymentAllocationHistory      还款历史
     * @param paymentAllocationAndBalanceBO 结果类型
     */
    private void fixStatementFlag(RecordedMidBO recordedMidBO, LocalDate lastStatementDate,
                                  PaymentAllocationHistoryDTO paymentAllocationHistory,
                                  PaymentAllocationAndBalanceBO paymentAllocationAndBalanceBO) {

        if (isOverflow(paymentAllocationHistory.getTransactionBalanceType())) {
            if (compareTwoLocalDate(recordedMidBO.recorded.getTxnBillingDate(), lastStatementDate) > 0) {
                paymentAllocationAndBalanceBO.setStatementFlag(0);
            } else {
                paymentAllocationAndBalanceBO.setStatementFlag(1);
            }
        } else {
            if (compareTwoLocalDate(paymentAllocationHistory.getCreateDate(), lastStatementDate) > 0) {
                paymentAllocationAndBalanceBO.setStatementFlag(0);
            } else {
                paymentAllocationAndBalanceBO.setStatementFlag(1);
            }
        }
    }

    private void checkParameter(Object parameter) {
        if ( parameter==null ) {
            logger.error("Parameter does not exist");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
    }

    private int compareTwoLocalDate(LocalDate date1, LocalDate date2) {
        return  date2!= null &&  date1!=null  ? date1.compareTo(date2) : 1;
    }
}
