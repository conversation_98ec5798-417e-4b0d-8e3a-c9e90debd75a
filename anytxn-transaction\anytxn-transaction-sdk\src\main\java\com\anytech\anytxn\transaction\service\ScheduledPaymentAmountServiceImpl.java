package com.anytech.anytxn.transaction.service;


import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.accounting.enums.CycleDueEnum;
import com.anytech.anytxn.business.base.card.constants.CardBusinessConstant;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountPaymentHistoryMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountPaymentHistorySelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountPaymentHistory;
import com.anytech.anytxn.common.core.enums.TransactionAttributeEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.BlockCodeMaintenanceLog;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.business.dao.transaction.mapper.PaymentAllocationHistorySelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PaymentAllocationHistory;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.MinimumPaymentPercentResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlService;
import com.anytech.anytxn.parameter.base.account.service.IMinimumPaymentPercentService;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeAccount;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.constants.Constants;
import com.anytech.anytxn.transaction.base.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.enums.TransactionRepDetailEnum;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import com.anytech.anytxn.transaction.base.service.IScheduledPaymentAmountService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.base.utils.CalculateDateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 应还款额逻辑实现
 *
 * <AUTHOR>
 * @Description
 * @date 2018-09-06
 */
@Service
public class ScheduledPaymentAmountServiceImpl implements IScheduledPaymentAmountService {
    private static final Logger logger = LoggerFactory.getLogger(ScheduledPaymentAmountServiceImpl.class);

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private IGlAmsService glAmsService;
    @Autowired
    private IMinimumPaymentPercentService minimumPaymentPercentService;
    @Autowired
    private PaymentAllocationHistorySelfMapper paymentAllocationHistorySelfMapper;
    @Autowired
    private IStatementProcessService statementProcessService;
    @Autowired
    private IDelinquentControlService delinquentControlService;
    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;
    @Autowired
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private AccountPaymentHistorySelfMapper accountPaymentHistorySelfMapper;
    @Autowired
    private AccountPaymentHistoryMapper accountPaymentHistoryMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private ParmBlockCodeAccountSelfMapper parmBlockCodeAccountSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private CustomerAuthorizationInfoMapper customerAuthorizationInfoMapper;
    @Autowired
    private IMaintenanceLogBisService maintenanceLogService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public void process(RecordedMidBO recordedMidBO) {
        logger.info("Account management {} scheduled payment amount processing started", recordedMidBO.accountBO.getAccountManagementInfo()
                .getAccountManagementId());

        TransactionCodeResDTO transactionCodeResDTO = recordedMidBO.transactionCodeResDTO;
        //如果交易码参数中的交易模式 = 1（还款模式），按原逻辑继续处理。b）上述a）以外，结束应还款额处理。
        //注意这里比较容易发生还款后由于该交易码未配置成还款模式而导致各延滞金额不正确的情况
        if (!Objects.equals("1", transactionCodeResDTO.getTransactionMode())) {
            logger.debug("Transaction code configured transaction mode value is {}, not 1. Skip scheduled payment amount processing", transactionCodeResDTO.getTransactionMode());
            return;
        }


        //这里由于内生交易可能已经进行了修改，需要取到最新的accountManagementInfo。但是会导致效率问题，结构有待优化


        AccountManagementInfoDTO accountManagementInfoDTO = txnRecordedAccountCacheService.accountManagementInfoDTOSelectByPrimaryKey(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        //数据准备时有将客户ID 设置成 公司客户ID情况，此处恢复
        //!!! 开卡未创建管理 ，开卡成功后首笔交易进行还款，这里会把客户id更新为空,有问题！！！
       /* AccountManagementInfo dbAcc = accountManagementInfoMapper.selectByPrimaryKey(accountManagementInfoDTO.getAccountManagementId());
        accountManagementInfoDTO.setCustomerId(dbAcc!=null?dbAcc.getCustomerId():null);*/

        BlockCodeMaintenanceLog blockCodeMaintenanceLog = new BlockCodeMaintenanceLog();
        String mid = String.valueOf(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        blockCodeMaintenanceLog.setLogId(mid);
        blockCodeMaintenanceLog.setKey(accountManagementInfoDTO.getAccountManagementId());
        blockCodeMaintenanceLog.setKeyType(CardBusinessConstant.KEY_TYPE_A);
        blockCodeMaintenanceLog.setBranchNumber(accountManagementInfoDTO.getOrganizationNumber());
        blockCodeMaintenanceLog.setBlockCodeBefore(accountManagementInfoDTO.getBlockCode());
        blockCodeMaintenanceLog.setBlockCodeDateBefore(accountManagementInfoDTO.getBlockCodeSetDate());
        blockCodeMaintenanceLog.setPreviousBlockCodeBefore(accountManagementInfoDTO.getPreviousBlockCode());
        blockCodeMaintenanceLog.setPreviousBlockDateBefore(accountManagementInfoDTO.getPreviousBlockCodeSetDate());
        blockCodeMaintenanceLog.setPreBlockStopDateBefore(accountManagementInfoDTO.getPreviousBlockCodeSetDate());

        //首期账单也需要更新管理账户的还款总额
        if (accountManagementInfoDTO.getLastStatementDate() == null) {
            LocalDate transactionDate = recordedMidBO.recorded.getTxnTransactionDate().toLocalDate();
            LocalDate nextProcessingDay = recordedMidBO.organizationInfoResDTO.getNextProcessingDay();
            LocalDate today = recordedMidBO.organizationInfoResDTO.getToday();
            LocalDate accruedThruDay = recordedMidBO.organizationInfoResDTO.getAccruedThruDay();
            LocalDate calculateDate = custReconciliationControlService.getBillingDate(recordedMidBO.getControl(), accruedThruDay, today, nextProcessingDay);
            LocalDate nextStatementDate = CalculateDateUtils.calculateDate(calculateDate, accountManagementInfoDTO.getCycleDay());
            if (!transactionDate.isAfter(nextStatementDate)) {
                BigDecimal amount = accountManagementInfoDTO.getTotalPaymentAmount() == null ? BigDecimal.ZERO : accountManagementInfoDTO.getTotalPaymentAmount();
                accountManagementInfoDTO.setTotalPaymentAmount(amount.add(recordedMidBO.recorded.getTxnBillingAmount()));
            }
            // 更新最后还款日期 及最后还款金额
            setLastPaymentAmountAndDate(accountManagementInfoDTO, recordedMidBO);
            //更新延滞状态
            setCycleDue(accountManagementInfoDTO);
            txnRecordedAccountCacheService.accountManagementInfoUpdateByPrimaryKey(BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class));
        }

        if (recordedMidBO.isSchedulePaymentAmountService()) {
            String pymtAuth = transactionCodeResDTO.getPymtAuth();
            if (recordedMidBO.isCreditTransaction()) {
                if ("1".equals(pymtAuth)) {
                    leastScheduledPaymentAmount(recordedMidBO, accountManagementInfoDTO);
                } else if ("2".equals(pymtAuth)) {
                    //按百分比冲减最低额
                    redutionByPercent(recordedMidBO, accountManagementInfoDTO);
                }

                creditAdjustBeforeGraceDay(accountManagementInfoDTO,
                        recordedMidBO.recorded.getTxnBillingDate(),
                        recordedMidBO.recorded.getTxnBillingAmount());
            } else if (recordedMidBO.isRepaymentTransaction()) {
                if ("1".equals(pymtAuth)) {
                    leastScheduledPaymentAmount(recordedMidBO, accountManagementInfoDTO);
                } else if ("2".equals(pymtAuth)) {
                    //按百分比冲减最低额
                    redutionByPercent(recordedMidBO, accountManagementInfoDTO);
                }
                paymentAmountBeforeGraceDay(accountManagementInfoDTO,
                        recordedMidBO.recorded.getTxnBillingDate(),
                        recordedMidBO.recorded.getTxnBillingAmount());

                updateTotalPaymentAmount(accountManagementInfoDTO,
                        recordedMidBO.recorded.getTxnBillingDate(),
                        recordedMidBO.recorded.getTxnBillingAmount());

            }
            //更新延滞状态
            setCycleDue(accountManagementInfoDTO);
            //会计处理
            accountManagementInfoDTO.setUpdateTime(LocalDateTime.now());
            //五级分类
            String fiveTypeIndicator = getFiveTypeIndicator(accountManagementInfoDTO);
            accountManagementInfoDTO.setFiveTypeIndicator(fiveTypeIndicator);
            // 更新最后还款日期 及最后还款金额
            setLastPaymentAmountAndDate(accountManagementInfoDTO, recordedMidBO);
            // 入账逻辑所有数据在缓存
            glAmsService.carryOverCommon(recordedMidBO.getControl(), accountManagementInfoDTO);
            try {

                txnRecordedAccountCacheService.accountManagementInfoUpdateByPrimaryKey(BeanMapping
                        .copy(accountManagementInfoDTO, AccountManagementInfo.class));

                blockCodeMaintenanceLog.setBlockCodeAfter(accountManagementInfoDTO.getBlockCode());
                blockCodeMaintenanceLog.setBlockCodeDateAfter(accountManagementInfoDTO.getBlockCodeSetDate());
                blockCodeMaintenanceLog.setPreviousBlockCodeAfter(accountManagementInfoDTO.getPreviousBlockCode());
                blockCodeMaintenanceLog.setPreBlockDateAfter(accountManagementInfoDTO.getPreviousBlockCodeSetDate());
                blockCodeMaintenanceLog.setPreBlockStopDateAfter(null);
                blockCodeMaintenanceLog.setCreateTime(LocalDateTime.now());
                blockCodeMaintenanceLog.setUpdateTime(LocalDateTime.now());
                blockCodeMaintenanceLog.setUpdateBy("admin");
                blockCodeMaintenanceLog.setVersionNumber(1);

                try {
                    txnRecordedAccountCacheService.blockCodeMaintenanceLogInsertSelective(
                            blockCodeMaintenanceLog);
                } catch (Exception e) {
                    logger.error("Failed to insert block code maintenance history during scheduled payment amount processing", e);
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, TransactionRepDetailEnum.REIMBURSABLE_ACCOUNT);
                }
            } catch (Exception e) {
                logger.error("Failed to call [{}], query database table [{}], error info [{}]",
                        "ScheduledPaymentAmountServiceImpl" + ".process",
                        "ACCOUNT_MANAGEMENT_INFO", e);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, TransactionRepDetailEnum.ACCOUNTMANAGE, e);
            }
        } else if (recordedMidBO.isRepaymentReductionTransaction()) {
            //还款还原交易
            //还款撤销更新延滞
            logger.info("Payment reversal update delinquency: accountManagementId={}", accountManagementInfoDTO.getAccountManagementId());
            boolean b = handleRepayReductionTotalAmount(recordedMidBO, accountManagementInfoDTO);
            if (b) {
                return;
            }
        }
        logger.info("Account management {} scheduled payment amount processing completed",
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
    }


    private void setLastPaymentAmountAndDate(AccountManagementInfoDTO accountManagementInfoDTO,
                                             RecordedMidBO recordedMidBO) {
        String transactionAttribute = recordedMidBO.getTransactionCodeResDTO().getTransactionAttribute();

        if (Objects.equals(TransactionAttributeEnum.REPAYMENT.getCode(), transactionAttribute)) {
            accountManagementInfoDTO.setLastPaymentAmount(recordedMidBO.recorded.getTxnBillingAmount());
            accountManagementInfoDTO.setLastPaymentDate(recordedMidBO.getPostedTransaction().getPostingDate());
        }
    }

    /**
     * 如果统计账户00000的余额小于0则将延滞状态更新为-1
     *
     * @param accountManagementInfoDTO AccountManagementInfoDTO
     */
    private void setCycleDue(AccountManagementInfoDTO accountManagementInfoDTO) {
        //如果即将产生溢缴款，更新cycleDue为-1
        AccountStatisticsInfo accountStatisticsInfo = txnRecordedAccountCacheService
                .accountStatisticsInfoSelectByIdAndType(accountManagementInfoDTO
                        .getAccountManagementId(), "00000");
        if (!ObjectUtils.isEmpty(accountStatisticsInfo)) {
            if (accountStatisticsInfo.getBalance().compareTo(BigDecimal.ZERO) < 0) {
                //有溢缴款
                accountManagementInfoDTO.setCycleDue(CycleDueEnum.HAS_CREDIT.getCode());
                logger.info("Account management {} has overpayment, cycleDue is {}", accountManagementInfoDTO
                        .getAccountManagementId(), CycleDueEnum.HAS_CREDIT.getCode());
            }
        } else {
            logger.info("Account management {} 00000 statistics account does not exist, cycleDue is {}", accountManagementInfoDTO
                    .getAccountManagementId(), accountManagementInfoDTO.getCycleDue());
        }
    }

    /**
     * 还款撤销更新延滞
     *
     * @param recordedMidBO
     * @param accountManagementInfoDTO
     * @return
     */
    private boolean handleRepayReductionTotalAmount(RecordedMidBO recordedMidBO, AccountManagementInfoDTO accountManagementInfoDTO) {
//        AccountPaymentHistory payHisBase = accountPaymentHistorySelfMapper.selectByGlobalFlowNo(OrgNumberUtils.getOrg(),recordedBO.recorded.getTxnOriginalGlobalFlowNumber());
        AccountPaymentHistory payHisBase = accountPaymentHistorySelfMapper.selectByGlobalFlowNoAndAccountManagement(OrgNumberUtils.getOrg(), recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber(), accountManagementInfoDTO.getAccountManagementId());
        if (null == payHisBase) {
            logger.error("PayHisBase does not exist: originalGlobalFlowNumber={}", recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber());
            return true;
        }
        //更新为已撤销
        payHisBase.setReversalInd("1");
        accountPaymentHistoryMapper.updateByPrimaryKeySelective(payHisBase);
        OrganizationInfoResDTO organizationInfoResDTO = recordedMidBO.organizationInfoResDTO;
        LocalDate txnBillingDate = recordedMidBO.recorded.getTxnBillingDate();
        LocalDate pmtTxnDate = payHisBase.getPmtTxnDate();
        long numberOfDays = txnBillingDate.toEpochDay() - pmtTxnDate.toEpochDay();
        Integer pmtRevMaxDays = organizationInfoResDTO.getPmtRevMaxDays();
        if (pmtRevMaxDays < numberOfDays) {
            //不执行最小还款额的更新
            logger.error("Organization table pmtRevMaxDays is less than difference days: txnBillingDate={}, pmtTxnDate={}, pmtRevMaxDays={}", txnBillingDate, pmtTxnDate, pmtRevMaxDays);
            return true;
        }
        if (null == accountManagementInfoDTO.getLastStatementDate()
                || accountManagementInfoDTO.getLastStatementDate().compareTo(LocalDate.of(1, 1, 1)) == 0) {
            //不执行最小还款额的更新
            logger.error("Account management has no statement");
            return true;
        }
        LocalDate txnTransactionDate = recordedMidBO.recorded.getTxnTransactionDate().toLocalDate();
        List<AccountPaymentHistory> accountPaymentHisList = accountPaymentHistorySelfMapper.selectByMid(OrgNumberUtils.getOrg(), accountManagementInfoDTO.getAccountManagementId());
        List<AccountPaymentHistory> payHisPlus = accountPaymentHisList.stream().filter(x -> x.getPmtTxnDate().isAfter(payHisBase.getPmtTxnDate())
                || (x.getPmtTxnDate().isEqual(payHisBase.getPmtTxnDate()) && x.getPmySeq() > payHisBase.getPmySeq())).collect(Collectors.toList());
        logger.info("PayHisBase ID: {}", payHisBase.getId());
        BigDecimal pmtCurrDuePaid = payHisBase.getPmtCurrDuePaid();
        BigDecimal pmtPastDuePaid = payHisBase.getPmtPastDuePaid();
        BigDecimal pmt30daysPaid = payHisBase.getPmt30daysPaid();
        BigDecimal pmt60daysPaid = payHisBase.getPmt60daysPaid();
        BigDecimal pmt90daysPaid = payHisBase.getPmt90daysPaid();
        BigDecimal pmt120daysPaid = payHisBase.getPmt120daysPaid();
        BigDecimal pmt150daysPaid = payHisBase.getPmt150daysPaid();
        BigDecimal pmt180daysPaid = payHisBase.getPmt180daysPaid();
        BigDecimal pmt210daysPaid = payHisBase.getPmt210daysPaid();
        BigDecimal pmt240daysPaid = payHisBase.getPmt240daysPaid();
        BigDecimal pmt270daysPaid = payHisBase.getPmt270daysPaid();
        BigDecimal pmt300daysPaid = payHisBase.getPmt300daysPaid();
        BigDecimal pmt330daysPaid = payHisBase.getPmt330daysPaid();
        BigDecimal pmt360daysPaid = payHisBase.getPmt360daysPaid();
        BigDecimal pmt390daysPaid = payHisBase.getPmt390daysPaid();
        BigDecimal sumPmtCurrDuePaid = BigDecimal.ZERO;
        BigDecimal sumPmtPastDuePaid = BigDecimal.ZERO;
        BigDecimal sumPmt30daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt60daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt90daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt120daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt150daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt180daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt210daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt240daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt270daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt300daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt330daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt360daysPaid = BigDecimal.ZERO;
        BigDecimal sumPmt390daysPaid = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(payHisPlus)) {
            sumPmtCurrDuePaid = payHisPlus.stream().map(AccountPaymentHistory::getPmtCurrDuePaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmtPastDuePaid = payHisPlus.stream().map(AccountPaymentHistory::getPmtPastDuePaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt30daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt30daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt60daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt60daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt90daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt90daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt120daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt120daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt150daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt150daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt180daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt180daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt210daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt210daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt240daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt240daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt270daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt270daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt300daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt300daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt330daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt330daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
            sumPmt360daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt360daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);
//            sumPmt390daysPaid = payHisPlus.stream().map(AccountPaymentHistory::getPmt390daysPaid).reduce(BigDecimal.ZERO, BigDecimal::add);

        }
        accountManagementInfoDTO.setCurrentDueAmount(accountManagementInfoDTO.getCurrentDueAmount().add(pmtCurrDuePaid).add(sumPmtCurrDuePaid));
        accountManagementInfoDTO.setPastDueAmount(accountManagementInfoDTO.getPastDueAmount().add(pmtPastDuePaid).add(sumPmtPastDuePaid));
        accountManagementInfoDTO.setDay30DueAmount(accountManagementInfoDTO.getDay30DueAmount().add(pmt30daysPaid).add(sumPmt30daysPaid));
        accountManagementInfoDTO.setDay60DueAmount(accountManagementInfoDTO.getDay60DueAmount().add(pmt60daysPaid).add(sumPmt60daysPaid));
        accountManagementInfoDTO.setDay90DueAmount(accountManagementInfoDTO.getDay90DueAmount().add(pmt90daysPaid).add(sumPmt90daysPaid));
        accountManagementInfoDTO.setDay120DueAmount(accountManagementInfoDTO.getDay120DueAmount().add(pmt120daysPaid).add(sumPmt120daysPaid));
        accountManagementInfoDTO.setDay150DueAmount(accountManagementInfoDTO.getDay150DueAmount().add(pmt150daysPaid).add(sumPmt150daysPaid));
        accountManagementInfoDTO.setDay180DueAmount(accountManagementInfoDTO.getDay180DueAmount().add(pmt180daysPaid).add(sumPmt180daysPaid));
        accountManagementInfoDTO.setDay210DueAmount(accountManagementInfoDTO.getDay210DueAmount().add(pmt210daysPaid).add(sumPmt210daysPaid));
        accountManagementInfoDTO.setDay240DueAmount(accountManagementInfoDTO.getDay240DueAmount().add(pmt240daysPaid).add(sumPmt240daysPaid));
        accountManagementInfoDTO.setDay270DueAmount(accountManagementInfoDTO.getDay270DueAmount().add(pmt270daysPaid).add(sumPmt270daysPaid));
        accountManagementInfoDTO.setDay300DueAmount(accountManagementInfoDTO.getDay300DueAmount().add(pmt300daysPaid).add(sumPmt300daysPaid));
        accountManagementInfoDTO.setDay330DueAmount(accountManagementInfoDTO.getDay330DueAmount().add(pmt330daysPaid).add(sumPmt330daysPaid));
        accountManagementInfoDTO.setDay360DueAmount(accountManagementInfoDTO.getDay360DueAmount().add(pmt360daysPaid).add(sumPmt360daysPaid));
//        accountManagementInfoDTO.setDay390DueAmount(accountManagementInfoDTO.getDay390DueAmount().add(pmt390daysPaid).add(sumPmt390daysPaid));
        Stream<BigDecimal> sumPmtDuePaidStream = Stream.of(sumPmtCurrDuePaid, sumPmtPastDuePaid, sumPmt30daysPaid, sumPmt60daysPaid, sumPmt90daysPaid, sumPmt120daysPaid, sumPmt150daysPaid, sumPmt180daysPaid, sumPmt210daysPaid, sumPmt240daysPaid, sumPmt270daysPaid, sumPmt300daysPaid, sumPmt330daysPaid, sumPmt360daysPaid, sumPmt390daysPaid);
        BigDecimal sumAmtCrt = sumPmtDuePaidStream.reduce(BigDecimal.ZERO, BigDecimal::add);
        logger.info("Sum amount credit: {}", sumAmtCrt);
        //重新计算各等级的延滞金额、最小还款额，重新计算CD值
        offsetLeastScheduledPaymentAmount(recordedMidBO, accountManagementInfoDTO, sumAmtCrt);
        recordedMidBO.accountBO.setAccountManagementInfo(accountManagementInfoDTO);
        txnRecordedAccountCacheService.accountManagementInfoUpdateByPrimaryKey(BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class));
        return false;
    }


    /**
     * 最小还款额处理
     *
     * @param recordedMidBO 接口参数
     */
    public void leastScheduledPaymentAmount(RecordedMidBO recordedMidBO,
                                            AccountManagementInfoDTO accountManagementInfo) {
        // TODO update by CBS report
        List<BigDecimal> list = getDueAmountReportList(accountManagementInfo, true);
        BigDecimal totalNotPaidAmout = list.stream()
                .filter(bigDecimal -> !bigDecimal.equals(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //存在总最小还款额为0,但是账户有未支付的欠款金额,需要同步更新report金额
        if(totalNotPaidAmout.compareTo(recordedMidBO.recorded.getTxnBillingAmount()) <= 0){
            //还款总金额>总未支付金额,将report各金额置为0
            setDueAmountsReport(accountManagementInfo);
        }else {
            //还款总金额小于未支付欠款需冲减report金额
            BigDecimal[] dueAmountsReport = getDueAmountsReport(accountManagementInfo);
            reduceDueAmountReport(recordedMidBO.recorded.getTxnBillingAmount(), dueAmountsReport);
            setDueAmountsReport(accountManagementInfo, dueAmountsReport);
        }

        if (isTotalDueAmountGetxnBillingAmount(accountManagementInfo.getTotalDueAmount(),
                recordedMidBO.recorded.getTxnBillingAmount())) {

            BigDecimal remainAmount = recordedMidBO.recorded.getTxnBillingAmount().subtract(accountManagementInfo.getTotalDueAmount());

            BigDecimal[] oldDueAmounts = getDueAmounts(accountManagementInfo);
            accountManagementInfo.setTotalDueAmount(BigDecimal.ZERO);
            setDueAmounts(accountManagementInfo);
            accountManagementInfo.setCycleDue(CycleDueEnum.NO_DEBIT.getCode());
            accountManagementInfo.setWaiveLateFeeNum(0);

            //还款撤销更新延滞
            saveDueAmountLogs(accountManagementInfo, recordedMidBO, oldDueAmounts);

            //2020-02-29
            // 3 .逻辑A：最低应还款额更新处理 中的1)流程也加入延滞封锁码滚动处理
            updateBlockCode(recordedMidBO, Short.valueOf("0"), accountManagementInfo);

            // // TODO update by CBS report 管理账户的总计最小还款额为0 还款更新账户未付款容差总金额
            BigDecimal totalPaymentVarianceAmout = accountManagementInfo.getTotalPaymentVarianceAmout() == null ? BigDecimal.ZERO : accountManagementInfo.getTotalPaymentVarianceAmout();
            if(totalPaymentVarianceAmout.compareTo(remainAmount) <=0){
                accountManagementInfo.setTotalPaymentVarianceAmout(BigDecimal.ZERO);
            }else{
                accountManagementInfo.setTotalPaymentVarianceAmout(totalPaymentVarianceAmout.subtract(remainAmount));
            }

        } else {
            BigDecimal txnBillingAmount = recordedMidBO.recorded.getTxnBillingAmount();
            offsetLeastScheduledPaymentAmount(recordedMidBO, accountManagementInfo, txnBillingAmount);
        }
    }

    private List<BigDecimal> getDueAmountReportList(AccountManagementInfoDTO managementAccount, boolean isContainCurrentDueAmount) {
        List<BigDecimal> list = new ArrayList<>();
        if (isContainCurrentDueAmount) {
            list.add(getBigDecimal(managementAccount.getCurrentDueAmountReport()));
        }
        list.add(getBigDecimal(managementAccount.getDayXDueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay30DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay60DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay90DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay120DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay150DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay180DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay210DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay240DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay270DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay300DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay330DueAmountReport()));
        list.add(getBigDecimal(managementAccount.getDay360DueAmountReport()));
//        list.add(getBigDecimal(managementAccount.getDay390DueAmount()));
        return list;
    }
    
    /**
     * 封锁码联动修改
     *
     * @param orgConfig             机构
     * @param newBlockCode          封锁码
     * @param accountManagementInfo 管理账户
     */
    private void doBlockHandle(OrganizationInfoResDTO orgConfig, String newBlockCode, AccountManagementInfoDTO accountManagementInfo) {
        logger.info("Block code linkage modification");
        if (StringUtils.isNotEmpty(newBlockCode)
                && (!newBlockCode.equals(accountManagementInfo.getBlockCode()))) {
            String updFlag = null;
            if ("1".equals(orgConfig.getEscalationInUse())) {
                String esc_auth_action = null;
                int esc_priority = 0;
                int cust_priority = 0;
                List<ParmBlockCodeAccount> parmBlockCodeAccountList = new ArrayList<>();
                List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerIdAndOrgNo(accountManagementInfo.getCustomerId(), accountManagementInfo.getOrganizationNumber());
                CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId());
                CustomerAuthorizationInfo customerAuthorizationInfoOld = new CustomerAuthorizationInfo();
                BeanMapping.copy(customerAuthorizationInfo, customerAuthorizationInfoOld);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(accountManagementInfoList)) {
                    for (AccountManagementInfo managementInfo : accountManagementInfoList) {
                        //获取账户产品参数id
                        ParmProductInfo parmProductInfo = parmProductInfoSelfMapper.selectByProdNumber(managementInfo.getProductNumber(), OrgNumberUtils.getOrg()).get(0);
                        ParmBlockCodeAccount parmBlockCodeAccount = parmBlockCodeAccountSelfMapper.selectParmBlockCodeAccount(managementInfo.getOrganizationNumber(), parmProductInfo.getAccountBlockCodeTableId(), managementInfo.getBlockCode());
                        if (parmBlockCodeAccount != null && "1".equals(parmBlockCodeAccount.getEscalationIndicator())) {
                            //暂借优先级字段使用
                            parmBlockCodeAccount.setPriority(getEscalatedValue(parmBlockCodeAccount.getAuthorizationIndicator()));
                            parmBlockCodeAccountList.add(parmBlockCodeAccount);
                        }
                    }
                }
                if (parmBlockCodeAccountList.size() > 0) {
                    //获取优先级，计算出esc_priority，
                    //        parmBlockCodeAccountList.sort((x, y) -> Integer.compare(Integer.parseInt(y.getAuthorizationIndicator()), Integer.parseInt(x.getAuthorizationIndicator())));
                    parmBlockCodeAccountList.sort(Comparator.comparing(ParmBlockCodeAccount::getPriority).reversed()); // 倒序
                    ParmBlockCodeAccount max = parmBlockCodeAccountList.get(0);
                    esc_auth_action = max.getAuthorizationIndicator();
                    esc_priority = max.getPriority();
                }
                if (customerAuthorizationInfo.getEscalatedAuthorizationAction() != null) {
                    //获取优先级
                    cust_priority = getEscalatedValue(customerAuthorizationInfo.getEscalatedAuthorizationAction());
                }
                if (esc_priority > cust_priority) {
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(esc_auth_action);
                    updFlag = "1";
                } else if (esc_priority == 0 && cust_priority != 0) {
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(null);
                    updFlag = "1";
                } else if (esc_priority < cust_priority) {
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(esc_auth_action);
                    updFlag = "1";
                }
                //记录卡户人维护日志（maintenance_log）
                //添加业务日志
                if (updFlag != null) {
                    customerAuthorizationInfoMapper.updateByPrimaryKeySelective(customerAuthorizationInfo);
                    MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                    maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                    maintenanceLog.setPrimaryKeyValue(accountManagementInfo.getAccountManagementId());
                    maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                    maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_P);
                    maintenanceLog.setOriginalValue(customerAuthorizationInfoOld.getEscalatedAuthorizationAction());
                    maintenanceLog.setUpdatedValue(customerAuthorizationInfo.getEscalatedAuthorizationAction());
                    maintenanceLog.setOperatorId("admin");
                    maintenanceLogService.add(maintenanceLog, customerAuthorizationInfo, customerAuthorizationInfoOld, MaintenanceConstant.CUSTOMER_AUTHORIZATION_INFO);
                }
            }
        }
    }

    //联动属性获取组装key、value
    private int getEscalatedValue(String key) {
        Map<String, Integer> map = new HashMap();
        map.put("0", 0);
        map.put("1", 90);
        map.put("2", 70);
        map.put("3", 80);
        map.put("4", 60);
        map.put("5", 0);
        map.put("6", 0);
        map.put("7", 0);
        map.put("8", 0);
        map.put("9", 0);
        return map.get(key);
    }


    /**
     * 最小还款额是否小于等于接口中的入账金额
     *
     * @param totalDueAmount   最小还款额
     * @param txnBillingAmount 入账金额
     * @return 若最小还款额小于等于入账金额返回true，否则返回false
     */
    private boolean isTotalDueAmountGetxnBillingAmount(BigDecimal totalDueAmount,
                                                       BigDecimal txnBillingAmount) {
        return totalDueAmount.compareTo(txnBillingAmount) <= 0;
    }

    /**
     * 冲减还款额
     *
     * @param accountManagementInfo accountManagementInfo
     * @param recordedMidBO            接口参数
     *                              param deductAmount 冲减金额
     */
    public void offsetLeastScheduledPaymentAmount(RecordedMidBO recordedMidBO,
                                                  AccountManagementInfoDTO accountManagementInfo, BigDecimal deductAmount) {
        logger.info("Account management {} offset payment amount started",
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());

        //账单参数信息
        StatementProcessResDTO paramStatementProcess = statementProcessService.findByOrgAndTableId(
                accountManagementInfo.getOrganizationNumber(),
                recordedMidBO.productInfoResDTO.getStatementProcessingTableId());
        if (paramStatementProcess == null) {
            logger.error("ParamStatementProcess does not exist: organizationNumber={}, statementProcessingTableId={}", accountManagementInfo.getOrganizationNumber(), recordedMidBO.productInfoResDTO.getStatementProcessingTableId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        BigDecimal paymentVariance = paramStatementProcess.getPaymentVariance();
        BigDecimal[] dueAmounts = getDueAmounts(accountManagementInfo);
        BigDecimal[] oldDueAmounts = getDueAmounts(accountManagementInfo);
        logger.info("Payment update delinquency level started, current payment amount: {}", deductAmount);
        short cycleDue = getCycleDue(deductAmount, paymentVariance, dueAmounts);
        logger.info("CycleDue is: {}", cycleDue);
        setDueAmounts(accountManagementInfo, dueAmounts);
        BigDecimal totalDueAmount = Stream.of(dueAmounts).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        accountManagementInfo.setTotalDueAmount(totalDueAmount);
        accountManagementInfo.setCycleDue(cycleDue);
        if (CycleDueEnum.isUnDelayedState(cycleDue)) {
            accountManagementInfo.setWaiveLateFeeNum(0);
        }

        if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), recordedMidBO.transactionCodeResDTO.getDebitCreditIndicator())) {
            saveDueAmountLogs(accountManagementInfo, recordedMidBO, oldDueAmounts);
        }
        updateBlockCode(recordedMidBO, cycleDue, accountManagementInfo);
        logger.info("Account management {} offset payment amount completed",
                recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
    }

    /**
     * 获取延滞等级
     *
     * @param txnBillingAmount 接口金额
     * @param paymentVariance  容差
     * @param dueAmounts       dueAmounts
     * @return 延滞等级
     */
    public short getCycleDue(BigDecimal txnBillingAmount, BigDecimal paymentVariance,
                             BigDecimal[] dueAmounts) {
        short cycleDue = 0;
        for (int i = dueAmounts.length - 1; i >= 0; i--) {
            if (dueAmounts[i].compareTo(txnBillingAmount) > 0) {
                dueAmounts[i] = dueAmounts[i].subtract(txnBillingAmount);
                txnBillingAmount = BigDecimal.ZERO;
                logger.info("Current due amount is greater than payment amount, current period: {}", i + 1);
//                if (isUpdateCycle(dueAmounts[i], paymentVariance)) {
                if (cycleDue == 0) {
                    cycleDue = (short) (i + 1);
                    logger.info("CycleDue is: {}", cycleDue);   
                }
//                }
//                else {
//                    dueAmounts[i] = BigDecimal.ZERO;
//                }
            } else if (txnBillingAmount.compareTo(BigDecimal.ZERO) > 0 && dueAmounts[i].compareTo(txnBillingAmount) == 0) {
                dueAmounts[i] = dueAmounts[i].subtract(txnBillingAmount);
                txnBillingAmount = BigDecimal.ZERO;
                logger.info("Current due amount is equal to payment amount, current period: {}", i + 1);
                if (cycleDue > 0) {
                    cycleDue = (short) (cycleDue - 1);
                    logger.info("CycleDue is: {}", cycleDue);
                }
            } else {
                logger.info("Current due amount is less than payment amount, current period: {}", i + 1);
                txnBillingAmount = txnBillingAmount.subtract(dueAmounts[i]);
                dueAmounts[i] = BigDecimal.ZERO;
                logger.info("CycleDue is: {}", cycleDue);
            }
        }
        return cycleDue;
    }

    public void reduceDueAmountReport(BigDecimal txnBillingAmount,BigDecimal[] dueAmounts) {

        for (int i = dueAmounts.length - 1; i >= 0; i--) {
            if (dueAmounts[i].compareTo(txnBillingAmount) > 0) {
                dueAmounts[i] = dueAmounts[i].subtract(txnBillingAmount);
                txnBillingAmount = BigDecimal.ZERO;
            } else if (txnBillingAmount.compareTo(BigDecimal.ZERO)>0 && dueAmounts[i].compareTo(txnBillingAmount) == 0){
                dueAmounts[i] = dueAmounts[i].subtract(txnBillingAmount);
                txnBillingAmount = BigDecimal.ZERO;
            } else {
                txnBillingAmount = txnBillingAmount.subtract(dueAmounts[i]);
                dueAmounts[i] = BigDecimal.ZERO;
            }
        }
    }


    /**
     * 应缴总金额是否大于容差
     *
     * @param dueAmount       应缴金额
     * @param paymentVariance 容差
     * @return 若应缴金额大于容差则返回true，否则返回false
     */
    private boolean isUpdateCycle(BigDecimal dueAmount, BigDecimal paymentVariance) {
        return dueAmount.compareTo(BigDecimal.ZERO) > 0 && dueAmount.compareTo(
                paymentVariance) > 0;
    }

    /**
     * 设置还款额
     *
     * @param accountManagementInfo 管理账户
     * @param amounts               还款额
     */
    public void setDueAmounts(AccountManagementInfoDTO accountManagementInfo,
                              BigDecimal... amounts) {
        if (amounts == null || amounts.length == 0) {
            accountManagementInfo.setCurrentDueAmount(BigDecimal.ZERO);
            accountManagementInfo.setPastDueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay30DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay60DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay90DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay120DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay150DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay180DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay210DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay240DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay270DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay300DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay330DueAmount(BigDecimal.ZERO);
            accountManagementInfo.setDay360DueAmount(BigDecimal.ZERO);
//            accountManagementInfo.setDay390DueAmount(BigDecimal.ZERO);
        } else {
            accountManagementInfo.setCurrentDueAmount(amounts[0]);
            accountManagementInfo.setPastDueAmount(amounts[1]);
            accountManagementInfo.setDay30DueAmount(amounts[2]);
            accountManagementInfo.setDay60DueAmount(amounts[3]);
            accountManagementInfo.setDay90DueAmount(amounts[4]);
            accountManagementInfo.setDay120DueAmount(amounts[5]);
            accountManagementInfo.setDay150DueAmount(amounts[6]);
            accountManagementInfo.setDay180DueAmount(amounts[7]);
            accountManagementInfo.setDay210DueAmount(amounts[8]);
            accountManagementInfo.setDay240DueAmount(amounts[9]);
            accountManagementInfo.setDay270DueAmount(amounts[10]);
            accountManagementInfo.setDay300DueAmount(amounts[11]);
            accountManagementInfo.setDay330DueAmount(amounts[12]);
            accountManagementInfo.setDay360DueAmount(amounts[13]);
//            accountManagementInfo.setDay390DueAmount(amounts[14]);
        }
    }

    public void setDueAmountsReport(AccountManagementInfoDTO accountManagementInfo,
                              BigDecimal... amounts) {
        if ( amounts==null  || amounts.length == 0) {
            accountManagementInfo.setCurrentDueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDayXDueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay30DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay60DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay90DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay120DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay150DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay180DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay210DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay240DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay270DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay300DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay330DueAmountReport(BigDecimal.ZERO);
            accountManagementInfo.setDay360DueAmountReport(BigDecimal.ZERO);
//            accountManagementInfo.setDay390DueAmount(BigDecimal.ZERO);
        } else {
            accountManagementInfo.setCurrentDueAmountReport(amounts[0]);
            accountManagementInfo.setDayXDueAmountReport(amounts[1]);
            accountManagementInfo.setDay30DueAmountReport(amounts[2]);
            accountManagementInfo.setDay60DueAmountReport(amounts[3]);
            accountManagementInfo.setDay90DueAmountReport(amounts[4]);
            accountManagementInfo.setDay120DueAmountReport(amounts[5]);
            accountManagementInfo.setDay150DueAmountReport(amounts[6]);
            accountManagementInfo.setDay180DueAmountReport(amounts[7]);
            accountManagementInfo.setDay210DueAmountReport(amounts[8]);
            accountManagementInfo.setDay240DueAmountReport(amounts[9]);
            accountManagementInfo.setDay270DueAmountReport(amounts[10]);
            accountManagementInfo.setDay300DueAmountReport(amounts[11]);
            accountManagementInfo.setDay330DueAmountReport(amounts[12]);
            accountManagementInfo.setDay360DueAmountReport(amounts[13]);
//            accountManagementInfo.setDay390DueAmount(amounts[14]);
        }
    }

    /**
     * 账户还款历史的记录
     * 条件：交易的借贷方向为贷记，并且冲减了最小还款额（外部判断，dueAmounts有值）
     *
     * @param accountManagementInfoDTO
     * @param recordedMidBO
     */
    public void saveDueAmountLogs(AccountManagementInfoDTO accountManagementInfoDTO, RecordedMidBO recordedMidBO, BigDecimal[] oldDueAmounts) {
        if (recordedMidBO.getTransactionCodeResDTO() != null && recordedMidBO.getTransactionCodeResDTO().getDebitCreditIndicator().equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode())) {
            AccountPaymentHistory history = new AccountPaymentHistory();
            history.setId(new BigDecimal(sequenceIdGen.generateId(TenantUtils.getTenantId())));
            history.setAccountManagementId(accountManagementInfoDTO.getAccountManagementId());
            history.setGlobalFlowNo(recordedMidBO.recorded.getTxnGlobalFlowNumber());
            history.setPmtTxnDate(recordedMidBO.recorded.getTxnTransactionDate().toLocalDate());

            int i = accountPaymentHistorySelfMapper.countByMidAndDate(OrgNumberUtils.getOrg(), accountManagementInfoDTO.getAccountManagementId(), recordedMidBO.recorded.getTxnTransactionDate().toLocalDate());
            history.setPmySeq((i + 1L));
            history.setReversalInd("0");
            history.setPmtAmt(recordedMidBO.recorded.getTxnBillingAmount());
            logger.info("Old due amounts: {}, account: accountManagementId={}, currentDueAmount={}, totalDueAmount={}",
                    oldDueAmounts, accountManagementInfoDTO.getAccountManagementId(),
                    accountManagementInfoDTO.getCurrentDueAmount(), accountManagementInfoDTO.getTotalDueAmount());
            history.setPmtCurrDuePaid(oldDueAmounts[0].subtract(accountManagementInfoDTO.getCurrentDueAmount()));
            history.setPmtPastDuePaid(oldDueAmounts[1].subtract(accountManagementInfoDTO.getPastDueAmount()));
            history.setPmt30daysPaid(oldDueAmounts[2].subtract(accountManagementInfoDTO.getDay30DueAmount()));
            history.setPmt60daysPaid(oldDueAmounts[3].subtract(accountManagementInfoDTO.getDay60DueAmount()));
            history.setPmt90daysPaid(oldDueAmounts[4].subtract(accountManagementInfoDTO.getDay90DueAmount()));
            history.setPmt120daysPaid(oldDueAmounts[5].subtract(accountManagementInfoDTO.getDay120DueAmount()));
            history.setPmt150daysPaid(oldDueAmounts[6].subtract(accountManagementInfoDTO.getDay150DueAmount()));
            history.setPmt180daysPaid(oldDueAmounts[7].subtract(accountManagementInfoDTO.getDay180DueAmount()));
            history.setPmt210daysPaid(oldDueAmounts[8].subtract(accountManagementInfoDTO.getDay210DueAmount()));
            history.setPmt240daysPaid(oldDueAmounts[9].subtract(accountManagementInfoDTO.getDay240DueAmount()));
            history.setPmt270daysPaid(oldDueAmounts[10].subtract(accountManagementInfoDTO.getDay270DueAmount()));
            history.setPmt300daysPaid(oldDueAmounts[11].subtract(accountManagementInfoDTO.getDay300DueAmount()));
            history.setPmt330daysPaid(oldDueAmounts[12].subtract(accountManagementInfoDTO.getDay330DueAmount()));
            history.setPmt360daysPaid(oldDueAmounts[13].subtract(accountManagementInfoDTO.getDay360DueAmount()));
//            history.setPmt390daysPaid(oldDueAmounts[14].subtract(accountManagementInfoDTO.getDay390DueAmount()));

            history.setCreateTime(LocalDateTime.now());
            history.setUpdateTime(LocalDateTime.now());
            history.setUpdateBy("admin");
            history.setVersionNumber(1L);

            accountPaymentHistoryMapper.insertSelective(history);
        }
    }

    /**
     * 将最小还款额相关的数据整合为一个数组
     *
     * @return 最小还款额相关数组
     */
    public BigDecimal[] getDueAmounts(AccountManagementInfoDTO accountManagementInfo) {
        return new BigDecimal[]{
                getBigDecimal(accountManagementInfo.getCurrentDueAmount()),
                getBigDecimal(accountManagementInfo.getPastDueAmount()),
                getBigDecimal(accountManagementInfo.getDay30DueAmount()),
                getBigDecimal(accountManagementInfo.getDay60DueAmount()),
                getBigDecimal(accountManagementInfo.getDay90DueAmount()),
                getBigDecimal(accountManagementInfo.getDay120DueAmount()),
                getBigDecimal(accountManagementInfo.getDay150DueAmount()),
                getBigDecimal(accountManagementInfo.getDay180DueAmount()),
                getBigDecimal(accountManagementInfo.getDay210DueAmount()),
                getBigDecimal(accountManagementInfo.getDay240DueAmount()),
                getBigDecimal(accountManagementInfo.getDay270DueAmount()),
                getBigDecimal(accountManagementInfo.getDay300DueAmount()),
                getBigDecimal(accountManagementInfo.getDay330DueAmount()),
                getBigDecimal(accountManagementInfo.getDay360DueAmount())};
    }

    public BigDecimal[] getDueAmountsReport(AccountManagementInfoDTO accountManagementInfo) {
        return new BigDecimal[]{
                getBigDecimal(accountManagementInfo.getCurrentDueAmountReport()),
                getBigDecimal(accountManagementInfo.getDayXDueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay30DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay60DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay90DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay120DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay150DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay180DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay210DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay240DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay270DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay300DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay330DueAmountReport()),
                getBigDecimal(accountManagementInfo.getDay360DueAmountReport())};
    }

    /**
     * 如果传入的数据为空，返回Zero。反之，返回其自身
     *
     * @param amount 金额
     * @return BigDecimal
     */
    private BigDecimal getBigDecimal(BigDecimal amount) {
        return amount == null ? BigDecimal.ZERO : amount;
    }

    /**
     * 宽限期内还款金额更新处理
     */
    public void paymentAmountBeforeGraceDay(AccountManagementInfoDTO accountManagementInfo,
                                            LocalDate txnBillingDate,
                                            BigDecimal txnBillingAmount) {
        AccountStatementInfo accountStatementInfo = getAccountStatementInfo(
                accountManagementInfo);
        if (isUpdateManageAccountByTxnTransactionDate(txnBillingDate,
                accountStatementInfo)) {
            accountManagementInfo.setTotalGracePaymentAmount(
                    accountManagementInfo.getTotalGracePaymentAmount().add(
                            txnBillingAmount));
        }
        updateStatementDueAmount(accountManagementInfo, txnBillingAmount);
    }

    /**
     * 修改应还款额处理，在类似于原来更新“宽限期内还款金额”的地方，或者最后更新管理账户前，增加一组更新“还款总金额”的逻辑：
     * 1、根据管理账户的账单日等要素，计算下一账单日
     * 2、在处理还款交易时，如果管理账户的上一账单日 < 接口中的交易日期（txnTransactionDate）<= 下一账单日，还款总金额 = 还款总金额 + 接口中的入账金额（txnBillingAmount），否则不更新
     */
    private void updateTotalPaymentAmount(AccountManagementInfoDTO accountManagementInfo, LocalDate txnTransactionDate,
                                          BigDecimal txnBillingAmount) {
        if (accountManagementInfo.getLastStatementDate() == null) {
            return;
        }
        LocalDate nextStatementDate = CalculateDateUtils.calculateDate(accountManagementInfo.getLastStatementDate().plusDays(1), accountManagementInfo.getCycleDay());
        if (txnTransactionDate.isAfter(accountManagementInfo.getLastStatementDate()) &&
                (txnTransactionDate.isEqual(nextStatementDate) || txnTransactionDate.isBefore(nextStatementDate))) {
            BigDecimal amount = accountManagementInfo.getTotalPaymentAmount() == null ? BigDecimal.ZERO : accountManagementInfo.getTotalPaymentAmount();
            accountManagementInfo.setTotalPaymentAmount(amount.add(txnBillingAmount));
        }

    }

    /**
     * 根据入账日期和利息宽限日期判断是否需要更新accountManageInfo
     *
     * @param txnBillingDate       入账日期
     * @param accountStatementInfo 账单日期
     * @return boolean
     */
    private boolean isUpdateManageAccountByTxnTransactionDate(LocalDate txnBillingDate,
                                                              AccountStatementInfo accountStatementInfo) {

        return !txnBillingDate.isAfter(
                accountStatementInfo.getInterestWaiveGraceDate());

    }


    private AccountStatementInfo getAccountStatementInfo(AccountManagementInfoDTO accountManagementInfo) {
        AccountStatementInfo accountStatementInfo = new AccountStatementInfo();
        try {
            accountStatementInfo = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(
                    accountManagementInfo.getAccountManagementId(),
                    accountManagementInfo.getLastStatementDate());
        } catch (Exception e) {
            logger.error("Failed to call [{}], query database table [{}], error info [{}]",
                    "ScheduledPaymentAmountServiceImpl.creditAdjustBeforeGraceDay",
                    "ACCOUNT_STATEMENT_INFO", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_SELECT_ERROR, TransactionRepDetailEnum.BILL_ACCOUNT, e);
        }
        return accountStatementInfo;
    }

    /**
     * 宽限期内往期贷调总金额更新处理
     */
    public void creditAdjustBeforeGraceDay(AccountManagementInfoDTO accountManagementInfo,
                                           LocalDate txnBillingDate,
                                           BigDecimal txnBillingAmount) {
        AccountStatementInfo accountStatementInfo = getAccountStatementInfo(accountManagementInfo);
        if (isUpdateManageAccountByTxnTransactionDate(txnBillingDate, accountStatementInfo)) {
            accountManagementInfo.setLastCycleCreditAdjAmount(accountManagementInfo.getLastCycleCreditAdjAmount().add(txnBillingAmount));
        }
        updateStatementDueAmount(accountManagementInfo, txnBillingAmount);
    }

    /**
     * 更新账单剩余未还金额
     *
     * @param accountManagementInfo 账户管理信息表
     * @param txnBillingAmount      入账金额
     */
    private void updateStatementDueAmount(AccountManagementInfoDTO accountManagementInfo, BigDecimal txnBillingAmount) {
        if (accountManagementInfo.getStatementDueAmount().compareTo(txnBillingAmount) > 0) {
            accountManagementInfo.setStatementDueAmount(accountManagementInfo.getStatementDueAmount().subtract(txnBillingAmount));
        } else {
            accountManagementInfo.setStatementDueAmount(BigDecimal.ZERO);
        }
        //同步更新当前逾期总额
        if (accountManagementInfo.getCurrentAmountOverdue().compareTo(BigDecimal.ZERO) > 0) {
            if (accountManagementInfo.getCurrentAmountOverdue().compareTo(txnBillingAmount) > 0) {
                accountManagementInfo.setCurrentAmountOverdue(accountManagementInfo.getCurrentAmountOverdue().subtract(txnBillingAmount));
            } else {
                accountManagementInfo.setCurrentAmountOverdue(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 封锁码更新公共处理
     */
    public void updateBlockCode(RecordedMidBO recordedMidBO, short cycleDue,
                                AccountManagementInfoDTO accountManagementInfo) {
        logger.info("Account management {} block code update started", recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        String organizationNumber = accountManagementInfo.getOrganizationNumber();
        if (cycleDue < TransactionConstants.CYCLE_DUE_AMOUNT_FLAG) {
            // f）如果账户管理信息表（account_management_info）中的延滞状态（cycle_due）< 2：
            getAccountManagementInfoWithNotCycleDue(recordedMidBO, accountManagementInfo, organizationNumber);
        } else {
            updateBlockCodeWithCycleDue(recordedMidBO, cycleDue, accountManagementInfo);
        }
        logger.info("Account management {} block code update completed", recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
    }

    /**
     * 延滞户的封锁码处理
     *
     * @param recordedMidBO            接口参数
     * @param cycleDue              延滞等级
     * @param accountManagementInfo 账户管理信息
     */
    public void updateBlockCodeWithCycleDue(RecordedMidBO recordedMidBO, short cycleDue,
                                            AccountManagementInfoDTO accountManagementInfo) {
        logger.info("Delinquent account block code processing");
        DelinquentControlResDTO paramDelinquentControl = delinquentControlService.findByOrgNumTableIdCycleDue(
                accountManagementInfo.getOrganizationNumber(),
                recordedMidBO.productInfoResDTO.getDelinquentControlTableId(),
                Integer.valueOf(cycleDue));
        if (paramDelinquentControl == null) {
            logger.error("ParamDelinquentControl does not exist: organizationNumber={}, delinquentControlTableId={}, cycleDue={}", accountManagementInfo.getOrganizationNumber(),
                    recordedMidBO.productInfoResDTO.getDelinquentControlTableId(),
                    Integer.valueOf(cycleDue));
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.DELAY_NOT_EXIST);
        }
        String newBlockCode = paramDelinquentControl.getBlockCode();
        String oldBlockType = getPriority(recordedMidBO, accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getBlockCode()).getType();
        if (TransactionConstants.BLOCK_CODE_TYPE_CYCLE.equals(oldBlockType)) {
            if (!newBlockCode.equals(accountManagementInfo.getBlockCode())) {
                accountManagementInfo.setBlockCode(newBlockCode);
                accountManagementInfo.setBlockCodeSetDate(getToday(recordedMidBO));
            }
        } else {
            updateBlockCodeAndPreviousBlockCode(accountManagementInfo.getBlockCode(), newBlockCode, recordedMidBO, accountManagementInfo, oldBlockType);
            // 封锁码联动处理
            if (!"C".equals(accountManagementInfo.getLiability())) {
                doBlockHandle(recordedMidBO.getOrganizationInfoResDTO(), newBlockCode, accountManagementInfo);
            }
        }
    }

    /**
     * 非延滞户的封锁码处理
     *
     * @param recordedMidBO            接口参数
     * @param accountManagementInfo 管理账户信息
     * @param organizationNumber    机构信息
     */
    public void getAccountManagementInfoWithNotCycleDue(RecordedMidBO recordedMidBO,
                                                        AccountManagementInfoDTO accountManagementInfo,
                                                        String organizationNumber) {
        logger.info("Non-delinquent account block code processing: {}", accountManagementInfo.getBlockCode());
        if (!StringUtils.isBlank(accountManagementInfo.getBlockCode())) {
            String oldBlockType = getPriority(recordedMidBO, organizationNumber, accountManagementInfo.getBlockCode()).getType();
            if (TransactionConstants.BLOCK_CODE_TYPE_CYCLE.equals(oldBlockType)) {
                // f21）如果原封锁码参数中的封锁码类别（type） = 0（系统封锁码-延滞），
                // 先update账户管理信息表（account_management_info），赋值规则见《Rule-1.1》。
                // 再根据update后结果，进行下述f22和f23处理逻辑。
                updateWithOldBlockCodeTypeIsCycle(recordedMidBO, accountManagementInfo, organizationNumber);
            }
        }
    }

    /**
     * 原封锁码类型为延滞类型处理
     *
     * @param recordedMidBO            recordedBO
     * @param accountManagementInfo accountManagementInfo
     * @param organizationNumber    organizationNumber
     */
    public void updateWithOldBlockCodeTypeIsCycle(RecordedMidBO recordedMidBO,
                                                  AccountManagementInfoDTO accountManagementInfo,
                                                  String organizationNumber) {
        BlockCodeAccountResDTO lastPriorityConfig;
        String newBlockCode;
        accountManagementInfo.setBlockCode(null);
        accountManagementInfo.setBlockCodeSetDate(getToday(recordedMidBO));
        lastPriorityConfig = getPriority(recordedMidBO, organizationNumber, accountManagementInfo.getPreviousBlockCode());
        String lastBlockCodeType = lastPriorityConfig.getType();

        if (!TransactionConstants.BLOCK_CODE_TYPE_CYCLE.equals(lastBlockCodeType)) {
            newBlockCode = accountManagementInfo.getPreviousBlockCode();
            updateBlockCodeAndPreviousBlockCode(accountManagementInfo.getBlockCode(), newBlockCode, recordedMidBO, accountManagementInfo, lastBlockCodeType);
        } else {
            //f22）如果上一封锁码参数中的封锁码类别（type） = 0（系统封锁码-延滞），
            // update账户管理信息表（account_management_info），赋值规则见《Rule-1.2》
            accountManagementInfo.setPreviousBlockCode("");
            accountManagementInfo.setPreviousBlockCodeSetDate(getToday(recordedMidBO));
        }
    }

    /**
     * 更新封锁码
     *
     * @param oldBlockCode          原封锁码
     * @param newBlockCode          新封锁码
     * @param recordedMidBO            接口数据
     * @param accountManagementInfo 管理账户信息
     */
    public void updateBlockCodeAndPreviousBlockCode(String oldBlockCode,
                                                    String newBlockCode,
                                                    RecordedMidBO recordedMidBO,
                                                    AccountManagementInfoDTO accountManagementInfo,
                                                    String oldBlockCodeType) {
        if (isUpdateBlockCode(oldBlockCode, newBlockCode, recordedMidBO, accountManagementInfo)) {
            if (oldBlockCodeType.equals(TransactionConstants.BLOCK_CODE_TYPE_OVER) || oldBlockCodeType.equals(TransactionConstants.BLOCK_CODE_TYPE_CYCLE)) {
                accountManagementInfo.setBlockCode(newBlockCode);
                accountManagementInfo.setBlockCodeSetDate(getToday(recordedMidBO));
            } else {
                accountManagementInfo.setPreviousBlockCode(accountManagementInfo.getBlockCode());
                accountManagementInfo.setPreviousBlockCodeSetDate(accountManagementInfo.getBlockCodeSetDate());
                accountManagementInfo.setBlockCode(newBlockCode);
                accountManagementInfo.setBlockCodeSetDate(getToday(recordedMidBO));
            }
        }
    }

    /**
     * 从参数表中获取当前时间
     *
     * @param recordedMidBO record
     * @return LocalDate
     */
    private LocalDate getToday(RecordedMidBO recordedMidBO) {
        return recordedMidBO.organizationInfoResDTO.getToday();
    }

    /**
     * 是否需要更新封锁码
     *
     * @param oldBlockCode          oldBlockCode
     * @param newBlockCode          newBlockCode
     * @param recordedMidBO            recordedBO
     * @param accountManagementInfo accountManagementInfo
     * @return boolean
     */
    public boolean isUpdateBlockCode(String oldBlockCode, String newBlockCode,
                                     RecordedMidBO recordedMidBO,
                                     AccountManagementInfoDTO accountManagementInfo) {
        if (newBlockCode != null && newBlockCode.equals(oldBlockCode)) {
            return false;
        }
        Integer oldPriority = getPriority(recordedMidBO, accountManagementInfo.getOrganizationNumber(),
                oldBlockCode).getPriority();
        Integer newPriority = getPriority(recordedMidBO, accountManagementInfo.getOrganizationNumber(),
                newBlockCode).getPriority();
        return newPriority > oldPriority;
    }

    /**
     * 获取封锁码的优先级
     *
     * @param recordedMidBO         接口参数
     * @param organizationNumber 机构号
     * @param blockCode          封锁码
     * @return String
     */
    public BlockCodeAccountResDTO getPriority(RecordedMidBO recordedMidBO,
                                              String organizationNumber, String blockCode) {
        BlockCodeAccountResDTO parameterConfig = blockCodeAccountService.findBlockCodeAccount(
                organizationNumber,
                recordedMidBO.productInfoResDTO.getAccountBlockCodeTableId(),
                blockCode);
        if (parameterConfig == null) {
            logger.error("BlockCodeAccount does not exist: organizationNumber={}, accountBlockCodeTableId={}, blockCode={}", organizationNumber,
                    recordedMidBO.productInfoResDTO.getAccountBlockCodeTableId(),
                    blockCode);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.BLOCKING_CODE_NOT_EXIST);
        }
        return parameterConfig;
    }

    /**
     * 2）	逻辑F：按百分比冲减最低额
     */
    public void redutionByPercent(RecordedMidBO recordedMidBO, AccountManagementInfoDTO accountManagementInfo) {
        String balanceId = null;
        String accId = recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId();
        AccountStatisticsInfo accountStatisticsInfoInfo = accountStatisticsInfoSelfMapper.selectByIdAndType(accId, "00000");
        //还款分配流水
        List<PaymentAllocationHistory> paymentAllocationHistorys = paymentAllocationHistorySelfMapper.listByTxnOriginalGlobalFlowNumberAndTxnAccountManageId(recordedMidBO.recorded.getTxnGlobalFlowNumber(), accountManagementInfo.getAccountManagementId());
        if (!CollectionUtils.isEmpty(paymentAllocationHistorys)) {
            if (paymentAllocationHistorys.size() > 1) {
                logger.error("Payment allocation history data is not unique based on global flow number: {}, account management ID: {}", recordedMidBO.recorded.getTxnGlobalFlowNumber(), accountManagementInfo.getAccountManagementId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UNIQUE_VIOLATION, TransactionRepDetailEnum.REPAYMENT_DISTRIBUTION_FLOW, recordedMidBO.recorded.getTxnGlobalFlowNumber());
            }
            PaymentAllocationHistory paymentAllocationHistory = paymentAllocationHistorys.get(0);
            balanceId = paymentAllocationHistory.getTransactionBalanceId();
        } else {
            balanceId = recordedMidBO.recorded.getTxnOriginalTransactionBalanceId();
        }
        if (null != balanceId && !"".equals(balanceId)) {
            /*if (paymentAllocationHistorys.size() > 1) {
                log.error("根据全局流水号：{}，管理账号：{},查询的还款分配流水表数据不唯一", recordedBO.recorded.getTxnGlobalFlowNumber(), accountManagementInfo.getAccountManagementId());
                AnyTxnTransactionRespCode.D_DATABASE_UNIQUE_VIOLATION.setDetail("还款分配流水表数据不唯一");
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_DATABASE_UNIQUE_VIOLATION,"还款分配流水表,globalFlowNumber:"+recordedBO.recorded.getTxnGlobalFlowNumber());
            }
            PaymentAllocationHistory paymentAllocationHistory = paymentAllocationHistorys.get(0);*/
            AccountBalanceInfoDTO accountBalanceInfo = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getBalanceById(balanceId);
            //有时候（比如溢缴款)accountBalanceInfo.getTransactionMinimumPaymentId()为空
            if (accountBalanceInfo != null && accountBalanceInfo.getTransactionMinimumPaymentId() != null) {
                MinimumPaymentPercentResDTO minimumPaymentPercent = minimumPaymentPercentService.findByOrgAndTableId(accountBalanceInfo.getOrganizationNumber(), accountBalanceInfo.getTransactionMinimumPaymentId());
                if (minimumPaymentPercent != null) {
                    //冲减金额
                    // 2020/02/18 最低还款比例参数表结构变化
                    BigDecimal minPaymentPercent = minimumPaymentPercent.getPreMinPaymentPercentage();
                    if (TransactionConstants.STATEMENT_INDICATOR_NO.equals(accountBalanceInfo.getStatementIndicator())) {
                        minPaymentPercent = minimumPaymentPercent.getCurMinPaymentPercentage();
                    }
                    BigDecimal redutionAmout = recordedMidBO.recorded.getTxnBillingAmount().multiply(minPaymentPercent);
                    if (accountManagementInfo.getTotalDueAmount().compareTo(redutionAmout) <= 0) {
                        BigDecimal[] oldDueAmounts = getDueAmounts(accountManagementInfo);
                        //原《逻辑A》中第1）小点进行处理
                        accountManagementInfo.setTotalDueAmount(BigDecimal.ZERO);
                        setDueAmounts(accountManagementInfo);
                        accountManagementInfo.setCycleDue(CycleDueEnum.NO_DEBIT.getCode());
                        accountManagementInfo.setWaiveLateFeeNum(0);
                        //还款撤销更新延滞
                        saveDueAmountLogs(accountManagementInfo, recordedMidBO, oldDueAmounts);

                    } else {
                        //按照原《逻辑B》进行处理,按照上述计算后的‘冲减金额’进行冲减
                        //账单参数信息
                        StatementProcessResDTO paramStatementProcess = statementProcessService.findByOrgAndTableId(
                                accountManagementInfo.getOrganizationNumber(),
                                recordedMidBO.productInfoResDTO.getStatementProcessingTableId());
                        if (paramStatementProcess == null) {
                            logger.error("Statement processing parameter does not exist: organizationNumber={}, statementProcessingTableId={}", accountManagementInfo.getOrganizationNumber(),
                                    recordedMidBO.productInfoResDTO.getStatementProcessingTableId());
                            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.BILL_PROCESSING_NOT_EXIST);
                        }
                        BigDecimal paymentVariance = paramStatementProcess.getPaymentVariance();
                        BigDecimal[] dueAmounts = getDueAmounts(accountManagementInfo);
                        BigDecimal[] oldDueAmounts = getDueAmounts(accountManagementInfo);
                        logger.info("Payment update delinquency level started, current percentage payment: {}", redutionAmout);
                        short cycleDue = getCycleDue(redutionAmout, paymentVariance, dueAmounts);
                        logger.info("CycleDue2 is: {}", cycleDue);
                        setDueAmounts(accountManagementInfo, dueAmounts);
                        BigDecimal totalDueAmount = Stream.of(dueAmounts).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                        accountManagementInfo.setTotalDueAmount(totalDueAmount);
                        accountManagementInfo.setCycleDue(cycleDue);

                        if (CycleDueEnum.isUnDelayedState(cycleDue)) {
                            accountManagementInfo.setWaiveLateFeeNum(0);
                        }

                        saveDueAmountLogs(accountManagementInfo, recordedMidBO, oldDueAmounts);
                        updateBlockCode(recordedMidBO, cycleDue, accountManagementInfo);
                    }
                }
            }
        }

    }

    /*如果更新后的cycle_due = 0 or 1，five_type_indicator赋值为1；
              b）如果更新后的cycle_due = 2 or 3 or 4，five_type_indicator赋值为2；
              c）如果更新后的cycle_due = 5，five_type_indicator赋值为3；
              d）如果更新后的cycle_due = 6 or 7，five_type_indicator赋值为4；
              e）如果更新后的cycle_due >= 8，five_type_indicator赋值为5；*/
    private String getFiveTypeIndicator(AccountManagementInfoDTO accountManagementInfoDTO) {
        String fiveTypeIndicator = null;
        if (Objects.equals(CycleDueEnum.NO_DEBIT.getCode(), accountManagementInfoDTO.getCycleDue())
                || Objects.equals(CycleDueEnum.NOT_LATE.getCode(), accountManagementInfoDTO.getCycleDue())) {
            fiveTypeIndicator = Constants.FIVE_TYPE_IND_ONE;
        } else if (Objects.equals(CycleDueEnum.X_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())
                || Objects.equals(CycleDueEnum.THIRTY_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())
                || Objects.equals(CycleDueEnum.SIXTY_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())) {
            fiveTypeIndicator = Constants.FIVE_TYPE_IND_TWO;
        } else if (Objects.equals(CycleDueEnum.NINETY_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())) {
            fiveTypeIndicator = Constants.FIVE_TYPE_IND_THREE;
        } else if (Objects.equals(CycleDueEnum.ONE_HUNDRED_TWENTY_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())
                || Objects.equals(CycleDueEnum.ONE_HUNDRED_FIFTY_DAYS.getCode(), accountManagementInfoDTO.getCycleDue())) {
            fiveTypeIndicator = Constants.FIVE_TYPE_IND_FOUR;
        } else if (accountManagementInfoDTO.getCycleDue().compareTo(CycleDueEnum.ONE_HUNDRED_EIGHTY_DAYS.getCode()) >= 0) {
            fiveTypeIndicator = Constants.FIVE_TYPE_IND_FIVE;
        }
        return fiveTypeIndicator;
    }


}
