package com.anytech.anytxn.transaction.service;


import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.central.base.domain.dto.MarkUpFeeGlamsBO;
import com.anytech.anytxn.central.base.domain.dto.PricingRuleDTO;
import com.anytech.anytxn.central.service.rule.PricingRuleService;
import com.anytech.anytxn.central.base.domain.dto.LabelPricingDTO;
import com.anytech.anytxn.central.base.domain.dto.MarkUpDccFeeDTO;
import com.anytech.anytxn.central.service.common.impl.MarkUpFeeGlamsServiceImpl;
import com.anytech.anytxn.common.core.constants.RespCodePrefix;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.core.enums.RuleTypeEnum;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.authorization.domain.dto.TransFeeParamDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardVirtualAccountInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.card.service.ICardAcctCustReleationService;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountLockException;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.base.transaction.domain.bo.TransRecordResultBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.SettlementLogMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.business.dao.transaction.model.SettlementLog;
import com.anytech.anytxn.limit.base.domain.dto.TokenVirtualDTO;
import com.anytech.anytxn.limit.base.service.ITokenVirtualInfoService;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTransFeeDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IParmTransFeeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyConversionFeeDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyConversionFeeService;
import com.anytech.anytxn.parameter.base.common.enums.TransFeeEnum;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.bo.AccountBO;
import com.anytech.anytxn.transaction.base.domain.bo.CardBO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.constants.Constants;
import com.anytech.anytxn.transaction.base.enums.PostMethodEnum;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.domain.dto.RecordedReqDTO;
import com.anytech.anytxn.transaction.base.domain.dto.VATAPaymentDTO;
import com.anytech.anytxn.transaction.base.domain.dto.VaTaTransferDTO;
import com.anytech.anytxn.transaction.base.domain.dto.VaWithdrawalDTO;
import com.anytech.anytxn.transaction.base.domain.dto.VataRepaymentDTO;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.AuthMatchIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.InputMethodEnum;
import com.anytech.anytxn.transaction.base.enums.RecordedEnum;
import com.anytech.anytxn.transaction.base.enums.ReleaseAuthAmountEnum;
import com.anytech.anytxn.transaction.base.enums.RepostFromSuspendEnum;
import com.anytech.anytxn.transaction.base.enums.ReverseFeeIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.VaAcctStatusEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.enums.TransactionRepDetailEnum;
import com.anytech.anytxn.transaction.base.service.IRecordedService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.service.virtual.VataFrozenAmountServiceImpl;
import com.anytech.anytxn.transaction.service.virtual.VataProcessServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;




/**
 * 交易入账
 *
 * <AUTHOR>
 * @date 2018-11-16
 **/
@Service
@Slf4j
public class RecordedServiceImpl implements IRecordedService {
    private static final Logger logger = LoggerFactory.getLogger(RecordedServiceImpl.class);

    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private VataProcessServiceImpl vataProcessService;
    @Autowired
    private SettlementLogMapper settlementLogMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private IRecordedService recordedService;
    @Resource
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Resource
    private ParmAcctProductMainInfoSelfMapper acctProductMainInfoSelfMapper;
    @Resource
    private ITokenVirtualInfoService tokenVirtualInfoService;
    @Resource
    private PricingRuleService pricingRuleService;
    @Resource
    private ICurrencyConversionFeeService currencyConversionFeeService;
    @Resource
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;
    @Resource
    private CardVirtualAccountInfoSelfMapper cardVirtualAccountInfoSelfMapper;
    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Resource
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Resource
    private MarkUpFeeGlamsServiceImpl markUpFeeGlamsService;
    @Resource
    private VataFrozenAmountServiceImpl vataFrozenAmountService;
    @Resource
    private PartitionKeyInitService partitionKeyInitService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Autowired
    private IParmTransFeeService parmTransFeeService;

    @Autowired
    private TransactionFeeServiceImpl transactionFeeService;

    @Autowired
    private ICardAcctCustReleationService cardAcctCustReleationService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse corporateRecoreded(RecordedReqDTO recordedReqDTO) {

        String inputMethod = recordedReqDTO.getInputMethod();
        if (org.springframework.util.StringUtils.isEmpty(inputMethod)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INPUT_METHOD_NOT_EXIST);
        } else {
            if ("A".equals(inputMethod)) {
                String txnAccountManageId = recordedReqDTO.getTxnAccountManageId();
                if (org.springframework.util.StringUtils.isEmpty(txnAccountManageId)) {
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INPUT_CORRECT_ACCT_ID);
                }
                AccountManagementInfo managementInfo = accountManagementInfoMapper.
                        selectByPrimaryKey(txnAccountManageId);
                if (managementInfo == null) {
                    logger.error("Account management info not found: accountManageId={}", txnAccountManageId);
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_NOT_EXIST_ACCT_ID);
                }
                String productNumber = managementInfo.getProductNumber();
                if (!org.springframework.util.StringUtils.isEmpty(productNumber)) {
                    ParmAcctProductMainInfo acctProductMainInfo = acctProductMainInfoSelfMapper
                            .selectByOrgNumAndProNum(managementInfo.getOrganizationNumber(),
                                    productNumber);
                    if (acctProductMainInfo == null) {
                        logger.error("Account product not found in system for account management ID: productNumber={}", productNumber);
                        throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_NOT_EXIST_PRODUCT_NUM);
                    }
                    String attribute = acctProductMainInfo.getAttribute();
                    if (!org.springframework.util.StringUtils.isEmpty(attribute)
                            && !"V".equals(attribute) && !"T".equals(attribute)) {
                        logger.error("Input account management ID is not VA account: accountManageId={}, productNumber={}, attribute={}", txnAccountManageId, productNumber, attribute);
                        throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_NOT_VA_ACCT_ID);
                    }
                    String txnTransactionCode = recordedReqDTO.getTxnTransactionCode();
                    if ("VA002".equals(txnTransactionCode)) {
                        BigDecimal billingAmount = recordedReqDTO.getTxnBillingAmount();
                        TokenVirtualDTO tokenVirtualDTO = new TokenVirtualDTO();
                        tokenVirtualDTO.setSearchType("A");
                        tokenVirtualDTO.setSearchNumber(txnAccountManageId);
                        TokenVirtualDTO virtualDTO = tokenVirtualInfoService.queryAcctInfo(tokenVirtualDTO);
                        BigDecimal frozenAmount = virtualDTO.getFrozenAmount().max(BigDecimal.ZERO);
                        BigDecimal balance = virtualDTO.getBalance().abs();
                        BigDecimal subtract = balance.subtract(frozenAmount);
                        if (billingAmount.compareTo(subtract) > 0) {
                            logger.error("VA account insufficient balance for transfer: accountManageId={}, frozenAmount={}, availableBalance={}, transferAmount={}",
                                    txnAccountManageId, frozenAmount, balance.subtract(frozenAmount), billingAmount);
                            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_NOT_CORRECT_TRANSFER_AMT);
                        }
                    }
                }
            } else if ("P".equals(inputMethod)) {
                String txnCardNumber = recordedReqDTO.getTxnCardNumber();
                if (org.springframework.util.StringUtils.isEmpty(txnCardNumber)) {
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INPUT_CORRECT_CARD_NUM);
                }
            }
        }

        return recoreded(recordedReqDTO);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse recoreded(RecordedReqDTO recordedReqDTO, boolean responsePostedTransactionId) {
        logger.info("Transaction recording started: txnRefId={}, txnPostMethod={}", recordedReqDTO.getTxnRefId(), recordedReqDTO.getTxnPostMethod());
        
        OrganizationInfoResDTO parameterConfig = getOrganizationInfoResDTO(OrgNumberUtils.getOrg());

        String txnPostMethod = recordedReqDTO.getTxnPostMethod();

        /*
        1、入账方式处理
        1）如果入账方式（txnPostMethod）= 0（实时入账），编辑核心入账接口，调用《核心入账逻辑》。
        2）如果入账方式（txnPostMethod）= 1（批量入账），编辑核心入账接口，并按照《清算流水数据表》格式入库。
        */
        if (RecordedEnum.REAL_TIME.getCode().equals(txnPostMethod)) {
            RecordedBO recorded = buildRecorded(recordedReqDTO, parameterConfig.getNextProcessingDay());
            try {
                // 核心入账处理
                logger.info("Calling core transaction recording service: txnRefId={}", recordedReqDTO.getTxnRefId());
                TransRecordResultBO transRecordResultBO = txnRecordedService.txnRecorded(recorded);
                logger.info("Core transaction recording service completed: txnRefId={}, result={}", recordedReqDTO.getTxnRefId(), transRecordResultBO.getResult());

                // TODO 拒绝交易返回特定信息 update by 2022/08/10
                AnyTxnHttpResponse transactionException = transRecordResultBO.getTransactionException();
                if (Objects.nonNull(transactionException)) {
                    return AnyTxnHttpResponse.fail(transactionException.getCode(), transactionException.getMessage());
                }

                // TODO 拒绝交易 update by 2022/08/25
                if (transRecordResultBO.getResult() == 1) {
                    return AnyTxnHttpResponse.fail("9999999988", "entry rejection, please check the rejection page for details");
                }

                return responsePostedTransactionId ? AnyTxnHttpResponse.success(transRecordResultBO.getPostingTransactionId()) : AnyTxnHttpResponse.success(recorded.getTxnBillingAmount());
            } catch (AnyTxnTransactionException e) {
                logger.error("Core transaction recording failed: txnRefId={}", recordedReqDTO.getTxnRefId(), e);
                throw e;
            }
        } else if (RecordedEnum.BATCH.getCode().equals(txnPostMethod)) {

            CustReconciliationControlDTO control = getCustReconciliationControlDTO(recordedReqDTO);
            // 入账日期与外围无关，入账自己计算
            LocalDate postingDate = getBillingDate(control);
            SettlementLog settlementLog = buildSettlementLog(recordedReqDTO, postingDate);
            try {
                int i = settlementLogMapper.insertSelective(settlementLog);
                if (i != 1) {
                    logger.error("Failed to insert settlement log: expectedRows={}, actualRows={}", 1, i);
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_EFFECT_NUM_ERROR);
                }
                return AnyTxnHttpResponse.success(settlementLog.getTxnBillingAmount());
            } catch (Exception e) {
                logger.error("Failed to insert settlement log: txnRefId={}", recordedReqDTO.getTxnRefId(), e);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR, e);

            }
        }
        return null;
    }

    public LocalDate getBillingDate(CustReconciliationControlDTO controlDTO) {

        if (controlDTO == null) {
            logger.error("Customer reconciliation control info not found");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }

        OrganizationInfoResDTO organizationInfoResDTO = getOrganizationInfoResDTO(controlDTO.getOrganizationNumber());
        LocalDate accruedDay = organizationInfoResDTO.getAccruedThruDay();
        LocalDate today = organizationInfoResDTO.getToday();
        LocalDate nextProcessingDate = organizationInfoResDTO.getNextProcessingDay();

        return custReconciliationControlService.getBillingDate(controlDTO, accruedDay, today, nextProcessingDate);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = AnyTxnTransactionException.class)
    public AnyTxnHttpResponse feeWaiverEntry(RecordedReqDTO recordedReqDTO) {
        /*        //检查防重id
        int existsByTxnRefId = postedTransactionSelfMapper.isExistsByTxnRefId(recordedReqDTO.getTxnRefId());
        if (existsByTxnRefId > 0) {
            logger.error("Fee waiver entry - txnRefId already exists in recorded transaction details: txnRefId={}, cardNumber={}, accountManageId={}", recordedReqDTO.getTxnRefId(), recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_TXN_REF_ID_EXIST);
        }
        //检查交易码是否为借记
        TransactionCodeResDTO paramTransactionCode = transactionCodeService.findTransactionCode(OrgNumberUtils.getOrg(), recordedReqDTO.getTxnTransactionCode());
        if (!DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode().equals(paramTransactionCode.getDebitCreditIndicator())) {
            logger.error("feeWaiver入账-交易码非借记, 交易码: {}", recordedReqDTO.getTxnTransactionCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_TXN_REF_ID_EXIST);
        }*/
        return recoreded(recordedReqDTO, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse recoreded(RecordedReqDTO recordedReqDTO) {
        return this.recoreded(recordedReqDTO, false);
    }

    @Override
    public AnyTxnHttpResponse appTransactionEntry(RecordedReqDTO recordedReqDTO) {
        //检查防重id
        int existsByTxnRefId = postedTransactionSelfMapper.isExistsByTxnRefId(recordedReqDTO.getTxnRefId());
        if (existsByTxnRefId > 0) {
            logger.error("App transaction entry - txnRefId already exists in recorded transaction details: txnRefId={}, cardNumber={}, accountManageId={}", recordedReqDTO.getTxnRefId(), recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_EXIST);
        }
        return recoreded(recordedReqDTO, true);
    }


    private OrganizationInfoResDTO getOrganizationInfoResDTO(String organizationNumber) {
        OrganizationInfoResDTO parameterConfig = organizationInfoService.findOrganizationInfo(organizationNumber);
        if (parameterConfig == null) {
            logger.error("Failed to get organization info: organizationNumber={}", OrgNumberUtils.getOrg());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }

        return parameterConfig;
    }


    @Override
    public SettlementLog buildSettlementLog(RecordedReqDTO recordedReqDTO, LocalDate txnBillingDate) {
        String txnCardNumber = recordedReqDTO.getTxnCardNumber();
        SettlementLog settlementLog = new SettlementLog();
        // 管理账户Id
        String txnAccountManageId = recordedReqDTO.getTxnAccountManageId();
        settlementLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        settlementLog.setTxnAccountManageId(txnAccountManageId);
        //	父级交易账户ID
        settlementLog.setTxnParentTxnAccountId(recordedReqDTO.getTxnParentTransactionAccountId());
        // 调整交易的原交易余额信息id
        settlementLog.setTxnOriginalTxnBalanceId(recordedReqDTO.getTxnOriginalTransactionBalanceId());
        // 卡号,初始化0
        settlementLog.setTxnCardNumber(txnCardNumber);
        // 拒绝重入账标志
        settlementLog.setTxnRepostFromSuspend(recordedReqDTO.getTxnRepostFromSuspend());
        // 原始交易日期
        settlementLog.setTxnOriginalTxnDate(recordedReqDTO.getTxnOriginalTransactionDate());
        // 原全局业务流水号
        settlementLog.setTxnOriginalGlobalFlowNum(recordedReqDTO.getTxnOriginalGlobalFlowNumber());
        // 全局业务流水号
        settlementLog.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        // 入账方式
        settlementLog.setTxnPostMethod(recordedReqDTO.getTxnPostMethod());
        // 冲减交易费用标识
        settlementLog.setTxnReverseFeeIndicator(recordedReqDTO.getTxnReverseFeeIndicator());
        // 交易码
        settlementLog.setTxnTransactionCode(recordedReqDTO.getTxnTransactionCode());
        // 交易来源.默认本地
        if (StringUtils.isEmpty(recordedReqDTO.getTxnTransactionSource())) {
            settlementLog.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        } else {
            settlementLog.setTxnTransactionSource(recordedReqDTO.getTxnTransactionSource());
        }
        // 交易描述
        settlementLog.setTxnTransactionDescription(recordedReqDTO.getTxnTransactionDescription());
        // 交易日期
        settlementLog.setTxnTransactionDate((recordedReqDTO.getTxnTransactionDate().atTime(LocalTime.now())));
        // 交易金额
        settlementLog.setTxnTransactionAmount(recordedReqDTO.getTxnTransactionAmount());
        // 交易币种,交易级账户的币种（currency）
        settlementLog.setTxnTransactionCurrency(recordedReqDTO.getTxnTransactionCurrency());
//        //入账日期
        settlementLog.setTxnBillingDate(txnBillingDate);
        // 入账金额
        settlementLog.setTxnBillingAmount(recordedReqDTO.getTxnBillingAmount());
        // 入账币种
        settlementLog.setTxnBillingCurrency(recordedReqDTO.getTxnBillingCurrency());
        // 清算金额
        settlementLog.setTxnSettlementAmount(recordedReqDTO.getTxnSettlementAmount());
        // 清算币种
        settlementLog.setTxnSettlementCurrency(recordedReqDTO.getTxnSettlementCurrency());
        // 汇率
        settlementLog.setTxnExchangeRate(recordedReqDTO.getTxnExchangeRate());
        // 邮编
        settlementLog.setTxnZipCode(recordedReqDTO.getTxnZipCode());
        // 商户编号
        settlementLog.setTxnMerchantId(recordedReqDTO.getTxnMerchantId());
        // 商户名称
        settlementLog.setTxnMerchantName(recordedReqDTO.getTxnMerchantName());
        // MCC
        settlementLog.setTxnMerchantCategoryCode(recordedReqDTO.getTxnMerchantCategoryCode());
        // 国家码
        settlementLog.setTxnCountryCode(recordedReqDTO.getTxnCountryCode());
        // 省份/州
        settlementLog.setTxnStateCode(recordedReqDTO.getTxnStateCode());
        // 城市
        settlementLog.setTxnCityCode(recordedReqDTO.getTxnCityCode());
        // 参考号
        settlementLog.setTxnReferenceNumber(recordedReqDTO.getTxnReferenceNumber());
        // 授权匹配标志,0=未匹配授权
        settlementLog.setTxnAuthMatchIndicator(recordedReqDTO.getTxnAuthorizationMatchIndicator());
        // 是否恢复授权占用额度标志,N:不需要
        settlementLog.setTxnReleaseAuthAmount(recordedReqDTO.getTxnReleaseAuthorizationAmount());
        // 授权额度占用金额
        settlementLog.setTxnOutstandingAmount(recordedReqDTO.getTxnOutstandingAmount());
        // 授权交易对应额度节点编号
        settlementLog.setTxnLimitNodeId(recordedReqDTO.getTxnLimitNodeId());
        // 交易对手银行号
        settlementLog.setTxnOpponentBankNum(recordedReqDTO.getTxnOpponentBankNumber());
        // 交易对手账户号
        settlementLog.setTxnOpponentAccountNum(recordedReqDTO.getTxnOpponentAccountNumber());
        // 交易对手账户名称
        settlementLog.setTxnOpponentAccountName(recordedReqDTO.getTxnOpponentAccountName());
        // 二级商户号
        settlementLog.setTxnSecondMerchantId(recordedReqDTO.getTxnSecondMerchantId());
        // 二级商户名称
        settlementLog.setTxnSecondMerchantName(recordedReqDTO.getTxnSecondMerchantName());
        // POS输入方式
        settlementLog.setTxnPosEntryMode(recordedReqDTO.getTxnPosEntryMode());
        // Visa ISA标识
        settlementLog.setTxnVisaChargeFlag(recordedReqDTO.getTxnVisaChargeFlag());
        // RA标识
        settlementLog.setTxnReimbursementAttribute(recordedReqDTO.getTxnReimbursementAttribute());
        // IFI标识
        settlementLog.setTxnIfiIndicator(recordedReqDTO.getTxnIfiIndicator());
        // PSV标识
        settlementLog.setTxnPsvIndicator(recordedReqDTO.getTxnPsvIndicator());
        // DCC标识
        settlementLog.setTxnDccIndicator(recordedReqDTO.getTxnDccIndicator());
        // 强制入账标识
        settlementLog.setTxnForcePostIndicator(recordedReqDTO.getTxnForcePostIndicator());
        // 降级交易标识
        settlementLog.setTxnFallBackIndicator(recordedReqDTO.getTxnFallBackIndicator());
        // 分期标识
        settlementLog.setTxnInstallmentIndicator(recordedReqDTO.getTxnInstallmentIndicator());
        // 分期订单号
        settlementLog.setTxnInstallmentOrderId(recordedReqDTO.getTxnInstallmentOrderId());
        // 分期期数
        settlementLog.setTxnInstallmentTerm(recordedReqDTO.getTxnInstallmentTerm());
        // 利率参数表id
        settlementLog.setTxnInterestTableId(recordedReqDTO.getTxnInterestTableId());
        // 费用参数表id
        settlementLog.setTxnFeeTableId(recordedReqDTO.getTxnFeeTableId());
        settlementLog.setCreateTime(LocalDateTime.now());
        settlementLog.setUpdateTime(LocalDateTime.now());
        settlementLog.setMkUpFeeInd(recordedReqDTO.getMkUpFeeInd());
        settlementLog.setUpdateBy("");
        settlementLog.setVersionNumber(1L);

        String customerId;
        int partitionKey;
        if (org.springframework.util.StringUtils.isEmpty(txnCardNumber)) {
            AccountManagementInfo managementInfo = accountManagementInfoMapper
                    .selectByPrimaryKey(txnAccountManageId);
            if ("C".equals(managementInfo.getLiability())) {
                customerId = managementInfo.getCorporateCustomerId();
            } else {
                customerId = managementInfo.getCustomerId();
            }
            partitionKey = partitionKeyInitService.partitionKeyGenerator(null, managementInfo);
        } else {
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper
                    .selectByPrimaryKey(txnCardNumber, OrgNumberUtils.getOrg());
            if ("C".equals(cardAuthorizationInfo.getLiability())) {
                customerId = cardAuthorizationInfo.getCorporateCustomerId();
            } else {
                if (TransactionConstants.PRIMARY_INDICATOR.equals(cardAuthorizationInfo
                        .getRelationshipIndicator())) {
                    // 主卡
                    customerId = cardAuthorizationInfo.getPrimaryCustomerId();
                } else {
                    // 附卡
                    customerId = cardAuthorizationInfo.getSupplementaryCustomerId();
                }
            }
            CardAuthorizationDTO cardAuthorizationDTO = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
            partitionKey = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);
        }

        settlementLog.setCustomerId(customerId);
        settlementLog.setPartitionKey(partitionKey);

        // TODO 资产渠道ID
        settlementLog.setTxnChannelId(recordedReqDTO.getTxnChannelId());
        // 资产子渠道ID
        settlementLog.setTxnSubChannelId(recordedReqDTO.getTxnSubChannelId());
        // 资金源ID
        settlementLog.setTxnFundId(recordedReqDTO.getTxnFundId());
        settlementLog.setTxnExchangeRate(recordedReqDTO.getTxnExchangeRate());
        if (StringUtils.equals(TransactionSourceEnum.TOKEN_VA_BY_APP.getCode(),
                recordedReqDTO.getTxnTransactionSource())) {
            settlementLog.setJsonReserved(JSON.toJSONString(recordedReqDTO));
        }
        return settlementLog;
    }

    /**
     * @param recordedReqDTO 交易录入请求参数
     * @param txnBillingDate 入账日
     * @return Recorded
     */
    @Override
    public RecordedBO buildRecorded(RecordedReqDTO recordedReqDTO, LocalDate txnBillingDate) {
        RecordedBO recorded = new RecordedBO();
        // 是否返回拒绝入账信息
        recorded.setReturnRejectInfo(recordedReqDTO.isReturnRejectInfo());
        // 卡号
        recorded.setTxnCardNumber(recordedReqDTO.getTxnCardNumber());
        // 管理账户
        recorded.setTxnAccountManageId(recordedReqDTO.getTxnAccountManageId());
        // 原全局业务流水号
        recorded.setTxnOriginalGlobalFlowNumber(recordedReqDTO.getTxnOriginalGlobalFlowNumber());
        // 原交易ID
        recorded.setTxnOriginalPostedTransactionId(recordedReqDTO.getOriginalPostedTransactionId());
        // 全局业务流水号
        recorded.setTxnGlobalFlowNumber(StringUtils.isBlank(recordedReqDTO.getTxnGlobalFlowNumber()) ? sequenceIdGen.generateId(TenantUtils.getTenantId()) :
                recordedReqDTO.getTxnGlobalFlowNumber());
        // 入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        // 拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        // 冲减交易费用标识
        recorded.setTxnReverseFeeIndicator(ReverseFeeIndicatorEnum.NO.getCode());
        // 交易码
        recorded.setTxnTransactionCode(recordedReqDTO.getTxnTransactionCode());
        recorded.setTxnTransactionCode(recordedReqDTO.getTxnTransactionCode());
        boolean flag = recorded.getTxnTransactionCode().startsWith("VA");
        String transactionSource = recordedReqDTO.getTxnTransactionSource() == null
                ? "" : recordedReqDTO.getTxnTransactionSource();
        // 如果是VA开头的交易码并且交易来源不为6/8/9/T/F/O/G/P
        // 或者交易来源未上送 将交易来源赋值为0-手工交易录入
        // 其他情况则赋值为上送的交易来源
        if ((flag && !StringUtils.equalsAny(transactionSource,
                TransactionSourceEnum.TRIGGER_BY_APP.getCode(),
                TransactionSourceEnum.PAYNOW_BY_APP.getCode(),
                TransactionSourceEnum.DBS_VA_BY_APP.getCode(),
                TransactionSourceEnum.TOKEN_VA_BY_APP.getCode(),
                TransactionSourceEnum.FOMO_VA_BY_CMS.getCode(),
                TransactionSourceEnum.OVERSEAS_TT.getCode(),
                TransactionSourceEnum.GIRO.getCode(),
                TransactionSourceEnum.MEPS.getCode(),
                TransactionSourceEnum.DVAULT_TRANSFER_TO_DVAULT.getCode(),
                TransactionSourceEnum.TRIPLE_A.getCode()
        ))
                || org.springframework.util.StringUtils.isEmpty(transactionSource)) {
            recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        } else {
            recorded.setTxnTransactionSource(transactionSource);
        }
        if (StringUtils.isNotEmpty(recordedReqDTO.getTxnTransactionDescription())) {
            // 交易描述
            recorded.setTxnTransactionDescription(recordedReqDTO.getTxnTransactionDescription());
        } else {
            TransactionCodeResDTO paramTransactionCode = transactionCodeService.findTransactionCode(OrgNumberUtils.getOrg(), recordedReqDTO.getTxnTransactionCode());
            if (paramTransactionCode != null) {
                // 交易描述
                recorded.setTxnTransactionDescription(paramTransactionCode.getDescription());
                if (StringUtils.isNotEmpty(recordedReqDTO.getTxnTransactionCode())
                        && ("DC001".equals(recordedReqDTO.getTxnTransactionCode()) || "DC003".equals(recordedReqDTO.getTxnTransactionCode()))) {
                    recorded.setTxnTransactionDescription(paramTransactionCode.getDescription() + " - " + recordedReqDTO.getTxnBillingAmount());
                }
            }
        }
        // 交易日期
        recorded.setTxnTransactionDate(recordedReqDTO.getTxnTransactionDate().atTime(LocalTime.now()));

        recorded.setTxnTransactionTime(recordedReqDTO.getTxnTransactionTime());
        // 交易金额
        recorded.setTxnTransactionAmount(recordedReqDTO.getTxnTransactionAmount());
        // 交易币种,交易级账户的币种（currency）
        recorded.setTxnTransactionCurrency(recordedReqDTO.getTxnTransactionCurrency());
        // 入账日期
        recorded.setTxnBillingDate(txnBillingDate);
        // 入账金额
        recorded.setTxnBillingAmount(recordedReqDTO.getTxnBillingAmount());
        // 入账币种
        recorded.setTxnBillingCurrency(recordedReqDTO.getTxnBillingCurrency());
        // 清算金额
        if (recordedReqDTO.getMarkupFeeAmount() != null) {
            recorded.setTxnSettlementAmount(recordedReqDTO.getTxnBillingAmount().add(recordedReqDTO.getMarkupFeeAmount()));
        } else {
            recorded.setTxnSettlementAmount(recordedReqDTO.getTxnBillingAmount());
        }
        // 清算币种
        recorded.setTxnSettlementCurrency(recordedReqDTO.getTxnBillingCurrency());
        // 商户编号
        recorded.setTxnMerchantId(recordedReqDTO.getTxnMerchantId());
        // 商户名称
        recorded.setTxnMerchantName(recordedReqDTO.getTxnMerchantName());
        // MCC
        recorded.setTxnMerchantCategoryCode(recordedReqDTO.getTxnMerchantCategoryCode());
        // 国家码
        recorded.setTxnCountryCode(recordedReqDTO.getTxnCountryCode());
        // 城市
        recorded.setTxnCityCode(recordedReqDTO.getTxnCityCode());
        // 授权匹配标志,0=未匹配授权
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        // 是否恢复授权占用额度标志,N:不需要
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        // 强制入账标识
        recorded.setTxnForcePostIndicator(recordedReqDTO.getTxnForcePostIndicator());
        // 调整交易的原交易ID
        recorded.setTxnOriginalTransactionBalanceId(recordedReqDTO.getTxnOriginalTransactionBalanceId());
        // 原始交易日期
        if (recordedReqDTO.getTxnOriginalTransactionDate() != null) {
            recorded.setTxnOriginalTransactionDate(recordedReqDTO.getTxnOriginalTransactionDate().atTime(LocalTime.now()));
        }
        // TODO 交易录入赋值
        // 资产渠道ID
        recorded.setTxnChannelId(recordedReqDTO.getTxnChannelId());
        // 资产子渠道ID
        recorded.setTxnSubChannelId(recordedReqDTO.getTxnSubChannelId());
        // 资金源ID
        recorded.setTxnFundId(recordedReqDTO.getTxnFundId());
        recorded.setTxnReferenceNumber(recordedReqDTO.getTxnReferenceNumber());
        recorded.setTxnRefId(recordedReqDTO.getTxnRefId());
        recorded.setTxnExchangeRate(recordedReqDTO.getTxnExchangeRate());
        recorded.setMkUpFeeInd(recordedReqDTO.getMkUpFeeInd());
        recorded.setOverPaymentTransInd(recordedReqDTO.getOverPaymentTransInd());
        if (!StringUtils.isEmpty(recordedReqDTO.getTxnInstallmentOrderId())){
            recorded.setTxnInstallmentOrderId(recordedReqDTO.getTxnInstallmentOrderId());
        }
        if (!StringUtils.isEmpty(recordedReqDTO.getTxnInstallmentIndicator())){
            recorded.setTxnInstallmentIndicator(recordedReqDTO.getTxnInstallmentIndicator());
        }
        return recorded;
    }

    private boolean invalidAmount(BigDecimal bigDecimal) {
        return bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * VA虚拟账户充值
     *
     * @return AnyTxnHttpResponse
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse virtualRecharge(RecordedReqDTO recordedReqDTO) {
        recordedReqDTO.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        //VA充值手续费金额
        BigDecimal txnVaTopUpFee = recordedReqDTO.getTxnVaTopUpFee();
        String transactionCurrency = recordedReqDTO.getTxnTransactionCurrency();
        String billingCurrency = recordedReqDTO.getTxnBillingCurrency();
        String accountManageId = recordedReqDTO.getTxnAccountManageId();

        // 交易金额 防重id 入账币种校验
        parameterCheck(recordedReqDTO);

        if (txnVaTopUpFee == null || txnVaTopUpFee.compareTo(BigDecimal.ZERO) < 0) {
            txnVaTopUpFee = BigDecimal.ZERO;
        }

        AnyTxnHttpResponse httpResponse;
        BigDecimal actualAmount = recordedReqDTO.getTxnBillingAmount();
        BigDecimal markupFeeAmount = BigDecimal.ZERO;
        BigDecimal markUpFeeRate = BigDecimal.ZERO;

//        if (!(StringUtils.equals(TransactionSourceEnum.TOKEN_VA_BY_APP.getCode(), recordedReqDTO.getTxnTransactionSource())
//                || StringUtils.equals(recordedReqDTO.getTxnTransactionSource(), TransactionSourceEnum.FOMO_VA_BY_CMS.getCode()))) {
//            // 区分交易来源 交易币种检查
//            checkCurrency(transactionCurrency, accountManageId);
//        }

        // 交易币种和入账币种不一致
        String txnBillSameCurFlag = Constants.YES;
        if(!transactionCurrency.equals(billingCurrency)){
            if (Objects.isNull(recordedReqDTO.getTxnExchangeRate())) {
                logger.error("TOKEN channel VA recharge exchange rate is empty: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_PARAM_CONVERT_FEE_ERROR);
            }
            txnBillSameCurFlag = Constants.NO;
        }

        //txn VA充值费
        String txnCardNumber = recordedReqDTO.getTxnCardNumber();
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(txnCardNumber,recordedReqDTO.getOrganizationNumber());
        TransactionCodeResDTO transactionCodeResDTO = transactionCodeService.findTransactionCode(recordedReqDTO.getOrganizationNumber(), recordedReqDTO.getTxnTransactionCode());
                LabelPricingDTO tranFeeLabelPricingDTO = pricingRuleService.executeRule(PricingRuleDTO.builder()
                .cardProductNum(cardAuthorizationInfo.getProductNumber())
                .debitCreditIndicator(transactionCodeResDTO.getDebitCreditIndicator())
                .transactionAttribute(transactionCodeResDTO.getTransactionAttribute())
                .transactionCode(recordedReqDTO.getTxnTransactionCode())
                .txnBillSameCur(txnBillSameCurFlag)
                .transactionCode( recordedReqDTO.getTxnTransactionCode())
                .ruleType(RuleTypeEnum.RECHARGE_FEE_PARAMETER_RULES.getRuleType())
                .build());
        ParmTransFeeDTO parmRechargeFeeInfo = null;
        if(null !=tranFeeLabelPricingDTO && StringUtils.isNotEmpty(tranFeeLabelPricingDTO.getTableId())){
            parmRechargeFeeInfo = parmTransFeeService.findByOrgAndTableId(cardAuthorizationInfo.getOrganizationNumber(),  tranFeeLabelPricingDTO.getTableId(), TransFeeEnum.getFeeType(RuleTypeEnum.RECHARGE_FEE_PARAMETER_RULES.getRuleType()));
        }
        BigDecimal rechargeFeeAmount = BigDecimal.ZERO;
        if(null != parmRechargeFeeInfo){
            TransFeeParamDTO feeParameter = TransFeeParamDTO.builder()
                    .chargeOption(parmRechargeFeeInfo.getChargeOption())
                    .fixedAmnt(parmRechargeFeeInfo.getFixedAmount())
                    .chargePercent(parmRechargeFeeInfo.getFeeRate())
                    .minAmnt(parmRechargeFeeInfo.getMinAmount())
                    .maxAmnt(parmRechargeFeeInfo.getMaxAmount())
                    .build();
            //充值费
            rechargeFeeAmount = transactionFeeService.transactionFee(recordedReqDTO.getTxnBillingAmount(), feeParameter, false);
            //费用合并标志，0：不合并 1：合并
            if(StringUtils.equals(Constants.S_ONE, parmRechargeFeeInfo.getFeeMergeFlag())){
                actualAmount = actualAmount.subtract(rechargeFeeAmount);
            }

        }
        //判断VA实际充值的金额是否比费用金额大
        BigDecimal feeTotalAmount = markupFeeAmount.add(rechargeFeeAmount);
        if (txnVaTopUpFee.compareTo(recordedReqDTO.getTxnBillingAmount().subtract(feeTotalAmount)) > 0) {
            //VA充值上送的手续费大于入账金额
            logger.error("VA recharge transaction fee is greater than received amount: cardNumber={}, accountManageId={}, txnVaTopUpFee={}, actualAmount={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId(), txnVaTopUpFee, actualAmount);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_ERROR,
                    TransactionRepDetailEnum.PAYMENT_TXN_VA_TOP_UP_FEE_BILLING_AMOUNT);
        }

        recordedReqDTO.setTxnBillingAmount(actualAmount);

        recordedReqDTO.setTxnBillingAmount(actualAmount);
        recordedReqDTO.setTxnSettlementAmount(actualAmount);
        recordedReqDTO.setMarkupFeeAmount(markupFeeAmount);
        recordedReqDTO.setMarkupFeeRate(markUpFeeRate);


        // 返回响应给App端的入账金额 txn将手续费单独入账了 这里为了兼容app端的入账金额
        BigDecimal txnActualVOAmount = actualAmount.subtract(txnVaTopUpFee);
        // 本金入账
        httpResponse = recoreded(recordedReqDTO);
        VATAPaymentDTO vataPaymentDTO = new VATAPaymentDTO(txnActualVOAmount, markUpFeeRate, markupFeeAmount, txnVaTopUpFee);
        httpResponse.setData(vataPaymentDTO);

        if (RespCodePrefix.NORMAL_RESP.equals(httpResponse.getCode())) {
            if(null != parmRechargeFeeInfo && rechargeFeeAmount.compareTo(BigDecimal.ZERO) >0){
                //txn VA充值费
                vaRecharageFeeRecord(recordedReqDTO, parmRechargeFeeInfo.getFeeMergeFlag(),parmRechargeFeeInfo.getTransactionCode() ,rechargeFeeAmount);
            }

        }

        // 手续费为0时 本金入账后返回
        if (txnVaTopUpFee.compareTo(BigDecimal.ZERO) == 0) {
            return httpResponse;
        }
        if (RespCodePrefix.NORMAL_RESP.equals(httpResponse.getCode())) {
            // VA充值手续费入账
            recordedReqDTO.setTxnTransactionCode(TransactionConstants.TA_VA_PAYMENT_FEE_AMOUNT);
            recordedReqDTO.setTxnTransactionCurrency(billingCurrency);
            // PC014: 交易 入账 清算金额 三者保持统一 汇率置1
            recordedReqDTO.setMarkupFeeAmount(BigDecimal.ZERO);
            recordedReqDTO.setTxnBillingAmount(txnVaTopUpFee);
            recordedReqDTO.setTxnSettlementAmount(recordedReqDTO.getTxnBillingAmount());
            recordedReqDTO.setTxnTransactionAmount(recordedReqDTO.getTxnBillingAmount());
            recordedReqDTO.setTxnExchangeRate(BigDecimal.ONE);
            recordedReqDTO.setTxnAccountManageId(accountManageId);
            recordedReqDTO.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            httpResponse = recoreded(recordedReqDTO);
            httpResponse.setData(vataPaymentDTO);
            return httpResponse;
        }
        return httpResponse;
    }

    private AnyTxnHttpResponse vaRecharageFeeRecord(RecordedReqDTO recordedReqDTO, String feeMergeFlag, String transactionCode,BigDecimal rechargeFeeAmount) {
        String billingCurrency = recordedReqDTO.getTxnBillingCurrency();
        AnyTxnHttpResponse httpResponse = null;
        if(!StringUtils.equals(Constants.S_ONE, feeMergeFlag)){
            //txn的VA充值手续费入账
            // VA充值手续费入账
            recordedReqDTO.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            recordedReqDTO.setTxnTransactionCode(transactionCode);
            recordedReqDTO.setTxnTransactionCurrency(billingCurrency);
            recordedReqDTO.setMarkupFeeAmount(BigDecimal.ZERO);
            recordedReqDTO.setTxnBillingAmount(rechargeFeeAmount);
            recordedReqDTO.setTxnSettlementAmount(rechargeFeeAmount);
            recordedReqDTO.setTxnTransactionAmount(rechargeFeeAmount);
            recordedReqDTO.setTxnExchangeRate(BigDecimal.ONE);
            recordedReqDTO.setTxnTransactionSource(TransactionSourceEnum.INNER_FEE.getCode());
            httpResponse = recoreded(recordedReqDTO);
        }
        return httpResponse;
    }

    /**
     * 交易金额 入账金额 refId 入账币种 参数校验
     *
     * @param recordedReqDTO
     */
    private void parameterCheck(RecordedReqDTO recordedReqDTO) {
        String txnRefId = recordedReqDTO.getTxnRefId();
        String accountManageId = recordedReqDTO.getTxnAccountManageId();
        String txnBillingCurrency = recordedReqDTO.getTxnBillingCurrency();

        if(StringUtils.isBlank(recordedReqDTO.getTxnTransactionCode())){
            logger.error("Transaction code cannot be empty: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TRANSACTION_DATE_NULL);
        }

        if(StringUtils.isBlank(recordedReqDTO.getInputMethod())){
            logger.error("Input method cannot be empty: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INPUT_METHOD_NOT_EXIST);
        }else {
            if(StringUtils.equals(InputMethodEnum.CARD_INPUT.getCode(),recordedReqDTO.getInputMethod()) && StringUtils.isEmpty(recordedReqDTO.getTxnCardNumber())){
                logger.error("Card number cannot be empty");
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INPUT_CORRECT_CARD_NUM);
            }
        }

        if(StringUtils.isBlank(accountManageId)){
            logger.error("Account management ID cannot be empty: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_ID_IS_EMPTY);
        }

        if (invalidAmount(recordedReqDTO.getTxnBillingAmount()) && invalidAmount(recordedReqDTO.getTxnTransactionAmount())) {
            //VA充值上送的入账金额为0
            logger.error("VA recharge received amount is 0: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TRANS_AMOUNT_REJECT);
        }

        if (StringUtils.isBlank(txnRefId)) {
            //VA充值上送的refId为空
            logger.error("VA recharge refId is empty: cardNumber={}, accountManageId={}", recordedReqDTO.getTxnCardNumber(), recordedReqDTO.getTxnAccountManageId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_NOT_NULL);
        }

        int existsByTxnRefId = postedTransactionSelfMapper.isExistsByTxnRefId(txnRefId);
        if (existsByTxnRefId > 0) {
            //txnRefId在已入账交易明细表中已存在
            logger.error("TxnRefId already exists in recorded transaction details: txnRefId={}, cardNumber={}, accountManageId={}", txnRefId, recordedReqDTO.getTxnCardNumber(), accountManageId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_EXIST);
        }

        String currencyByAccountMan = accountManagementInfoMapper.selCurrencyByAccountManId(accountManageId);
        if (!StringUtils.equals(currencyByAccountMan, txnBillingCurrency)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCT_CURRENCY_NOT_EXIST);
        }
    }

    /**
     * 交易币种和管理账户币种须一致
     *
     * @param txnTransactionCurrency
     * @param accountManageId
     */
    private void checkCurrency(String txnTransactionCurrency, String accountManageId) {
        String currencyByAccountManId = accountManagementInfoMapper.selCurrencyByAccountManId(accountManageId);
        if (StringUtils.isNotBlank(currencyByAccountManId)) {
            if (!currencyByAccountManId.equals(txnTransactionCurrency)) {
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_ERROR);
            }
        }
    }

    private void buildMarkFeeGlamsRecord(RecordedReqDTO recordedReqDTO, BigDecimal feeAmount) {

        if (feeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        recordedReqDTO.setMkUpFeeInd("M");

        MarkUpFeeGlamsBO markUpFeeGlamsBO = new MarkUpFeeGlamsBO();
        markUpFeeGlamsBO.setTxnSource(recordedReqDTO.getTxnTransactionSource());

        markUpFeeGlamsBO.setBillingAmtwithNoFee(recordedReqDTO.getTxnBillingAmount()
                .add(feeAmount));

        markUpFeeGlamsBO.setMarkUpFee(feeAmount);
        markUpFeeGlamsBO.setAccountManagerId(recordedReqDTO.getTxnAccountManageId());


        RecordedBO recorded = new RecordedBO();
        recorded.setTxnTransactionCode(recordedReqDTO.getTxnTransactionCode());
        recorded.setTxnAccountManageId(recordedReqDTO.getTxnAccountManageId());
        recorded.setExtraCardNumber(recordedReqDTO.getExtraCardNumber());
        String cardNumber = vataFrozenAmountService.cardFounder(null, recorded);


        SettlementLog settlementLog = new SettlementLog();
        settlementLog.setTxnGlobalFlowNumber(recordedReqDTO.getTxnGlobalFlowNumber());
        settlementLog.setTxnTransactionCode(recordedReqDTO.getTxnTransactionCode());
        settlementLog.setTxnMerchantId(recordedReqDTO.getTxnMerchantId());
        settlementLog.setTxnCardNumber(cardNumber);
        settlementLog.setTxnChannelId(recordedReqDTO.getTxnChannelId());
        settlementLog.setTxnTransactionDescription(recordedReqDTO.getTxnTransactionDescription());
        settlementLog.setTxnExchangeRate(recordedReqDTO.getTxnExchangeRate());
        settlementLog.setTxnTransactionCurrency(recordedReqDTO.getTxnTransactionCurrency());
        settlementLog.setTxnBillingCurrency(recordedReqDTO.getTxnBillingCurrency());
        settlementLog.setTxnBillingAmount(recordedReqDTO.getTxnBillingAmount());

        settlementLog.setTxnTransactionAmount(recordedReqDTO.getTxnTransactionAmount());
        settlementLog.setTxnTransactionDate(recordedReqDTO.getTxnTransactionDate().atTime(LocalTime.now()));

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(recordedReqDTO.getTxnAccountManageId());
        String vaNumber = Optional.ofNullable(accountManagementInfo.getVaNumber()).orElse("");
        markUpFeeGlamsBO.setVaNumber(vaNumber);


        CustReconciliationControlDTO control = custReconciliationControlService.getControl(
                accountManagementInfo.getCustomerId(), recordedReqDTO.getOrganizationNumber());


        settlementLog.setTxnBillingDate(getBillingDate(control));

        settlementLog.setTxnReferenceNumber(recordedReqDTO.getTxnReferenceNumber());
        settlementLog.setTxnTransactionSource(recordedReqDTO.getTxnTransactionSource());

        markUpFeeGlamsService.generateMkUpGlams(settlementLog, markUpFeeGlamsBO, "C", "D");

    }


    /**
     * va充值
     * @param recordedReqDTO
     * @return
     */
    private CurrencyConversionFeeDTO getCurrencyConverFee(RecordedReqDTO recordedReqDTO) {

        String schema = "";
        String groupType = "";
//        CardVirtualAccountInfo cardVirtualAccountInfo = cardVirtualAccountInfoSelfMapper.selectByAccountManagementId(recordedReqDTO.getTxnAccountManageId());
//        if (!Objects.isNull(cardVirtualAccountInfo)
//                && "1".equals(cardVirtualAccountInfo.getBindStatus())
//                && StringUtils.isNotBlank(cardVirtualAccountInfo.getCustomerId())) {
//
//            //归户后不一定只关联到一个卡组的卡片，所以卡组的参数查询无意义
//            /*CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerIdAndCardProNum
//                    (cardVirtualAccountInfo.getCustomerId(), cardVirtualAccountInfo.getCardProductNumber());
//            if (ObjectUtils.isNotEmpty(cardAuthorizationInfo)) {
//                schema = cardAuthorizationInfo.getCardScheme();
//            }*/
//            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId
//                    (recordedReqDTO.getOrganizationNumber(), cardVirtualAccountInfo.getCustomerId());
//            if (customerAuthorizationInfo != null) {
//                groupType = customerAuthorizationInfo.getGroupType();
//            }
//        }
        AccountManagementInfo vaAccountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(recordedReqDTO.getTxnAccountManageId());
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId
                    (recordedReqDTO.getOrganizationNumber(), vaAccountManagementInfo.getCustomerId());
            if (customerAuthorizationInfo != null) {
                groupType = customerAuthorizationInfo.getGroupType();
            }
        // 虚拟账户卡组从绑定信息查询,卡组 客群非必传因子?
        LabelPricingDTO labelPricingDTO = pricingRuleService.executeMarkUpFeeAndDccFeeRuleEntity(MarkUpDccFeeDTO.builder()
                .cardScheme(schema)
                .groupType(groupType)
                .ruleType(RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType())
                .cardNumber(recordedReqDTO.getTxnCardNumber())
                .accountManagementId(recordedReqDTO.getTxnAccountManageId())
                .customerId(recordedReqDTO.getCustomerId())
                .transactionSource(recordedReqDTO.getTxnTransactionSource())
                .build());

        String txnTransactionCode = recordedReqDTO.getTxnTransactionCode();
        ParmTransactionCode parmTransactionCode = transactionCodeSelfMapper.selectByOrgNumberAndCode(recordedReqDTO.getOrganizationNumber(), txnTransactionCode);
        if (parmTransactionCode == null) {
            logger.error("Transaction code parameter not found: organizationNumber={}, transactionCode={}", recordedReqDTO.getOrganizationNumber(), txnTransactionCode);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_ERROR);
        }

        // 新增方法不调用原MC DCC费逻辑 入参?方法内公共逻辑为不影响原各渠道交易费不抽取
        String markUpFeeTableId = Objects.nonNull(labelPricingDTO) ? labelPricingDTO.getTableId() : null;
        BigDecimal markupFeeRate = Objects.nonNull(labelPricingDTO) ? labelPricingDTO.getLabelFxFee() : BigDecimal.ZERO;

        return currencyConversionFeeService.paymentFeeProcess(CurrencyConversionFeeDTO.builder()
                .transactionCurrency(recordedReqDTO.getTxnTransactionCurrency())
                .billingCurrency(recordedReqDTO.getTxnBillingCurrency())
                .transactionAmount(recordedReqDTO.getTxnTransactionAmount())
                .groupType(groupType)
                .organizationNumber(recordedReqDTO.getOrganizationNumber())
                .debitCreditIndicator(parmTransactionCode.getDebitCreditIndicator())
                .billingAmount(recordedReqDTO.getTxnBillingAmount())
                .transactionSource(recordedReqDTO.getTxnTransactionSource())
                .markUpFeeTableId(markUpFeeTableId)
                .markupFeeRate(markupFeeRate)
                .build());

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class,
            AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse virtualManualRepayment(VataRepaymentDTO vataRepaymentDTO) {
        logger.info("Virtual manual repayment started: txnRefId={}", vataRepaymentDTO.getTxnRefId());
        checkRefId(vataRepaymentDTO.getTxnRefId(), vataRepaymentDTO);
        logger.info("Calling vata repayment processor service: txnRefId={}", vataRepaymentDTO.getTxnRefId());
        vataProcessService.vataRepaymentProcessor(vataRepaymentDTO);
        logger.info("Vata repayment processor service completed: txnRefId={}", vataRepaymentDTO.getTxnRefId());
        return AnyTxnHttpResponse.success();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class,
            AnyTxnCustAccountLockException.class})
    public AnyTxnHttpResponse virtualTransfer(VaTaTransferDTO vaTaTransferDTO) {
        logger.info("Virtual transfer started: txnRefId={}", vaTaTransferDTO.getTxnRefId());
        checkRefId(vaTaTransferDTO.getTxnRefId(), vaTaTransferDTO);
        logger.info("Calling vata transfer processor service: txnRefId={}", vaTaTransferDTO.getTxnRefId());
        vataProcessService.vaTaTransferProcessor(vaTaTransferDTO);
        logger.info("Vata transfer processor service completed: txnRefId={}", vaTaTransferDTO.getTxnRefId());
        return AnyTxnHttpResponse.success();
    }

    @Override
    public AnyTxnHttpResponse virtualWithdrawal(VaWithdrawalDTO vaWithdrawalDTO) {
        logger.info("VA withdrawal started: txnRefId={}", vaWithdrawalDTO.getTxnRefId());
        vaWithdrawalDTO.setTransactionCode(TransactionConstants.VA_WITHDRAWAL);

        // 在VA提现撤销的时候使用
        vaWithdrawalDTO.setWithdrawalReversalInd(VaAcctStatusEnum.UN_REVOKED_STATUS.getCode());

        checkRefId(vaWithdrawalDTO.getTxnRefId(), vaWithdrawalDTO);
        logger.info("Calling vata withdrawal processor service: txnRefId={}", vaWithdrawalDTO.getTxnRefId());
        VaWithdrawalDTO vaWithdrawal = vataProcessService.vaWithdrawalProcessor(vaWithdrawalDTO);
        logger.info("Vata withdrawal processor service completed: txnRefId={}", vaWithdrawalDTO.getTxnRefId());

        return AnyTxnHttpResponse.success(vaWithdrawal);
    }

    @Override
    public AnyTxnHttpResponse<?> virtualWithdrawalReversal(VaWithdrawalDTO vaWithdrawalDTO) {
        logger.info("VA withdrawal reversal started: txnRefId={}", vaWithdrawalDTO.getTxnRefId());
        vaWithdrawalDTO.setTransactionCode(TransactionConstants.VA_WITHDRAWAL_REVERSAL);

        // VA提现撤销标识
        vaWithdrawalDTO.setWithdrawalReversalInd(VaAcctStatusEnum.REVOKED_STATUS.getCode());

        checkRefId(vaWithdrawalDTO.getTxnRefId(), vaWithdrawalDTO);
        // 原交易校验
        checkOriRecord(vaWithdrawalDTO);

        logger.info("Calling vata withdrawal processor service for reversal: txnRefId={}", vaWithdrawalDTO.getTxnRefId());
        VaWithdrawalDTO vaWithdrawal = vataProcessService.vaWithdrawalProcessor(vaWithdrawalDTO);
        logger.info("Vata withdrawal processor service for reversal completed: txnRefId={}", vaWithdrawalDTO.getTxnRefId());
        // 在撤销成功后 对多次提现的撤销做控制
        postedTransactionSelfMapper.updateIndicatorByOriRefId(vaWithdrawalDTO.getWithdrawalReversalInd(), vaWithdrawalDTO.getOriRefId());

        return AnyTxnHttpResponse.success(vaWithdrawal);
    }

    /**
     * 防重检查
     *
     * @param txnRefId String
     */
    private void checkRefId(String txnRefId, Object o) {
        if (txnRefId == null || "".equals(txnRefId)) {
            if (o instanceof VataRepaymentDTO) {
                VataRepaymentDTO vataRepaymentDTO = (VataRepaymentDTO) o;
                logger.error("TA/VA manual repayment: duplicate prevention ID cannot be empty, cardNumber={}, caAccountManageId={}, vaAccountManageId={}", vataRepaymentDTO.getCaCardNumber(), vataRepaymentDTO.getCaAccountManagementId(), vataRepaymentDTO.getVaAccountManagementId());
            } else if (o instanceof VaTaTransferDTO) {
                VaTaTransferDTO vaTaTransferDTO = (VaTaTransferDTO) o;
                logger.error("TA/VA transfer: duplicate prevention ID cannot be empty, sourceAccountManageId={}, targetAccountManageId={}", vaTaTransferDTO.getSourceAccountManagementId(), vaTaTransferDTO.getTargetAccountManagementId());
            } else if (o instanceof VaWithdrawalDTO) {
                VaWithdrawalDTO vaWithdrawalDTO = (VaWithdrawalDTO) o;
                if (TransactionConstants.VA_WITHDRAWAL.equals(vaWithdrawalDTO.getTransactionCode())) {
                    logger.error("TA/VA withdrawal: duplicate prevention ID cannot be empty, vaAccountManageId={}", vaWithdrawalDTO.getVaAccountManagementId());
                } else if (TransactionConstants.VA_WITHDRAWAL_REVERSAL.equals(vaWithdrawalDTO.getTransactionCode())) {
                    logger.error("TA/VA withdrawal reversal: duplicate prevention ID cannot be empty, vaAccountManageId={}", vaWithdrawalDTO.getVaAccountManagementId());
                }
            } else {
                logger.error("TA/VA transaction: duplicate prevention ID cannot be empty");
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_NOT_NULL);
        }
        int existsByTxnRefId = postedTransactionSelfMapper.isExistsByTxnRefId(txnRefId);
        if (existsByTxnRefId > 0) {
            if (o instanceof VataRepaymentDTO) {
                VataRepaymentDTO vataRepaymentDTO = (VataRepaymentDTO) o;
                logger.error("TA/VA manual repayment: txnRefId already exists in recorded transaction details, txnRefId={}, cardNumber={}, caAccountManageId={}, vaAccountManageId={}", vataRepaymentDTO.getTxnRefId(), vataRepaymentDTO.getCaCardNumber(), vataRepaymentDTO.getCaAccountManagementId(), vataRepaymentDTO.getVaAccountManagementId());
            } else if (o instanceof VaTaTransferDTO) {
                VaTaTransferDTO vaTaTransferDTO = (VaTaTransferDTO) o;
                logger.error("TA/VA transfer: txnRefId already exists in recorded transaction details, txnRefId={}, sourceAccountManageId={}, targetAccountManageId={}", vaTaTransferDTO.getTxnRefId(), vaTaTransferDTO.getSourceAccountManagementId(), vaTaTransferDTO.getTargetAccountManagementId());
            } else if (o instanceof VaWithdrawalDTO) {
                VaWithdrawalDTO vaWithdrawalDTO = (VaWithdrawalDTO) o;
                if (TransactionConstants.VA_WITHDRAWAL.equals(vaWithdrawalDTO.getTransactionCode())) {
                    logger.error("TA/VA withdrawal: txnRefId already exists in recorded transaction details, txnRefId={}, vaAccountManageId={}", vaWithdrawalDTO.getTxnRefId(), vaWithdrawalDTO.getVaAccountManagementId());
                } else if (TransactionConstants.VA_WITHDRAWAL_REVERSAL.equals(vaWithdrawalDTO.getTransactionCode())) {
                    logger.error("TA/VA withdrawal reversal: txnRefId already exists in recorded transaction details, txnRefId={}, vaAccountManageId={}", vaWithdrawalDTO.getTxnRefId(), vaWithdrawalDTO.getVaAccountManagementId());
                }
            } else {
                logger.error("TA/VA transaction: txnRefId already exists in recorded transaction details: txnRefId={}", txnRefId);
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_EXIST);
        }
    }

    /**
     * VA Withdrawal原交易检查
     *
     * @param vaWithdrawalDTO VaWithdrawalDTO
     */
    private void checkOriRecord(VaWithdrawalDTO vaWithdrawalDTO) {
        LocalDateTime oriTransactionDate = vaWithdrawalDTO.getOriTransactionDate();
        String oriRefId = vaWithdrawalDTO.getOriRefId();
        String vaAccountManagementId = vaWithdrawalDTO.getVaAccountManagementId();
        BigDecimal billingAmount = vaWithdrawalDTO.getBillingAmount();

        if (Objects.isNull(oriTransactionDate)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL, TransactionRepDetailEnum.ORI_TRANS_DATE_NULL);
        }

        if (oriRefId == null || oriRefId.isEmpty()) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL, TransactionRepDetailEnum.ORI_REF_ID_NULL);
        }
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByOriRefIdTransDateCardNbr(
                oriTransactionDate, oriRefId, vaAccountManagementId);
        if (CollectionUtils.isEmpty(postedTransactions)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.ORI_REF_ID_NOT_EXIST,
                    oriRefId, oriTransactionDate, vaAccountManagementId);
        }

        // 若多次提现撤销过来 进行多次提现的拦截
        PostedTransactionDTO postedTransactionDTO = BeanMapping.copy(vaWithdrawalDTO, PostedTransactionDTO.class);
        postedTransactionDTO.setAccountManagementId(vaWithdrawalDTO.getVaAccountManagementId());
        postedTransactionDTO.setTxnRefId(vaWithdrawalDTO.getOriRefId());
        PostedTransactionDTO postedTransDetails = recordedService.getByOrgAndOther(postedTransactionDTO);
        if (postedTransDetails != null) {
            if (postedTransDetails.getWithdrawalReversalInd().equals(VaAcctStatusEnum.REVOKED_STATUS.getCode())) {
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_ERROR, TransactionRepDetailEnum.WITHDRAWAL_REVERSAL_IND);
            }
        }

        PostedTransaction postedTransaction = postedTransactions.get(0);
        BigDecimal postingAmount = postedTransaction.getPostingAmount();
        if (billingAmount.compareTo(postingAmount) != 0) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.AMT_NOT_EQUAL,
                    postingAmount, billingAmount, vaAccountManagementId);
        }
    }

    private CustReconciliationControlDTO getCustReconciliationControlDTO(RecordedReqDTO recordedReqDTO) {
        // 管理账户号
        String txnAccountManageId = recordedReqDTO.getTxnAccountManageId();
        RecordedMidBO rootRecorded = new RecordedMidBO();
        String customerId;
        String organizationNumber;
        // >> 管理账户号加载区
        if (StringUtils.isNotEmpty(txnAccountManageId)) {
            // 加载管理账户
            AccountManagementInfoDTO accountManagementInfo = null;
            // 非批中处理
            if (!CustAccountBO.isBatch()) {
                if (CustAccountBO.isFromAuth()) {
                    // 授权的入账请求从缓存中获取
                    accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByPrimaryKey(txnAccountManageId);
                } else {
                    if (CustAccountBO.threadCustAccountBO.get() != null
                            && CustAccountBO.threadCustAccountBO.get().getCustomerBO() != null) {
                        accountManagementInfo = CustAccountBO.threadCustAccountBO.get()
                                .getCustomerBO().getManagementById(txnAccountManageId);
                    }
                    if (null == accountManagementInfo) {
                        AccountManagementInfo model = accountManagementInfoMapper.selectByPrimaryKey(txnAccountManageId);
                        accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);
                    }
                }
                // 批中处理
            } else {
                accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByPrimaryKey(txnAccountManageId);
            }
            if (accountManagementInfo == null) {
                if (logger.isDebugEnabled()) {
                    logger.debug("Failed to get account management info, entry rejected: accountManageId={}", txnAccountManageId);
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_MANAGEMENT_REJECT);
            }
            if (rootRecorded.getAccountBO() == null) {
                rootRecorded.setAccountBO(new AccountBO());
            }
            rootRecorded.getAccountBO().setAccountManagementInfo(accountManagementInfo);
//            rootRecorded.getRecorded().setCorporateCustomerId(accountManagementInfo.getCorporateCustomerId());
            // 机构号
            organizationNumber = accountManagementInfo.getOrganizationNumber();
            // 客户号
            customerId = LiabilityEnum.CORPORATE.getCode().equals(accountManagementInfo.getLiability()) ? accountManagementInfo.getCorporateCustomerId() : accountManagementInfo.getCustomerId();
            // >> 卡号加载区
        } else {
            String txnCardNumber = recordedReqDTO.getTxnCardNumber();
            if (StringUtils.isEmpty(txnCardNumber)) {
                if (logger.isDebugEnabled()) {
                    logger.debug("Entry parameters missing card number info, entry rejected");
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_REJECT);
            }
            // 加载卡授权
            CardAuthorizationDTO cardAuthorizationInfo = null;
            if (StringUtils.isNotEmpty(txnCardNumber)) {
                // 非批中，使用mapper查询
                if (!CustAccountBO.isBatch()) {
                    if (CustAccountBO.isFromAuth()) {
                        // 授权过来的入账请求，数据从缓存中获取
                        cardAuthorizationInfo = CustAccountBO.threadCustAccountBO.get().getAuthBO().getCardAuthorizationByCarNum(txnCardNumber);

                    } else {
                        CardAuthorizationInfo model = cardAuthorizationInfoMapper.selectByPrimaryKey(txnCardNumber, OrgNumberUtils.getOrg());
                        cardAuthorizationInfo = model == null ? null : BeanMapping.copy(model, CardAuthorizationDTO.class);
                    }
                    // 批中直接读缓存
                } else {
                    cardAuthorizationInfo = txnRecordedAccountCacheService.cardAuthorizationSelectByPrimaryKey(txnCardNumber);
                }
            }
            if (cardAuthorizationInfo == null) {
                if (logger.isDebugEnabled()) {
                    logger.debug("Failed to get card authorization, entry rejected: cardNumber={}", txnCardNumber);
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_REJECT);
            }
            rootRecorded.setCardBO(new CardBO(cardAuthorizationInfo));
            // 读取卡授权中的机构号和客户号
            CardAuthorizationDTO cardAuthorization = rootRecorded.getCardBO().getAuthInfo();
            // 机构号
            organizationNumber = cardAuthorization.getOrganizationNumber();
            // 客户号
            customerId = LiabilityEnum.CORPORATE.getCode().equals(cardAuthorization.getLiability()) ? cardAuthorization.getCorporateCustomerId() : cardAuthorization.getPrimaryCustomerId();
        }

        // 预加载对账表(此步骤优先其他数据加载，如果后续入账拒绝，不影响对账表更新操作)
        return custReconciliationControlService.getControl(customerId, organizationNumber);
    }

    @Override
    public PostedTransactionDTO getByOrgAndOther(PostedTransactionDTO postedTransactionDTO) {
        String accountManagementId = postedTransactionDTO.getAccountManagementId();
        String txnRefId = postedTransactionDTO.getTxnRefId();
        LocalDateTime transactionDate = postedTransactionDTO.getTransactionDate();
        String organizationNumber = postedTransactionDTO.getOrganizationNumber();
        if (StringUtils.isBlank(accountManagementId)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_ID_IS_EMPTY);
        }
        if (StringUtils.isBlank(txnRefId)) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_REF_ID_NOT_NULL);
        }
        PostedTransaction postedTransaction = postedTransactionSelfMapper.selectPostedTransactionByOrgAndOther(organizationNumber, accountManagementId, txnRefId, transactionDate);
        if (!Objects.isNull(postedTransaction)) {
            return BeanMapping.copy(postedTransaction, PostedTransactionDTO.class);
        }
        return null;
    }


}
