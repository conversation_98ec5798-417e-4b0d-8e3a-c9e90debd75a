package com.anytech.anytxn.transaction.service;

import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.dao.transaction.mapper.DisputedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.SettleOutTransactionLogMapper;
import com.anytech.anytxn.business.dao.transaction.model.SettleOutTransactionLog;
import com.anytech.anytxn.central.service.common.CmServerTypeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.enums.ServerTypeEnum;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.dto.DisputeRegisterAndReleaseReqDTO;
import com.anytech.anytxn.transaction.base.enums.DisputeMethodEnum;
import com.anytech.anytxn.transaction.base.enums.MessageIndicatorEnum;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.transaction.base.service.IDisputeTransactionService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountLockException;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.base.transaction.domain.dto.DisputedTransactionDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.DisputedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.model.DisputedTransaction;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.transaction.base.constants.Constants;
import com.anytech.anytxn.transaction.base.enums.DisputeAllowCodeEnum;
import com.anytech.anytxn.transaction.base.enums.DisputeStatusEnum;
import com.anytech.anytxn.transaction.base.enums.ReleaseStatusEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;

import  java.util.stream.Collectors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 争议处理
 *
 * <AUTHOR>
 * @date 2018--11--9:49
 **/
@Service
public class DisputeTansactionServiceImpl implements IDisputeTransactionService {

    private static final Logger logger = LoggerFactory.getLogger(DisputeTansactionServiceImpl.class);

    @Autowired
    private PostedTransactionMapper postedTransactionMapper;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private DisputedTransactionMapper disputedTransactionMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private DisputedTransactionSelfMapper disputedTransactionSelfMapper;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    @Autowired
    private SettleOutTransactionLogMapper settleOutTransactionLogMapper;
    @Autowired
    private CmServerTypeService cmServerTypeService;

    @Override
    public List<DisputedTransactionDTO> getDisputedList(String managementId,
                                                        String status) {

        List<DisputedTransaction> disputedTransactions = disputedTransactionSelfMapper.selectByManagementId(
                managementId);

        return disputedTransactions.stream().filter(
                t -> t.getReleaseStatus().equals(status)).
                map(t -> BeanMapping.copy(t, DisputedTransactionDTO.class)).
                collect(Collectors.toList());
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public void disputeTransRegistrationProcess(String postedTransactionId,String disputeReason) {
        logger.info("Dispute processing - Dispute registration, received parameter postedTransactionId: [{}]", postedTransactionId);
        if (StringUtils.isBlank(postedTransactionId)) {
            logger.error("Posted transaction ID required for dispute registration cannot be empty");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_POSTED_TRANSACTION_ID_NULL);
        }
        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(postedTransactionId);
        if (postedTransaction == null) {
            logger.error("Original transaction does not exist, postedTransactionId: {}", postedTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_POSTED_TRANSACTION_NOT_EXIST);
        }
        if (postedTransaction.getBillingAmount().compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("Zero amount transactions do not allow disputes");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ZERO_AMOUNT_DISPUTE_NOT_ALLOW);
        }
        boolean bool = DisputeStatusEnum.DISPUTE.getCode().equals(
                postedTransaction.getDisputeStatus()) || DisputeStatusEnum.TO_BANK.getCode().equals(
                postedTransaction.getDisputeStatus()) || DisputeStatusEnum.TO_CUSTOM.getCode().equals(
                postedTransaction.getDisputeStatus());
        if (bool) {
            logger.error("Duplicate disputes not allowed");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_REPEAT_DISPUTE);
        }
        logger.info("Dispute processing - Dispute registration, obtaining transaction code parameter table, upload parameters posting_transaction_code: [{}], organizationNumber: [{}]",
                 postedTransaction.getPostingTransactionCode(),
                 postedTransaction.getOrganizationNumber());
        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", postedTransaction.getOrganizationNumber(), postedTransaction.getPostingTransactionCode());
        TransactionCodeResDTO parmTransactionCode = transactionCodeService.findTransactionCode( postedTransaction.getOrganizationNumber(),postedTransaction.getPostingTransactionCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", parmTransactionCode != null ? "found" : "not found");
        if ( parmTransactionCode==null ) {
            logger.error("Failed to get transaction code parameter table, organization: {}, transaction code: {}",postedTransaction.getOrganizationNumber(),postedTransaction.getPostingTransactionCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }
        /**
         *  a. 如果（dispute_allow_code = '1'），表示允许争议，继续后续处理。如果（dispute_allow_code = '2'），表示不允许争议，报错（此交易码不允许争议），结束整个<争议登记>处理。
         */
        if (DisputeAllowCodeEnum.NOT_ALLOW.getCode().equals(parmTransactionCode.getDisputeAllowCode()) ||
                StringUtils.isBlank(parmTransactionCode.getDisputeAllowCode())) {
            logger.error("This transaction code does not allow disputes");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_DISPUTE_TRANS_CODE);
        }
        logger.info("Calling accountManagementInfoMapper.selectByPrimaryKey: accountManagementId={}", postedTransaction.getAccountManagementId());
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(postedTransaction.getAccountManagementId());
        logger.info("AccountManagementInfoMapper.selectByPrimaryKey completed: result={}", accountManagementInfo != null ? "found" : "not found");

        if ( accountManagementInfo==null ) {
            logger.error("Management account not found, accountManagementId: {}", postedTransaction.getAccountManagementId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_NOT_EXIST);
        }

        logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", accountManagementInfo.getOrganizationNumber());
        OrganizationInfoResDTO parmOrganizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
        logger.info("OrganizationInfoService.findOrganizationInfo completed: result={}", parmOrganizationInfo != null ? "found" : "not found");
        if ( parmOrganizationInfo==null  ) {
            logger.error("Organization parameters do not exist, organizationNumber: {}", accountManagementInfo.getOrganizationNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_ORG_NOT_EXIST);
        }
        /**  争议登记交易入账处理  */
        disputeTransPostedProcess(postedTransaction, parmTransactionCode, parmOrganizationInfo,
                                  accountManagementInfo);
        /**   创建争议交易记录表  */
        addDisputedTransaction(postedTransaction,
                               accountManagementInfo.getAccountManagementId(),disputeReason);
        /**   更新原交易明细的状态,修改原入账交易明细表中的争议状态为1（ dispute_status = '1'） */
        postedTransaction.setDisputeStatus("1");
        updatePostedTransaction(postedTransaction);
    }

    /**
     * @param disputedTransactionId
     * @param releaseMethod
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public void disputeTransReleaseProcess(String disputedTransactionId,
                                           String releaseMethod,String releaseReason) {
        logger.info("Dispute processing - Dispute release, obtained upload parameters: disputedTransactionId: [{}], dispute release method: [{}]",
                 disputedTransactionId, releaseMethod);
        if (StringUtils.isBlank(disputedTransactionId)) {
            logger.error("Dispute transaction ID for dispute release cannot be empty");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DISPUTED_TRANS_ID_IS_NULL);
        }
        if (!Constants.RELEASE_C.equals(releaseMethod) && !Constants.RELEASE_B.equals(
                releaseMethod)) {
            logger.error("Dispute release method error, release method: {}",releaseMethod);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DISPUTED_TRANS_RELEASE_METHOD_ERROR);
        }
        DisputedTransaction disputedTransaction = queryDisputeById(disputedTransactionId);
        if ( disputedTransaction==null ) {
            logger.error("Original disputed transaction does not exist, dispute id: {}",disputedTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_DISPUTE_TRANS_NOT_EXSIT);
        }
        /**
         * 判断争议交易表中的释放状态（release_status = '0', 表示争议交易目前状态为争议中，未释放；release_status = '1'，
         *    表示争议交易已释放给发卡行；release_status = '2'，表示争议交易已释放给持卡人）。
         *    如果是已释放的交易（release_status = '1'  or  ‘2’），则报错（不允许重复释放），结束整个<争议释放>处理。
         */
        if (ReleaseStatusEnum.RELEASE_TO_CUSTEMER.getCode().equals(
                disputedTransaction.getReleaseStatus()) || ReleaseStatusEnum.RELEASE_TO_BANK.getCode().equals(
                disputedTransaction.getReleaseStatus())) {
            logger.error("Duplicate release not allowed");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_REPEAT_RELEASE);
        }
        logger.info("Calling postedTransactionMapper.selectByPrimaryKey: postedTransactionId={}", disputedTransaction.getOriginalPostedTransactionId());
        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(
                disputedTransaction.getOriginalPostedTransactionId());
        logger.info("PostedTransactionMapper.selectByPrimaryKey completed: result={}", postedTransaction != null ? "found" : "not found");
        if ( postedTransaction==null ) {
            logger.error("Original transaction details not found, transaction detail id: {}",disputedTransaction.getOriginalPostedTransactionId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_POSTED_TRANS_NOT_EXSIT);
        }
        logger.info("Calling accountManagementInfoMapper.selectByPrimaryKey: accountManagementId={}", disputedTransaction.getAccountManagementId());
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(disputedTransaction.getAccountManagementId());
        logger.info("AccountManagementInfoMapper.selectByPrimaryKey completed: result={}", accountManagementInfo != null ? "found" : "not found");
        if ( accountManagementInfo==null ) {
            logger.error("Management account not found, management account id: {}",disputedTransaction.getAccountManagementId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_NOT_EXIST);
        }
        logger.info("Dispute processing - Dispute release, obtaining transaction code parameter table, upload parameters posting_transaction_code: [{}], organizationNumber: [{}]",
                 postedTransaction.getPostingTransactionCode(),
                 postedTransaction.getOrganizationNumber());

        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", postedTransaction.getOrganizationNumber(), postedTransaction.getPostingTransactionCode());
        TransactionCodeResDTO parmTransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(), postedTransaction.getPostingTransactionCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", parmTransactionCode != null ? "found" : "not found");
        if ( parmTransactionCode==null ) {
            logger.error("Transaction code parameters do not exist");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }
        logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", accountManagementInfo.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
        logger.info("OrganizationInfoService.findOrganizationInfo completed: result={}", organizationInfo != null ? "found" : "not found");
        if ( organizationInfo==null  ) {
            logger.error("Organization parameters do not exist, organization number: {}",accountManagementInfo.getOrganizationNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_ORG_NOT_EXIST);
        }
        /**   争议释放入账处理  */
        disputedTransaction.setDisputeReleaseReason(releaseReason);
        disputedReleasePosted(releaseMethod, postedTransaction, parmTransactionCode,
                organizationInfo, accountManagementInfo,disputedTransaction);
    }


    /**
     * 争议登记或争议释放
     * @param disputeRegisterAndReleaseReqDTO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnTransactionException.class, AnyTxnCustAccountLockException.class})
    public void disputeTransRegisterAndRelease(DisputeRegisterAndReleaseReqDTO disputeRegisterAndReleaseReqDTO) {
//        if(!StringUtils.equals(ServerTypeEnum.CARD_SERVER.getCode(),cmServerTypeService.getServerType())) { TODO
        if(!StringUtils.equals(ServerTypeEnum.CARD_SERVER.getCode(),null)) {

            //非卡服务
            if(StringUtils.equals(DisputeMethodEnum.DISPUTE.getCode(),disputeRegisterAndReleaseReqDTO.getDisputeMethod())){
                //争议登记
                disputeTransRegistrationProcess(disputeRegisterAndReleaseReqDTO.getTransactionId(),disputeRegisterAndReleaseReqDTO.getDisputeReason());
            }else {
                //争议释放
                disputeTransReleaseProcess(disputeRegisterAndReleaseReqDTO.getTransactionId(), DisputeMethodEnum.DISPUTE.getReleaseMethod(disputeRegisterAndReleaseReqDTO.getDisputeMethod()),disputeRegisterAndReleaseReqDTO.getDisputeReason());
            }
        }else {
            if(StringUtils.equals(DisputeMethodEnum.DISPUTE.getCode(),disputeRegisterAndReleaseReqDTO.getDisputeMethod())){
                //卡服务争议登记
                disputeRegistCardServer(disputeRegisterAndReleaseReqDTO.getTransactionId(),disputeRegisterAndReleaseReqDTO.getDisputeReason());
            }else {
                //卡服务争议释放
                disputeReleaseCardServer(disputeRegisterAndReleaseReqDTO.getTransactionId(),DisputeMethodEnum.DISPUTE.getReleaseMethod(disputeRegisterAndReleaseReqDTO.getDisputeMethod()),disputeRegisterAndReleaseReqDTO.getDisputeReason());
            }

        }
    }

    /**
     * 卡服务的争议登记
     * @param transactionId
     * @param disputeReason
     */
    public void disputeRegistCardServer(String transactionId,String disputeReason) {
        //卡服务的争议登记
        logger.info("Dispute Resolution - Dispute Registration, Obtain Upward Parameter TransactionId: [{}]", transactionId);
        if (StringUtils.isBlank(transactionId)) {
            //需要做争议登记的对外入账交易表的id不能为空
            logger.error("The ID required for dispute registration cannot be empty");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_POSTED_TRANS_ID_IS_NULL);
        }
        logger.info("Calling settleOutTransactionLogMapper.selectByPrimaryKey: transactionId={}", transactionId);
        SettleOutTransactionLog settleOutTransactionLog = settleOutTransactionLogMapper.selectByPrimaryKey(transactionId);
        logger.info("SettleOutTransactionLogMapper.selectByPrimaryKey completed: result={}", settleOutTransactionLog != null ? "found" : "not found");
        if ( settleOutTransactionLog==null ) {
            //原交易不存在,
            logger.error("The original transaction does not exist, id: {}",transactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_TRANS_NOT_EXSIT);
        }
        if (settleOutTransactionLog.getBillingAmount().compareTo(BigDecimal.ZERO) <= 0) {
            //零金额交易不允许争议
            logger.error("Zero amount transactions do not allow disputes, id: {}",transactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_DISPUTE_ZERO_AMOUNT);
        }
        boolean bool = DisputeStatusEnum.DISPUTE.getCode().equals(
                settleOutTransactionLog.getDisputeStatus()) || DisputeStatusEnum.TO_BANK.getCode().equals(
                settleOutTransactionLog.getDisputeStatus()) || DisputeStatusEnum.TO_CUSTOM.getCode().equals(
                settleOutTransactionLog.getDisputeStatus());
        if (bool) {
            //不允许重复争议
            logger.error("Duplicate disputes are not allowed, id: {}",transactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_REPEAT_DISPUTE);
        }
        //获取交易码参数表
        logger.info("Dispute Resolution - Dispute Registration, Obtain Transaction Code Parameter Table, Upload Parameter TransactionCode: [{}], OrganizationNumber: [{}]",
                settleOutTransactionLog.getTransactionCode(),
                settleOutTransactionLog.getOrganizationNumber());
        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", settleOutTransactionLog.getOrganizationNumber(), settleOutTransactionLog.getTransactionCode());
        TransactionCodeResDTO parmTransactionCode = transactionCodeService.findTransactionCode( settleOutTransactionLog.getOrganizationNumber(),settleOutTransactionLog.getTransactionCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", parmTransactionCode != null ? "found" : "not found");
        if ( parmTransactionCode==null ) {
            logger.error("Failed to obtain transaction code parameter table, OrganizationNumber: {}, TransactionCode: {}",settleOutTransactionLog.getOrganizationNumber(),settleOutTransactionLog.getTransactionCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }
        /**
         *  a. 如果（dispute_allow_code = '1'），表示允许争议，继续后续处理。如果（dispute_allow_code = '2'），表示不允许争议，报错（此交易码不允许争议），结束整个<争议登记>处理。
         */
        if (DisputeAllowCodeEnum.NOT_ALLOW.getCode().equals(parmTransactionCode.getDisputeAllowCode()) ||
                StringUtils.isBlank(parmTransactionCode.getDisputeAllowCode())) {
            //此交易码不允许争议
            logger.error("This transaction code does not allow disputes, TransactionCode: {}",parmTransactionCode.getTransactionCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_DISPUTE_TRANS_CODE);
        }

        /**  争议登记交易写入对外入账交易表  */
        SettleOutTransactionLog registSettleOutTransactionLog = disputeToSettleOutTran(settleOutTransactionLog, parmTransactionCode);
        settleOutTransactionLogMapper.insert(registSettleOutTransactionLog);

        /**   创建争议交易记录表  */
        PostedTransaction copyPostedTransaction = new PostedTransaction();
        BeanUtils.copyProperties(settleOutTransactionLog, copyPostedTransaction);
        addDisputedTransaction(copyPostedTransaction,null,disputeReason);

        /**   更新原对外入账交易表的状态,修改原对外入账交易表中的争议状态为1（ dispute_status = '1'） */
        settleOutTransactionLog.setDisputeStatus(DisputeStatusEnum.DISPUTE.getCode());
        settleOutTransactionLogMapper.updateByPrimaryKeySelective(settleOutTransactionLog);
    }



    public void disputeReleaseCardServer(String disputedTransactionId,
                                           String releaseMethod,String releaseReason) {
        logger.info("Dispute Resolution - Dispute Release, Obtained Upward Parameters: disputedTransactionId: [{}], releaseMethod: [{}]",
                disputedTransactionId, releaseMethod);
        if (StringUtils.isBlank(disputedTransactionId)) {
            //争议释放的争议交易id不能为空
            logger.error("The dispute transaction ID for dispute release cannot be empty");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DISPUTED_TRANS_ID_IS_NULL);
        }
        if (!Constants.RELEASE_C.equals(releaseMethod) && !Constants.RELEASE_B.equals(
                releaseMethod)) {
            //争议释放的方式错误,释放方式
            logger.error("Wrong way of releasing disputes, releaseMethod: {}",releaseMethod);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DISPUTED_TRANS_RELEASE_METHOD_ERROR);
        }
        DisputedTransaction disputedTransaction = queryDisputeById(disputedTransactionId);
        if ( disputedTransaction==null ) {
            //原争议交易不存在
            logger.error("The original disputed transaction does not exist, disputedTransactionId: {}",disputedTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_DISPUTE_TRANS_NOT_EXSIT);
        }
        /**
         * 判断争议交易表中的释放状态（release_status = '0', 表示争议交易目前状态为争议中，未释放；release_status = '1'，
         *    表示争议交易已释放给发卡行；release_status = '2'，表示争议交易已释放给持卡人）。
         *    如果是已释放的交易（release_status = '1'  or  ‘2’），则报错（不允许重复释放），结束整个<争议释放>处理。
         */
        if (ReleaseStatusEnum.RELEASE_TO_CUSTEMER.getCode().equals(
                disputedTransaction.getReleaseStatus()) || ReleaseStatusEnum.RELEASE_TO_BANK.getCode().equals(
                disputedTransaction.getReleaseStatus())) {
            //不允许重复释放
            logger.error("Do not allow repeated release, disputedTransactionId: {}",disputedTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_REJECT_REPEAT_RELEASE);
        }
        logger.info("Calling settleOutTransactionLogMapper.selectByPrimaryKey: postedTransactionId={}", disputedTransaction.getOriginalPostedTransactionId());
        SettleOutTransactionLog settleOutTransactionLog = settleOutTransactionLogMapper.selectByPrimaryKey(
                disputedTransaction.getOriginalPostedTransactionId());
        logger.info("SettleOutTransactionLogMapper.selectByPrimaryKey completed: result={}", settleOutTransactionLog != null ? "found" : "not found");
        if ( settleOutTransactionLog==null ) {
            //找不到原交易明细
            logger.error("Unable to find original transaction details, postedTransactionId: {}",disputedTransaction.getOriginalPostedTransactionId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_POSTED_TRANS_NOT_EXSIT);
        }

        logger.info("Dispute processing - Dispute release, obtaining transaction code parameter table, upload parameters transaction_code: [{}], organizationNumber: [{}]",
                settleOutTransactionLog.getTransactionCode(),
                settleOutTransactionLog.getOrganizationNumber());

        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", settleOutTransactionLog.getOrganizationNumber(), settleOutTransactionLog.getTransactionCode());
        TransactionCodeResDTO parmTransactionCode = transactionCodeService.findTransactionCode(settleOutTransactionLog.getOrganizationNumber(), settleOutTransactionLog.getTransactionCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", parmTransactionCode != null ? "found" : "not found");
        if ( parmTransactionCode==null ) {
            logger.error("Transaction code parameters do not exist");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }

        /**   争议释放处理  */
        disputedTransaction.setDisputeReleaseReason(releaseReason);
        disputedReleaseToSettleOutTran(releaseMethod, settleOutTransactionLog, parmTransactionCode,disputedTransaction);
    }


    /**
     * 卡服务的争议释放
     * 争议释放到对外入账交易表
     */
    private void disputedReleaseToSettleOutTran(String releaseMethod,
                                       SettleOutTransactionLog settleOutTransactionLog,
                                       TransactionCodeResDTO transactionCodeResDTO,
                                       DisputedTransaction disputedTransaction) {
        if (Constants.RELEASE_C.equals(releaseMethod)) {
            //对持卡人有利的争议释放方式
            settleOutTransactionLog.setDisputeStatus(DisputeStatusEnum.TO_CUSTOM.getCode());
            settleOutTransactionLogMapper.updateByPrimaryKeySelective(settleOutTransactionLog);
            disputedTransaction.setReleaseStatus(ReleaseStatusEnum.RELEASE_TO_BANK.getCode());
            updateDisputedTransaction(disputedTransaction);
        } else {
            //对发卡行有利的争议释放方式
            OrganizationInfoResDTO organizationInfoResDTO = organizationInfoService.findOrganizationInfo(settleOutTransactionLog.getOrganizationNumber());
                    if ( organizationInfoResDTO==null  ) {
            logger.error("Organization parameters are not stored, OrganizationNumber: {}",settleOutTransactionLog.getOrganizationNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_ORG_NOT_EXIST);
        }

            SettleOutTransactionLog releaseToSettleOutTran = disputeToSettleOutTran(settleOutTransactionLog,transactionCodeResDTO);
            releaseToSettleOutTran.setTransactionDate(settleOutTransactionLog.getTransactionDate());

            releaseToSettleOutTran.setTransactionCode(settleOutTransactionLog.getTransactionCode());
            String description;
            if (StringUtils.isNotEmpty(settleOutTransactionLog.getMerchantName())){
                description = "Re-post of Disputed transaction - " + settleOutTransactionLog.getMerchantName() + " " + settleOutTransactionLog.getTransactionDescription();
            }else {
                description = "Re-post of Disputed transaction - " + settleOutTransactionLog.getTransactionDescription();
            }
            releaseToSettleOutTran.setTransactionDescription(description);
            releaseToSettleOutTran.setBillingDate(organizationInfoResDTO.getNextProcessingDay());
            settleOutTransactionLogMapper.insert(releaseToSettleOutTran);


            //更新争议状态
            settleOutTransactionLog.setDisputeStatus(DisputeStatusEnum.TO_BANK.getCode());
            /** 更新交易明细 */
            settleOutTransactionLogMapper.updateByPrimaryKeySelective(settleOutTransactionLog);

            //更新释放状态
            disputedTransaction.setReleaseStatus(ReleaseStatusEnum.RELEASE_TO_CUSTEMER.getCode());
            /** 更新争议交易表 */
            updateDisputedTransaction(disputedTransaction);
        }

    }


    /**
     * 卡服务的争议登记交易写对外入账交易表
     * @param settleOutTransactionLog
     * @param transactionCodeResDTO
     */
    private SettleOutTransactionLog disputeToSettleOutTran(SettleOutTransactionLog settleOutTransactionLog,TransactionCodeResDTO transactionCodeResDTO) {
        SettleOutTransactionLog disputeSettleOutTransactionLog = new SettleOutTransactionLog();
        disputeSettleOutTransactionLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        disputeSettleOutTransactionLog.setOrganizationNumber(settleOutTransactionLog.getOrganizationNumber());
        disputeSettleOutTransactionLog.setCardNumber(settleOutTransactionLog.getCardNumber());
        disputeSettleOutTransactionLog.setCustomerId(settleOutTransactionLog.getCustomerId());
        disputeSettleOutTransactionLog.setEcifNumber(settleOutTransactionLog.getEcifNumber());
        //3:其他模式
        disputeSettleOutTransactionLog.setMessageIndicator(MessageIndicatorEnum.OTHER_INDICATOR.getCode());
        disputeSettleOutTransactionLog.setPostStatus("0");
        disputeSettleOutTransactionLog.setHandleStatus("0");
        disputeSettleOutTransactionLog.setSettlementId("N");
        disputeSettleOutTransactionLog.setTransactionCode(transactionCodeResDTO.getCreditAdjustTxnCode());
        disputeSettleOutTransactionLog.setTransactionSource(TransactionSourceEnum.INNER_FEE.getCode());
        String description;
        if (StringUtils.isNotEmpty(settleOutTransactionLog.getMerchantName())){
            description = "Disputed transaction - " + settleOutTransactionLog.getMerchantName() + " " + settleOutTransactionLog.getTransactionDescription();
        }else {
            description = "Disputed transaction - " + settleOutTransactionLog.getTransactionDescription();
        }
        disputeSettleOutTransactionLog.setTransactionDescription(description);
        disputeSettleOutTransactionLog.setTransactionAmount(settleOutTransactionLog.getTransactionAmount());
        disputeSettleOutTransactionLog.setTransactionCurrency(settleOutTransactionLog.getTransactionCurrency());
        disputeSettleOutTransactionLog.setBillingAmount(settleOutTransactionLog.getBillingAmount());
        disputeSettleOutTransactionLog.setBillingCurrency(settleOutTransactionLog.getBillingCurrency());
        disputeSettleOutTransactionLog.setSettlementAmount(settleOutTransactionLog.getSettlementAmount());
        disputeSettleOutTransactionLog.setSettlementCurrency(settleOutTransactionLog.getSettlementCurrency());
        disputeSettleOutTransactionLog.setAuthDccFee(BigDecimal.ZERO);
        disputeSettleOutTransactionLog.setCashTransFee(BigDecimal.ZERO);
        disputeSettleOutTransactionLog.setAuthMarkupFee(BigDecimal.ZERO);
        disputeSettleOutTransactionLog.setTransactionFee(BigDecimal.ZERO);
        return disputeSettleOutTransactionLog;
    }

    /**
     * 争议登记
     * <逻辑A3> 争议登记交易入账处理
     * 1.组争议登记MEMO交易入账接口，赋值规则见Rule-1，执行<核心入账逻辑>。
     * 2.组贷记调整交易入账接口，赋值规则见Rule-2，执行<核心入账逻辑>。
     */
    private void disputeTransPostedProcess(PostedTransaction postedTransaction,
                                           TransactionCodeResDTO transactionCodeResDTO,
                                           OrganizationInfoResDTO organizationInfoResDTO,
                                           AccountManagementInfo accountManagementInfo) {
        //大莱不需要memo交易产生 所以拿掉
        // 读取对账表
//        Recorded disputeRecorded = buildDisputeRecorded(postedTransaction,transactionCodeResDTO,
//                organizationInfoResDTO,accountManagementInfo);
        /**  核心入账逻辑*/
        logger.info("Dispute processing - Dispute registration MEMO transaction posting - calling core posting logic begin........");
//        coreRecorded(disputeRecorded);
        logger.info("Dispute processing - Dispute registration MEMO transaction posting - calling core posting logic end........");

        RecordedBO creditAdjustmentRecorded = buildCreditAdjustmentRecorded(
                postedTransaction, transactionCodeResDTO, organizationInfoResDTO,
                accountManagementInfo);
        /**  核心入账逻辑*/
        logger.info("Dispute processing - Credit adjustment - calling core posting logic begin.......");
        coreRecorded(creditAdjustmentRecorded);
        logger.info("Dispute processing - Credit adjustment - calling core posting logic end.......");

        // 更新乐观锁
//        custReconciliationControlService.commitLock(control);
    }

    /**
     * 争议登记
     * <逻辑A4> 创建争议交易记录表
     * <p>
     * 创建争议交易记录,   按照赋值规则Rule-3，在disputed_transaction中新增记录。
     */
    private void addDisputedTransaction(PostedTransaction postedTransaction,
                                        String accountManagementId,String disputeReason) {
        DisputedTransaction disputeTransaction = new DisputedTransaction();
        BeanUtils.copyProperties(postedTransaction, disputeTransaction);
        disputeTransaction.setDisputedTransactionId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        disputeTransaction.setAccountManagementId(accountManagementId);
        disputeTransaction.setOriginalPostedTransactionId(
                postedTransaction.getPostedTransactionId());
        disputeTransaction.setReleaseStatus("0");
        disputeTransaction.setOriginalTxnAccountId(postedTransaction.getOriginalTxnAccountId());
        //争议登记原因
        disputeTransaction.setDisputeRegisterReason(disputeReason);
        disputeTransaction.setCreateTime(LocalDateTime.now());
        disputeTransaction.setUpdateBy("");
        disputeTransaction.setUpdateTime(null);
        disputeTransaction.setTransactionDate(postedTransaction.getTransactionDate().toLocalDate());
        disputeTransaction.setVersionNumber(1L);
        try {
            int i = disputedTransactionMapper.insertSelective(disputeTransaction);
            if(i!=1){
                logger.error("Failed to insert table {}, expected affected rows: {}, actual affected rows: {}", "disputeTransaction", 1, i);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_EFFECT_NUM_ERROR);
            }
        } catch (Exception e) {
            logger.error("Dispute processing - calling [{}] insert disputed transaction record table [{}] failed, error message: [{}]",
                      "disputedTransactionMapper.insertSelective", "DISPUTED_TRANSACTION",
                      e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR,e);
        }
    }

    /**
     * 争议释放
     * 三.争议释放入账处理
     */
    private void disputedReleasePosted(String releaseMethod,
                                       PostedTransaction postedTransaction,
                                       TransactionCodeResDTO transactionCodeResDTO,
                                       OrganizationInfoResDTO organizationInfoResDTO,
                                       AccountManagementInfo accountManagementInfo,
                                       DisputedTransaction disputedTransaction) {
        // 读取对账表
//        CustReconciliationControlDTO control = custReconciliationControlService.getControl(accountManagementInfo.getCustomerId(), accountManagementInfo.getOrganizationNumber());

        //大莱项目争议释放不需要生成memo交易 故拿掉该逻辑
        /**
         * 若争议释放方式为ReleaseC,  以对持卡人有利的方式释放，则执行<逻辑A>；若争议释放方式为ReleaseB,以对发卡行有利的方式释放，则执行<逻辑B>。
         */
//        Recorded recorded = buildDisputeReleaseRecorded(postedTransaction,transactionCodeResDTO,
//                organizationInfoResDTO,accountManagementInfo);
        /**  核心入账逻辑*/
        logger.info("Dispute processing - Dispute release, calling core posting - MEMO transaction posting interface logic begin.......");
//        coreRecorded(recorded);
        logger.info("Dispute processing - Dispute release, calling core posting - MEMO transaction posting interface logic end.......");
        if (Constants.RELEASE_C.equals(releaseMethod)) {
            /**
             * 1.组争议释放MEMO交易入账接口，赋值规则见Rule-3，执行<核心入账逻辑>。
             * 2.更新已入账交易信息表（posted_transaction），更新争议交易表（disputed_transaction），此时DISPUTE-STATUS = '3',  RELEASE-STATUS ='2‘，赋值规则见Rule-1，Rule-2.
             */
            postedTransaction.setDisputeStatus("3");
            updatePostedTransaction(postedTransaction);
            disputedTransaction.setReleaseStatus("2");
            updateDisputedTransaction(disputedTransaction);
        } else {
            /**
             * 1.组争议释放MEMO交易入账接口，赋值规则见Rule-3，执行<核心入账逻辑>。
             * 2.组借记入账交易接口，见赋值规则Rule-4，执行执行<核心入账逻辑>。
             * 3.更新已入账交易信息表（posted_transaction），更新争议交易表（disputed_transaction）
             */
            RecordedBO debitRecorded = buildDebitRecorded(postedTransaction,transactionCodeResDTO,
                    organizationInfoResDTO,accountManagementInfo);
            /**  核心入账逻辑*/
            logger.info("Dispute processing - Debit entry - calling core posting interface transaction logic begin........");
            coreRecorded(debitRecorded);
            logger.info("Dispute processing - Debit entry - calling core posting interface transaction logic end.......");
            postedTransaction.setDisputeStatus("2");
            /** 更新交易明细 */
            updatePostedTransaction(postedTransaction);
            disputedTransaction.setReleaseStatus("1");
            /** 更新争议交易表 */
            updateDisputedTransaction(disputedTransaction);
        }

        // 更新乐观锁
//        custReconciliationControlService.commitLock(control);
    }

    /**
     * 组争议登记MEMO交易入账接口(只写文档中非空值)
     * Rule-1:  争议登记MEMO接口赋值
     *
     * @return
     */
    private RecordedBO buildDisputeRecorded(PostedTransaction postedTransaction,
                                          TransactionCodeResDTO transactionCodeResDTO,
                                          OrganizationInfoResDTO organizationInfoResDTO,
                                          AccountManagementInfo accountManagementInfo) {
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnTransactionDescription(postedTransaction.getMerchantName()+" (Disputed transaction : Disputing)");
        recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
        recorded.setTxnCardNumber(postedTransaction.getCardNumber());
        recorded.setTxnOriginalTransactionBalanceId(
                postedTransaction.getTransactionBalanceId());
        recorded.setTxnOriginalTransactionDate(postedTransaction.getTransactionDate());
        recorded.setTxnRepostFromSuspend("0");
        recorded.setTxnPostMethod("0");
        recorded.setTxnReverseFeeIndicator("0");
        recorded.setTxnTransactionCode(transactionCodeResDTO.getDisputeRegisterTxnCode());
        recorded.setTxnTransactionSource("2");
        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", postedTransaction.getOrganizationNumber(), transactionCodeResDTO.getDisputeRegisterTxnCode());
        TransactionCodeResDTO pamRegtransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(),transactionCodeResDTO.getDisputeRegisterTxnCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", pamRegtransactionCode != null ? "found" : "not found");
        //recorded.setTxnTransactionDescription(pamRegtransactionCode.getDescription());
        // 入账日期 and 交易日期由入账逻辑赋值
        recorded.setTxnTransactionAmount(BigDecimal.ZERO);
        recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnBillingAmount(BigDecimal.ZERO);
        recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnSettlementAmount(BigDecimal.ZERO);
        recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnMerchantId(postedTransaction.getMerchantId());
        return recorded;
    }

    /**
     * 组贷记调整交易入账接口
     * Rule-2:  贷调交易入账接口赋值
     *
     * @return
     */
    private RecordedBO buildCreditAdjustmentRecorded(PostedTransaction postedTransaction,
                                                     TransactionCodeResDTO transactionCodeResDTO,
                                                     OrganizationInfoResDTO organizationInfoResDTO,
                                                     AccountManagementInfo accountManagementInfo) {
        RecordedBO recorded = new RecordedBO();
        String description;
        if (StringUtils.isNotEmpty(postedTransaction.getMerchantName())){
            description = "Disputed transaction - " + postedTransaction.getMerchantName() + " " + postedTransaction.getTransactionDescription();
        }else {
            description = "Disputed transaction - " + postedTransaction.getTransactionDescription();
        }
        recorded.setTxnTransactionDescription(description);
        recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
        recorded.setTxnCardNumber(postedTransaction.getCardNumber());
        recorded.setTxnOriginalTransactionBalanceId(
                postedTransaction.getTransactionBalanceId());
        recorded.setTxnOriginalTransactionDate(postedTransaction.getTransactionDate());
        recorded.setTxnPostMethod("0");
        recorded.setTxnRepostFromSuspend("0");
        recorded.setTxnReverseFeeIndicator("0");
        recorded.setTxnTransactionCode(transactionCodeResDTO.getCreditAdjustTxnCode());
        recorded.setTxnTransactionSource("2");
        logger.info("Calling transactionCodeService.findTransactionCode: org={}, transactionCode={}", postedTransaction.getOrganizationNumber(), transactionCodeResDTO.getCreditAdjustTxnCode());
        TransactionCodeResDTO parmAdjustTransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(),transactionCodeResDTO.getCreditAdjustTxnCode());
        logger.info("TransactionCodeService.findTransactionCode completed: result={}", parmAdjustTransactionCode != null ? "found" : "not found");
        //recorded.setTxnTransactionDescription(parmAdjustTransactionCode.getDescription());
        // 入账日期 and 交易日期由入账逻辑赋值
        recorded.setTxnTransactionAmount(postedTransaction.getPostingAmount());
        recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnBillingAmount(postedTransaction.getPostingAmount());
        recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnSettlementAmount(postedTransaction.getPostingAmount());
        recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnAuthorizationMatchIndicator("0");
        recorded.setTxnReleaseAuthorizationAmount("N");
        return recorded;
    }

    /**
     * 组争议释放MEMO交易入账接口
     * Rule-3:  争议释放MEMO交易接口赋值
     *
     * @param postedTransaction
     * @param transactionCodeResDTO
     * @param organizationInfoResDTO
     * @param accountManagementInfo
     * @return
     */
    private RecordedBO buildDisputeReleaseRecorded(PostedTransaction postedTransaction,
                                                 TransactionCodeResDTO transactionCodeResDTO,
                                                 OrganizationInfoResDTO organizationInfoResDTO,
                                                 AccountManagementInfo accountManagementInfo,
                                                 String releaseMethod) {
        RecordedBO recorded = buildDisputeRecorded(postedTransaction, transactionCodeResDTO,
                                                organizationInfoResDTO,accountManagementInfo);
        recorded.setTxnTransactionCode(transactionCodeResDTO.getDisputeReleaseTxnCode());
        TransactionCodeResDTO parmReleaseTransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(),transactionCodeResDTO.getDisputeReleaseTxnCode());
        if (StringUtils.isNotEmpty(releaseMethod)){
            if (Constants.RELEASE_C.equals(releaseMethod)){
                recorded.setTxnTransactionDescription(postedTransaction.getMerchantName() + " (Disputed transaction : Release to Cardholder)");
            }else {
                recorded.setTxnTransactionDescription(postedTransaction.getMerchantName() + " (Disputed transaction : Release to Bank)");
            }
        }else {
            recorded.setTxnTransactionDescription(parmReleaseTransactionCode.getDescription());
        }

        return recorded;
    }

    /**
     * 组借记入账交易接口
     * Rule-4:  借记交易入账接口赋值
     */
    private RecordedBO buildDebitRecorded(PostedTransaction postedTransaction,
                                        TransactionCodeResDTO transactionCodeResDTO,
                                        OrganizationInfoResDTO organizationInfoResDTO,
                                        AccountManagementInfo accountManagementInfo) {
        RecordedBO recorded = buildCreditAdjustmentRecorded(postedTransaction,transactionCodeResDTO,
                                                    organizationInfoResDTO,accountManagementInfo);
        //光大POC 修改Rule-4 txnTransactionDate和txnAuthorizationMatchIndicator字段赋值逻辑
        recorded.setTxnTransactionDate(postedTransaction.getTransactionDate());
//        recorded.setTxnAuthorizationMatchIndicator("9");

        recorded.setTxnTransactionCode(postedTransaction.getPostingTransactionCode());
        //TransactionCodeResDTO parmPostTransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(), postedTransaction.getPostingTransactionCode());
        String description;
        if (StringUtils.isNotEmpty(postedTransaction.getMerchantName())){
            description = "Re-post of Disputed transaction - " + postedTransaction.getMerchantName() + " " + postedTransaction.getTransactionDescription();
        }else {
            description = "Disputed transaction - " + postedTransaction.getTransactionDescription();
        }
        recorded.setTxnTransactionDescription(description);
        recorded.setTxnBillingDate(organizationInfoResDTO.getNextProcessingDay());
        return recorded;
    }

    /**
     * 更新入账明细
     *
     * @param postedTransaction
     */
    private void updatePostedTransaction(PostedTransaction postedTransaction) {
        postedTransaction.setUpdateTime(LocalDateTime.now());
        postedTransaction.setVersionNumber(postedTransaction.getVersionNumber() + 1);
        postedTransaction.setUpdateBy("");
        try {
            int i = postedTransactionMapper.updateByPrimaryKeySelective(postedTransaction);
            if( i != 1){
                logger.error("Failed to update table {} by primary key {}, expected affected rows: {}, actual affected rows: {}", "PostedTransaction", postedTransaction.getPostedTransactionId(), 1, i);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_EFFECT_NUM_ERROR);
            }
        } catch (Exception e) {
            logger.error("Dispute processing - calling [{}] update transaction details table [{}] failed, error message: [{}]",
                      "postedTransactionMapper.updateByPrimaryKeySelective",
                      "POSTED_TRANSACTION", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
    }

    /**
     * 更新争议交易表
     */
    private void updateDisputedTransaction(DisputedTransaction disputedTransaction) {
        disputedTransaction.setUpdateTime(LocalDateTime.now());
        disputedTransaction.setVersionNumber(disputedTransaction.getVersionNumber() + 1);
        disputedTransaction.setUpdateBy("");
        try {
            int i = disputedTransactionMapper.updateByPrimaryKeySelective(disputedTransaction);
            if( i != 1){
                logger.error("Failed to update table {} by primary key {}, expected affected rows: {}, actual affected rows: {}", "DisputedTransaction", disputedTransaction.getDisputedTransactionId(), 1, i);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_EFFECT_NUM_ERROR);
            }
        } catch (Exception e) {
            logger.error("Dispute processing - calling [{}] update disputed transaction table [{}] failed, error message: [{}]",
                      "disputedTransactionMapper.updateByPrimaryKeySelective",
                      "DISPUTED_TRANSACTION", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
    }

    /**
     * 调用核心入账逻辑
     */
    private void coreRecorded(RecordedBO recorded) {
        logger.info("Dispute processing - calling core posting logic start");
        try {
            txnRecordedService.txnRecorded(recorded);
        } catch (AnyTxnTransactionException e) {
            logger.error("Dispute processing - calling core posting logic failed, error message: [{}]", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TXN_RECORDED_SERVICE_EXCEPTION,e);
        }
    }

    /**
     * 根据争议id查询争议交易
     *
     * @param disputedTransactionId
     * @return
     */
    private DisputedTransaction queryDisputeById(String disputedTransactionId) {
        try {
            return disputedTransactionMapper.selectByPrimaryKey(disputedTransactionId);
        } catch (Exception e) {
            logger.error("Dispute processing - calling [{}] query disputed transaction table [{}] failed, error message: [{}]",
                      "disputedTransactionMapper.selectByPrimaryKey",
                      "DISPUTED_TRANSACTION", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_SELECT_ERROR);
        }
    }
}
