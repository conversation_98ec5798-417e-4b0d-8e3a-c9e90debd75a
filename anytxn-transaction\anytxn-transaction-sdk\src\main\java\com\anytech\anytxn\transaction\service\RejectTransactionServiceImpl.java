package com.anytech.anytxn.transaction.service;


import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.base.transaction.domain.dto.ResultRejectedTransactionDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.SettlementLogDTO;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.dao.monetary.mapper.CustReconciliationControlSelfMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.RejectedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.RejectedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.RejectedTransaction;
import com.anytech.anytxn.central.service.common.AccountantRecordManager;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.GlDetailDTO;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.domain.bo.RejectedTransactionBO;
import com.anytech.anytxn.transaction.base.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.PostMethodEnum;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.domain.dto.RejectedTransactionDTO;
import com.anytech.anytxn.transaction.base.enums.AlreadyHandleEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.enums.TransactionRepDetailEnum;
import com.anytech.anytxn.transaction.base.service.IRejectTransactionService;
import com.anytech.anytxn.transaction.base.service.ISettlementLogService;
import com.anytech.anytxn.transaction.base.service.VataFrozenAmountService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.base.utils.ListUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 拒绝交易的数据处理
 *
 * <AUTHOR>
 * @date 2018-08-23
 */
@Service
public class RejectTransactionServiceImpl implements IRejectTransactionService {
    private static final Logger logger = LoggerFactory.getLogger(RejectTransactionServiceImpl.class);

    @Resource
    private RejectedTransactionMapper rejectedTransactionMapper;
    @Resource
    private RejectedTransactionSelfMapper rejectedTransactionSelfMapper;
    @Resource
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Resource
    protected ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Resource
    private ISettlementLogService settlementLogService;
    @Resource
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Resource
    private CustReconciliationControlSelfMapper custReconciliationControlSelfMapper;
    @Resource
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;
    @Resource
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;
    @Resource
    private ICustReconciliationControlService custReconciliationControlService;
    @Resource
    private ITransactionCodeService transactionCodeService;
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private CardBasicInfoMapper cardBasicInfoMapper;
    @Resource
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;
    @Resource
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;
    @Resource
    private VataFrozenAmountService vataFrozenAmountService;
    @Resource
    private PartitionKeyInitService partitionKeyInitService;
    @Resource
    private AccountantRecordManager accountantRecordManager;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Value("${anytxn.batch.stmt.max-insert:500}")
    private int maxInsert;
    @Value("${anytxn.batch.stmt.max-update:500}")
    private int maxUpdate;

    public static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(1);

    public static final AtomicInteger ATOMIC_INTEGER_BATCHNO = new AtomicInteger(1);

    @Override
    public int saveRejectTransaction(RejectedTransactionDTO rejectedTransaction) {
        return rejectedTransactionMapper.insert(BeanMapping.copy(rejectedTransaction, RejectedTransaction.class));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void rejectTransaction(RecordedBO recorded, String rejectReason) {
        RejectedTransaction rejectedTransaction = new RejectedTransaction();
        logger.info("Reject transaction write: recorded={}, rejectReason={}", recorded, rejectReason);
        if (recorded == null || StringUtils.isEmpty(rejectReason)) {
            logger.error("Input parameters are null: recorded={}, rejectReason={}", recorded, rejectReason);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL);
        }
        String txnTransactionCode = recorded.getTxnTransactionCode();
        logger.info("Calling transactionCodeService.findTransactionCode: org={}, txnTransactionCode={}", OrgNumberUtils.getOrg(), txnTransactionCode);
        TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(OrgNumberUtils.getOrg(), txnTransactionCode);
        logger.info("TransactionCodeService.findTransactionCode completed: transactionCode={}", transactionCode != null ? "found" : "not found");
        if (transactionCode != null) {
            rejectedTransaction.setDebitCreditInd(transactionCode.getDebitCreditIndicator());
        }
        String rejectedTransactionId = sequenceIdGen.generateId(TenantUtils.getTenantId());
        rejectedTransaction.setRejectedTransactionId(rejectedTransactionId);
        rejectedTransaction.setCorporateCustomerId(recorded.getCorporateCustomerId());
        rejectedTransaction.setRejectReason(rejectReason);
        rejectedTransaction.setAccountManagementId(recorded.getTxnAccountManageId());
        rejectedTransaction.setParentTransactionAccountId(recorded.getTxnParentTransactionAccountId());
        rejectedTransaction.setOriginalTransactionBalId(recorded.getTxnOriginalTransactionBalanceId());
        rejectedTransaction.setCardNumber(recorded.getTxnCardNumber());
        rejectedTransaction.setOriginalTransactionDate(recorded.getTxnTransactionDate());
        rejectedTransaction.setGlobalFlowNumber(recorded.getTxnGlobalFlowNumber());
        rejectedTransaction.setOriginalGlobalFlowNumber(recorded.getTxnOriginalGlobalFlowNumber());
        rejectedTransaction.setPostMethod(recorded.getTxnPostMethod());
        rejectedTransaction.setReverseFeeIndicator(recorded.getTxnReverseFeeIndicator());
        rejectedTransaction.setTransactionDate(recorded.getTxnTransactionDate());
        rejectedTransaction.setTransactionCode(recorded.getTxnTransactionCode());
        rejectedTransaction.setTransactionSource(recorded.getTxnTransactionSource());
        rejectedTransaction.setTransactionDescription(recorded.getTxnTransactionDescription());
        rejectedTransaction.setTransactionAmount(recorded.getTxnTransactionAmount());
        rejectedTransaction.setTransactionCurrency(recorded.getTxnTransactionCurrency());
        rejectedTransaction.setBillingDate(recorded.getTxnBillingDate());
        rejectedTransaction.setBillingAmount(recorded.getTxnBillingAmount());
        rejectedTransaction.setBillingCurrency(recorded.getTxnBillingCurrency());
        rejectedTransaction.setSettlementAmount(recorded.getTxnSettlementAmount());
        rejectedTransaction.setSettlementCurrency(recorded.getTxnSettlementCurrency());
        rejectedTransaction.setExchangeRate(recorded.getTxnExchangeRate());
        rejectedTransaction.setAuthorizationCode(recorded.getTxnAuthorizationCode());
        rejectedTransaction.setZipCode(recorded.getTxnZipCode());
        rejectedTransaction.setMerchantId(recorded.getTxnMerchantId());
        rejectedTransaction.setMerchantName(recorded.getTxnMerchantName());
        rejectedTransaction.setMerchantCategoryCode(recorded.getTxnMerchantCategoryCode());
        rejectedTransaction.setCountryCode(recorded.getTxnCountryCode());
        rejectedTransaction.setStateCode(recorded.getTxnStateCode());
        rejectedTransaction.setCityCode(recorded.getTxnCityCode());
        rejectedTransaction.setReferenceNumber(recorded.getTxnReferenceNumber());
        rejectedTransaction.setAcquireReferenceNo(recorded.getTxnAcquireReferenceNo());
        rejectedTransaction.setAuthorizationMatchIndicator(recorded.getTxnAuthorizationMatchIndicator());
        rejectedTransaction.setReleaseAuthorizationAmount(recorded.getTxnReleaseAuthorizationAmount());
        rejectedTransaction.setLimitNodeId(recorded.getTxnLimitNodeId());
        rejectedTransaction.setOutstandingAmount(recorded.getTxnOutstandingAmount());
        rejectedTransaction.setOpponentBankNumber(recorded.getTxnOpponentBankNumber());
        rejectedTransaction.setOpponentAccountName(recorded.getTxnOpponentAccountName());
        rejectedTransaction.setOpponentAccountNumber(recorded.getTxnOpponentAccountNumber());
        rejectedTransaction.setSecondMerchantId(recorded.getTxnSecondMerchantId());
        rejectedTransaction.setSecondMerchantName(recorded.getTxnSecondMerchantName());
        rejectedTransaction.setPosEntryMode(recorded.getTxnPosEntryMode());
        rejectedTransaction.setVisaChargeFlag(recorded.getTxnVisaChargeFlag());
        rejectedTransaction.setReimbursementAttribute(recorded.getTxnReimbursementAttribute());
        rejectedTransaction.setIfiIndicator(recorded.getTxnIfiIndicator());
        rejectedTransaction.setPsvIndicator(recorded.getTxnPsvIndicator());
        rejectedTransaction.setDccIndicator(recorded.getTxnDccIndicator());
        rejectedTransaction.setForcePostIndicator(recorded.getTxnForcePostIndicator());
        rejectedTransaction.setFallBackIndicator(recorded.getTxnFallBackIndicator());
        rejectedTransaction.setInstallmentIndicator(recorded.getTxnInstallmentIndicator());
        rejectedTransaction.setInstallmentOrderid(recorded.getTxnInstallmentOrderId());
        rejectedTransaction.setInstallmentTerm(recorded.getTxnInstallmentTerm());
        rejectedTransaction.setInterestTableId(recorded.getTxnInterestTableId());
        rejectedTransaction.setFeeTableId(recorded.getTxnFeeTableId());
        rejectedTransaction.setRepostFromSuspend(recorded.getTxnRepostFromSuspend());
        rejectedTransaction.setAlreadyHandled(AlreadyHandleEnum.NO_HANDLE.getCode());
        //原始交易账户id
        rejectedTransaction.setOriginalTxnAccountId(recorded.getTxnOriginalTxnAccountId());
        //add 单信息 双信息模式 赋值
        rejectedTransaction.setMessageIndicator(recorded.getMessageIndicator());
        rejectedTransaction.setStatus("1");
        rejectedTransaction.setCreateTime(LocalDateTime.now());
        rejectedTransaction.setUpdateTime(LocalDateTime.now());
        rejectedTransaction.setUpdateBy("ADMIN");
        rejectedTransaction.setVersionNumber(1L);
        rejectedTransaction.setChannelId(recorded.getTxnChannelId());
        rejectedTransaction.setFundId(recorded.getTxnFundId());
        rejectedTransaction.setSubChannelId(recorded.getTxnSubChannelId());
        rejectedTransaction.setProtectBuyJson(recorded.getProtectBuyJson());
        rejectedTransaction.setInterchangeFee(recorded.getInterchangeFee());
        if (!StringUtils.isEmpty(recorded.getTxnAccountManageId())) {
            AccountManagementInfo managementInfo = accountManagementInfoMapper
                    .selectByPrimaryKey(recorded.getTxnAccountManageId());
            if (managementInfo != null) {
                rejectedTransaction.setProductNumber(managementInfo.getProductNumber());
                rejectedTransaction.setAccountStatus(managementInfo.getAccountStatus());
                rejectedTransaction.setAccountBlockCode(managementInfo.getBlockCode());
                rejectedTransaction.setOnStatementIndicator(managementInfo.getOnlineStatementIndicator());
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper
                        .selectByPrimaryCustomerIdAndCardProNum(recorded.getCustomerId(),
                                managementInfo.getProductNumber());
                if (cardAuthorizationInfo != null) {
                    rejectedTransaction.setCardStatus(cardAuthorizationInfo.getStatus());
                    rejectedTransaction.setCardBlockCode(cardAuthorizationInfo.getBlockCode());
                    rejectedTransaction.setScheme(cardAuthorizationInfo.getCardScheme());
                }
            } else if (!StringUtils.isEmpty(recorded.getTxnCardNumber())) {
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper
                        .selectPriCusIdAndProNum(recorded.getTxnCardNumber(),
                                recorded.getOrganizationNumber());
                if (cardAuthorizationInfo != null) {
                    String productNumber = cardAuthorizationInfo.getProductNumber();
                    logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", OrgNumberUtils.getOrg());
                    OrganizationInfoResDTO organizationInfo = organizationInfoService
                            .findOrganizationInfo(OrgNumberUtils.getOrg());
                    logger.info("OrganizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "not found");
                    if (ObjectUtils.isEmpty(organizationInfo)) {
                        logger.error("Organization info not found for org: {}", OrgNumberUtils.getOrg());
                    }
                    rejectedTransaction.setProductNumber(productNumber);
                    rejectedTransaction.setCardStatus(cardAuthorizationInfo.getStatus());
                    rejectedTransaction.setCardBlockCode(cardAuthorizationInfo.getBlockCode());
                    rejectedTransaction.setScheme(cardAuthorizationInfo.getCardScheme());
                    AccountManagementInfo accountManagementInfo = accountManagementInfoMapper
                            .selectByCusIdProNumAndCurrCode(
                                    recorded.getCustomerId(), productNumber,
                                    recorded.getOrganizationNumber(),
                                    organizationInfo.getOrganizationCurrency());
                    if (accountManagementInfo != null) {
                        rejectedTransaction.setAccountStatus(accountManagementInfo.getAccountStatus());
                        rejectedTransaction.setAccountBlockCode(accountManagementInfo.getBlockCode());
                        rejectedTransaction.setOnStatementIndicator(accountManagementInfo
                                .getOnlineStatementIndicator());
                    }
                }
            }
        }
        rejectedTransaction.setParentPostedTransactionId(recorded.getTxnParentPostedTransactionId());
        rejectedTransaction.setOriginalPostedTransactionId(recorded.getTxnOriginalPostedTransactionId());
        rejectedTransaction.setInstallmentCurrentTerm(recorded.getTxnInstallmentTerm());
        rejectedTransaction.setOpponentBankId(recorded.getTxnOpponentBankNumber());
        rejectedTransaction.setReversalIndicator(recorded.getTxnReverseFeeIndicator());
        rejectedTransaction.setOpponentTxnArea(recorded.getOpponentTxnArea());
        rejectedTransaction.setMerchantInfo2ndCode(recorded.getMerchantInfo2ndCode());
        rejectedTransaction.setMerchantInfo2ndName(recorded.getMerchantInfo2ndName());
        rejectedTransaction.setOpponentTxnArea1(recorded.getOpponentTxnArea1());
        rejectedTransaction.setOpponentTxnArea2(recorded.getOpponentTxnArea2());
        rejectedTransaction.setTerminalType(recorded.getTerminalType());
        rejectedTransaction.setSenderAccount(recorded.getSenderAccount());
        rejectedTransaction.setSenderName(recorded.getSenderName());
        rejectedTransaction.setSenderAccountBankNumber(recorded.getSenderAccountBankNumber());
        rejectedTransaction.setSendingOrganizationChineseAbbreviation(recorded
                .getSendingOrganizationChineseAbbreviation());
        rejectedTransaction.setTurnOutPartyCardNumber(recorded.getTurnOutPartyCardNumber());
        rejectedTransaction.setTransferPartyCardNumber(recorded.getTransferPartyCardNumber());
        rejectedTransaction.setTransfereeName(recorded.getTransfereeName());
        rejectedTransaction.setTurnOutPartyName(recorded.getTurnOutPartyName());
        rejectedTransaction.setClientIpAddress(recorded.getClientIpAddress());
        rejectedTransaction.setMacAddress(recorded.getMacAddress());
        rejectedTransaction.setMerchantInfo2ndSort(recorded.getMerchantInfo2ndSort());
        rejectedTransaction.setTradingAddress(recorded.getTradingAddress());
        rejectedTransaction.setJsonReserved(recorded.getJsonReserved());
        rejectedTransaction.setMkUpFeeInd(recorded.getMkUpFeeInd());
        rejectedTransaction.setTxnRefId(recorded.getTxnRefId());
        rejectedTransaction.setVaNumber(recorded.getVaNumber());
        rejectedTransaction.setMultiClearInd(recorded.getMultiClearInd());
        rejectedTransaction.setTraceId(recorded.getTraceId());
        rejectedTransaction.setTraceNumber(recorded.getTraceNumber());

        if (recorded.isReturnRejectInfo()) {
            ResultRejectedTransactionDTO resultRejectedTransactionDTO = BeanMapping.copy(rejectedTransaction, ResultRejectedTransactionDTO.class);
            CustAccountBO.threadCustAccountBO.get().getTransRecordResultBO().setResultRejectedTransactionDTO(resultRejectedTransactionDTO);
        } else {
            logger.info("Calling txnRecordedAccountCacheService.rejectedTransactionInsertSelective: rejectedTransactionId={}", rejectedTransaction.getRejectedTransactionId());
            txnRecordedAccountCacheService.rejectedTransactionInsertSelective(rejectedTransaction);
            logger.info("TxnRecordedAccountCacheService.rejectedTransactionInsertSelective completed");
        }
    }

    //@Override
   /* public GlDetailDTO generateGL(Recorded recorded, String transactionCode) {
        LocalDate txnBillingDate = recorded.getTxnBillingDate();
        String postingDate = DateHelper.formatYmd(txnBillingDate);
//        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(recorded.getTxnCardNumber());
//        if (ObjectUtils.isEmpty(cardAuthorizationInfo)) {
//            log.error("根据卡号:{}查询卡授权信息 数据不存在", recorded.getTxnCardNumber());
//        }
        logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("OrganizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "not found");
        if (ObjectUtils.isEmpty(organizationInfo)) {
            logger.error("Organization info not found for org: {}", OrgNumberUtils.getOrg());
        }
//        String defaultCurrency = cardProductCurrencyRelationService.findDefaultCurrency(organizationInfo, cardAuthorizationInfo.getProductNumber());
//        String blockCode = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(cardAuthorizationInfo.getPrimaryCustomerId(), OrgNumberUtils.getOrg()).get(0).getBlockCode();
        GlDetailDTO glDetailDto = new GlDetailDTO();
        glDetailDto.setRecordType("D");
        // 参考号/序列号6位  序列号从1开始
        glDetailDto.setSequenceNumber(1);
        glDetailDto.setCardNumber(recorded.getTxnCardNumber());
//        glDetailDto.setCardProductCode(cardAuthorizationInfo.getProductNumber());
        glDetailDto.setCardType("P");
        // 指持卡人或商户服务费用)中心代码
        glDetailDto.setServicingBranch("test");
        glDetailDto.setAcquirerId(" ");
        glDetailDto.setAgeCodeBeforePostingTxn(" ");
        glDetailDto.setPostingDate(Integer.valueOf(postingDate));
        // 批次号
        glDetailDto.setBatchNo(123);
        glDetailDto.setTransactionCode(transactionCode);
        glDetailDto.setPostingCurrencyCode(Integer.valueOf(recorded.getTxnBillingCurrency()));
        glDetailDto.setTransactionCurrencyCode(Integer.valueOf(recorded.getTxnTransactionCurrency()));
        glDetailDto.setSettlementCurrencyCode(Integer.valueOf(recorded.getTxnSettlementCurrency()));
        //持卡人在完成此交易前的余额
        glDetailDto.setBalanceBeforePostingTxn(BigDecimal.ZERO);
        if (recorded.getTxnBillingAmount() != null) {
            String billingAmount = String.valueOf(recorded.getTxnBillingAmount().multiply(new BigDecimal(100)));
            if (billingAmount.contains(".")) {
                billingAmount = billingAmount.substring(0, billingAmount.indexOf("."));
            }
            glDetailDto.setPostingAmount(new BigDecimal(billingAmount));
        }
        glDetailDto.setSourceId(recorded.getTxnChannelId());
        glDetailDto.setSettlementParty(" ");
//        glDetailDto.setCardStatus(blockCode);
        // 账户封锁码原因码
        glDetailDto.setCardStatusReasonCode("test");
        glDetailDto.setMcc(recorded.getTxnMerchantCategoryCode());
        glDetailDto.setMerchantBankPayeeCode("test");
        glDetailDto.setPaymentProcessingBranchCode("test");
        glDetailDto.setMerchantGstRegistrationNo(" ");
        glDetailDto.setCardAcceptorId(recorded.getTxnMerchantId());
        glDetailDto.setUsageCode(" ");
        glDetailDto.setUsageCodeDesc(" ");
        return glDetailDto;
    }*/

    /*public GlFileDto batchProcess(RejectedTransaction rejectedTransactionDTO) {
        Recorded recorded = buildRecorded(rejectedTransactionDTO);
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
        RejectedTransactionMapper rejectedTransMapper = sqlSession.getMapper(RejectedTransactionMapper.class);
        SettlementLogMapper settlementLogMapper = sqlSession.getMapper(SettlementLogMapper.class);
        // 构建settlementLog
        SettlementLogDTO settlementLogDTO = buildSettlementLog(recorded, sqlSession);
        settlementLogDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        settlementLogMapper.insertSelective(BeanMapping.copy(settlementLogDTO, SettlementLog.class));
        //更新拒绝交易状态
        RejectedTransaction rejectedTransaction = new RejectedTransaction();
        rejectedTransaction.setRejectedTransactionId(rejectedTransaction.getRejectedTransactionId());
        rejectedTransaction.setVersionNumber(1L);
        rejectedTransaction.setAlreadyHandled(AlreadyHandle.HANDLE.getCode());
        rejectedTransaction.setUpdateTime(LocalDateTime.now());
        rejectedTransaction.setUpdateBy(TransactionConstants.DEFAULT_USER);
        rejectedTransMapper.updateByPrimaryKeySelective(rejectedTransaction);

        GlDetailDTO glDetailDTO = generateGL(recorded, "OU010", sqlSession, null);
        log.info("组装GL数据:{}", JSON.toJSONString(glDetailDTO));
        GlFileDto glFileDto = new GlFileDto();
        BeanMapping.copy(glDetailDTO, glFileDto);
        //金额类型
        BigDecimal balanceBeforePostingTxn = glDetailDTO.getBalanceBeforePostingTxn();
        BigDecimal postingAmount = glDetailDTO.getPostingAmount();
        glFileDto.setBalanceBeforePostingTxn(StringFormatterUtils.format(balanceBeforePostingTxn));
        glFileDto.setPostingAmount(StringFormatterUtils.format(postingAmount));
        glFileDto.setSequenceNo(glDetailDTO.getSequenceNumber());
        return glFileDto;
    }*/


    @Override
    public GlDetailDTO generateGL(RecordedBO recordedBO, String transactionCode, SqlSession sqlSession, RecordedMidBO recordedMidBO) {
        LocalDate txnBillingDate = recordedBO.getTxnBillingDate();
        GlDetailDTO glDetailDTO = new GlDetailDTO();
        CardAuthorizationDTO cardAuthorizationInfo = null;
        ParmOrganizationInfo organizationInfo;
        String currency;
        AccountManagementInfo accountManagementInfo;
        ParmTransactionCode parmTransactionCode;
        String acquirerId = null;
        organizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(OrgNumberUtils.getOrg());
        if (ObjectUtils.isEmpty(organizationInfo)) {
            logger.error("Organization info not found for org: {}", OrgNumberUtils.getOrg());
        }
        if (!StringUtils.isEmpty(recordedBO.getTxnCardNumber())) {
            logger.info("Card number: {}", recordedBO.getTxnCardNumber());
        } else {
            logger.info("Recorded card number is empty, customerId: {}", recordedBO.getCustomerId());
        }
        //如果有管理账户，就直接查相关账户信息
        String boAcctId = ObjectUtils.isEmpty(recordedMidBO) ? null :
                ObjectUtils.isEmpty(recordedMidBO.accountBO) ? null :
                        ObjectUtils.isEmpty(recordedMidBO.accountBO.getAccountManagementInfo())
                                ? null : recordedMidBO.accountBO.getAccountManagementInfo()
                                .getAccountManagementId();
        String acctId = !StringUtils.isEmpty(recordedBO.getTxnAccountManageId())
                ? recordedBO.getTxnAccountManageId() : ObjectUtils.isEmpty(recordedMidBO)
                ? null : ObjectUtils.isEmpty(recordedMidBO.recorded) ? null :
                !StringUtils.isEmpty(recordedMidBO.recorded.getTxnAccountManageId())
                        ? recordedMidBO.recorded.getTxnAccountManageId() : boAcctId;
        if (!StringUtils.isEmpty(acctId)) {
            accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(acctId);
            currency = accountManagementInfo.getCurrency();

            if (!StringUtils.isEmpty(recordedBO.getTxnCardNumber())) {
                CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey
                        (recordedBO.getTxnCardNumber(), organizationInfo.getOrganizationNumber());
                if (Objects.isNull(cardBasicInfo)) {
                    logger.error("Card basic info not found for customerId: {}", recordedBO.getCustomerId());
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
                }
                glDetailDTO.setPaymentProcessingBranchCode(cardBasicInfo.getBranchNumber());
                glDetailDTO.setMerchantGstRegistrationNo(cardBasicInfo.getBranchNumber());
                acquirerId = cardBasicInfo.getBatchNumber();
            } else {
                if (Objects.nonNull(recordedMidBO) && Objects.nonNull(recordedMidBO.getCustomerBO())){
                    CustomerBasicInfo customerBasicInfo = BeanMapping.copy(recordedMidBO.getCustomerBO()
                            .getCustomerBasicInfo(), CustomerBasicInfo.class);
                    if (Objects.nonNull(customerBasicInfo)){
                        glDetailDTO.setPaymentProcessingBranchCode(customerBasicInfo.getBranchNumber());
                        glDetailDTO.setMerchantGstRegistrationNo(customerBasicInfo.getBranchNumber());
                    }
                }
            }
        } else {
            cardAuthorizationInfo = getCardAuthorizationInfo(recordedMidBO, recordedBO,
                    cardAuthorizationInfo, organizationInfo, glDetailDTO);
            if (cardAuthorizationInfo == null) {
                accountManagementInfo = BeanMapping.copy(recordedMidBO.getAccountBO()
                        .getAccountManagementInfo(), AccountManagementInfo.class);
                currency = accountManagementInfo.getCurrency();
            } else {
                currency = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum
                        (organizationInfo.getOrganizationNumber(), cardAuthorizationInfo
                                .getProductNumber()).get(0).getCurrencyCode();
                accountManagementInfo = getAccountManagementInfo(cardAuthorizationInfo,
                        organizationInfo, recordedBO, recordedMidBO);
            }
        }
        if (txnBillingDate == null) {
            CustReconciliationControlDTO control = CustAccountBO.threadCustAccountBO.get()
                    .getCustReconciliationControl();
            if (control == null) {
                String customerId;
                if (accountManagementInfo != null){
                    customerId = accountManagementInfo.getCustomerId();
                }else {
                    customerId = recordedBO.getCustomerId();
                }
                if (!StringUtils.isEmpty(customerId)){
                    control = BeanMapping.copy(custReconciliationControlSelfMapper
                                    .selectByCustAndOrg(customerId,
                                            organizationInfo.getOrganizationNumber()),
                            CustReconciliationControlDTO.class);
                }
            }
            if (control != null){
                logger.info("Calling custReconciliationControlService.getBillingDate: control={}", control.getCustomerId());
                txnBillingDate = custReconciliationControlService.getBillingDate(control,
                        organizationInfo.getAccruedThruDay(), organizationInfo.getToday(),
                        organizationInfo.getNextProcessingDay());
                logger.info("CustReconciliationControlService.getBillingDate completed: txnBillingDate={}", txnBillingDate);
            }
        }
        parmTransactionCode = transactionCodeSelfMapper.selectByOrgNumberAndCode(
                organizationInfo.getOrganizationNumber(), recordedBO.getTxnTransactionCode());
        String defaultCurrency = StringUtils.isBlank(currency) ? organizationInfo.getOrganizationCurrency() :
                currency;
        glDetailDTO.setRecordType("D");
        int sequenceNo = ATOMIC_INTEGER.getAndIncrement();
        if (sequenceNo > 999999) {
            ATOMIC_INTEGER.set(1);
            sequenceNo = ATOMIC_INTEGER.getAndIncrement();
        }
        glDetailDTO.setSequenceNumber(sequenceNo);
        int batchNo = ATOMIC_INTEGER_BATCHNO.getAndIncrement();
        if (batchNo > 999) {
            ATOMIC_INTEGER_BATCHNO.set(1);
            batchNo = ATOMIC_INTEGER_BATCHNO.getAndIncrement();
        }
        glDetailDTO.setBatchNo(batchNo);
        //找卡
        logger.info("Processing card number assignment");
        logger.info("Calling vataFrozenAmountService.cardFounder");
        String crdNumber = vataFrozenAmountService.cardFounder(recordedMidBO, recordedBO);
        logger.info("VataFrozenAmountService.cardFounder completed: crdNumber={}", crdNumber);
        logger.info("Calling vataFrozenAmountService.vaNumberFounder");
        String vaNumber = vataFrozenAmountService.vaNumberFounder(accountManagementInfo, recordedMidBO, recordedBO);
        logger.info("VataFrozenAmountService.vaNumberFounder completed: vaNumber={}", vaNumber);
        glDetailDTO.setCardNumber(crdNumber);
        glDetailDTO.setVaNumber(vaNumber);
        glDetailDTO.setAccountManagementId(ObjectUtils.isEmpty(accountManagementInfo) ? recordedBO.getTxnAccountManageId() : accountManagementInfo.getAccountManagementId());
        glDetailDTO.setCreateTime(LocalDateTime.now());
        glDetailDTO.setUpdateTime(LocalDateTime.now());
        //v2,v3配对标志
        glDetailDTO.setVataCoupleIndicator(ObjectUtils.isEmpty(recordedMidBO) ? StringUtils.isEmpty(recordedBO.getVataCoupleIndicator()) ? ""
                : recordedBO.getVataCoupleIndicator() : recordedMidBO.recorded.getVataCoupleIndicator());
        CardAuthorizationInfo cardAuthorizationInfo2 = cardAuthorizationInfoSelfMapper.selectByCardNumber(crdNumber);
        if (!ObjectUtils.isEmpty(cardAuthorizationInfo2)) {
            cardAuthorizationInfo = BeanMapping.copy(cardAuthorizationInfo2, CardAuthorizationDTO.class);
        }
        String cardProductNumber = cardAuthorizationInfo == null
                ? "" : cardAuthorizationInfo.getProductNumber();
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper
                .selectByOrgAndProductNum(organizationInfo.getOrganizationNumber(), cardProductNumber);
        if (!ObjectUtils.isEmpty(parmCardProductInfo)) {
            glDetailDTO.setServicingBranch(parmCardProductInfo.getScheme());
        } else {
            glDetailDTO.setServicingBranch("NOSCHE");
            logger.info("Card product not found for card: {}", glDetailDTO.getCardNumber());
        }
        glDetailDTO.setCardProductCode(cardProductNumber);
        glDetailDTO.setCardType("P");
        glDetailDTO.setAgeCodeBeforePostingTxn(accountManagementInfo == null ? "0"
                : String.valueOf(accountManagementInfo.getCycleDue()));
        String postingDate;
        if (txnBillingDate != null){
            postingDate = DateHelper.formatYmd(txnBillingDate);
            glDetailDTO.setPostingDate(Integer.valueOf(postingDate));
        }else if (recordedBO.getTxnBillingDate() != null){
            postingDate = DateHelper.formatYmd(recordedBO.getTxnBillingDate());
            glDetailDTO.setPostingDate(Integer.valueOf(postingDate));
        }else {
            glDetailDTO.setPostingDate(Integer.valueOf("********"));
        }

        glDetailDTO.setTransactionCode(transactionCode);
        glDetailDTO.setPostingCurrencyCode(Integer.valueOf(recordedBO.getTxnBillingCurrency()));
        glDetailDTO.setTransactionCurrencyCode(Integer.valueOf(recordedBO.getTxnTransactionCurrency()));
        glDetailDTO.setSettlementCurrencyCode(Integer.valueOf(defaultCurrency));
        glDetailDTO.setBalanceBeforePostingTxn(recordedBO.getTxnTransactionAmount());
        BigDecimal txnBillingAmount = recordedBO.getTxnBillingAmount();
        //入账金额
        if (Objects.nonNull(parmTransactionCode)) {
            if (DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode().equals(parmTransactionCode.getDebitCreditIndicator())) {
                txnBillingAmount = txnBillingAmount.multiply(new BigDecimal(-1));
            }
        }
        glDetailDTO.setPostingAmount(txnBillingAmount);
        glDetailDTO.setSourceId(Objects.equals(recordedBO.getTxnTransactionSource(), TransactionSourceEnum.LOCAL_OUT_L.getCode()) ? "0" : recordedBO.getTxnTransactionSource());
        glDetailDTO.setSettlementParty("  ");
        //TODO-2待定
        glDetailDTO.setCardStatus(" ");
        glDetailDTO.setCardStatusReasonCode(" ");
        glDetailDTO.setMcc(recordedBO.getTxnMerchantCategoryCode() == null ? " " : recordedBO.getTxnMerchantCategoryCode());
        glDetailDTO.setMerchantBankPayeeCode(" ");

        glDetailDTO.setCardAcceptorId(recordedBO.getTxnMerchantId() == null ? " " : recordedBO.getTxnMerchantId());
        glDetailDTO.setUsageCode(" ");
        glDetailDTO.setUsageCodeDesc(recordedBO.getTxnReferenceNumber() == null ? " " : recordedBO.getTxnReferenceNumber());

        //CURRENCY_CONVERSION_RATE字段存到MERCHANT_GST_REGISTRATION_NO上

        if (recordedBO.getTxnTransactionCurrency().equals(recordedBO.getTxnBillingCurrency())) {
            glDetailDTO.setMerchantGstRegistrationNo("1");
        } else {
            if ("C".equals(glDetailDTO.getSourceId())) {
                String rate = recordedBO.getTxnExchangeRate().toString();
                char[] arr = rate.toCharArray();
                for (int i = arr.length - 1; i >= 0; i--) {
                    if (arr[i] != '0') {
                        if (arr[i] == '.') {
                            rate = rate.substring(0, i);
                        } else {
                            rate = rate.substring(0, i + 1);
                        }
                        break;
                    }
                }
                glDetailDTO.setMerchantGstRegistrationNo(rate);
            } else if (recordedBO.getTxnExchangeRate() != null){
               glDetailDTO.setMerchantGstRegistrationNo(recordedBO.getTxnExchangeRate().toString());
            }else {
                String txnTransactionCurrency = recordedBO.getTxnTransactionCurrency();
                String txnBillingCurrency = recordedBO.getTxnBillingCurrency();

                logger.info("Card: {}, transaction currency: {}, transaction amount: {}, billing currency: {}, billing amount: {}",
                        recordedBO.getTxnCardNumber(),recordedBO.getTxnTransactionCurrency(),
                        recordedBO.getTxnTransactionAmount(), recordedBO.getTxnBillingCurrency(), recordedBO.getTxnBillingAmount());

                glDetailDTO.setMerchantGstRegistrationNo(getExchangeRate(
                        txnTransactionCurrency, txnBillingCurrency).toString());

            }
        }
        glDetailDTO.setId(String.valueOf(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        glDetailDTO.setAcquirerId(org.springframework.util.StringUtils.isEmpty(acquirerId)
                ? "DINERS" : acquirerId);
        logger.info("Calling accountantRecordManager.getDivision: cardProductCode={}, transactionCode={}", glDetailDTO.getCardProductCode(), glDetailDTO.getTransactionCode());
        ImmutablePair<String, String> division = accountantRecordManager.getDivision(glDetailDTO.getCardProductCode(), glDetailDTO.getTransactionCode());
        logger.info("AccountantRecordManager.getDivision completed: division={}", division != null ? "found" : "not found");

        glDetailDTO.setDivision(division.getLeft());
        glDetailDTO.setDivisionName(division.getRight());

        return glDetailDTO;
    }


    private BigDecimal getExchangeRate(String sourceCurrency,
                                       String targetCurrency){

        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateSelfMapper.
                selectByOrgAndCurrencyAndRateType(OrgNumberUtils.getOrg(),
                        org.apache.commons.lang3.StringUtils
                                .leftPad(sourceCurrency, 3, "0"),
                        org.apache.commons.lang3.StringUtils
                                .leftPad(targetCurrency, 3, "0"), "0");
        if (null == parmCurrencyRate) {
            logger.error("Exchange rate parameter not found: org={}, currencySource={}, currencyDestination={}, rateType={}",
                    OrgNumberUtils.getOrg(), sourceCurrency, targetCurrency, "0");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        return new BigDecimal(parmCurrencyRate.getRateValue()).divide(BigDecimal.valueOf(Math.pow(10, parmCurrencyRate.getExponent())),
                        parmCurrencyRate.getExponent(), RoundingMode.HALF_UP);
    }



    private AccountManagementInfo getAccountManagementInfo(CardAuthorizationDTO cardAuthorizationInfo,
                                                           ParmOrganizationInfo organizationInfo,
                                                           RecordedBO recorded,
                                                           RecordedMidBO recordedMidBO) {
        AccountManagementInfo accountManagementInfo = null;

        String customerId = "";
        String customerId2 = "";
        String relationshipIndicator = cardAuthorizationInfo.getRelationshipIndicator();

        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationInfo.getOrganizationNumber(), cardAuthorizationInfo.getProductNumber());
        if (ObjectUtils.isEmpty(parmCardProductInfo)) {
            logger.info("Card product parameter is empty for product: {}", cardAuthorizationInfo.getProductNumber());
        } else {
            logger.info("Card product parameter is not empty for product: {}", cardAuthorizationInfo.getProductNumber());
        }
        if (StringUtils.isNoneBlank(parmCardProductInfo.getCorporateType()) && parmCardProductInfo.getCorporateType().equals("C") && cardAuthorizationInfo.getLiability().equals("C")) {
            customerId = cardAuthorizationInfo.getCorporateCustomerId();
        } else if (StringUtils.isNoneBlank(parmCardProductInfo.getCorporateType()) && parmCardProductInfo.getCorporateType().equals("C") && cardAuthorizationInfo.getLiability().equals("P")) {
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else if (StringUtils.isNotEmpty(relationshipIndicator) && "P".equals(relationshipIndicator) && cardAuthorizationInfo.getLiability() == null) {
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else if ("S".equals(relationshipIndicator)) {
            customerId2 = cardAuthorizationInfo.getSupplementaryCustomerId();
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        }
        log.info("获取管理账户信息客户号:{}", customerId);
        logger.info("Getting account management info for customerId: {}", customerId);
        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(customerId, organizationInfo.getOrganizationNumber());

        if (CollectionUtils.isEmpty(accountManagementInfos)) {
            String productNumber = parmCardProductInfo.getAccountProductNumber();
            logger.info("Calling txnRecordedAccountCacheService.accountManagementInfoSelectByCondition: customerId={}, productNumber={}, currency={}", customerId, productNumber, recorded.getTxnBillingCurrency());
            AccountManagementInfoDTO accountManagementInfoDTO = txnRecordedAccountCacheService.accountManagementInfoSelectByCondition(customerId, productNumber, recorded.getTxnBillingCurrency());
            logger.info("TxnRecordedAccountCacheService.accountManagementInfoSelectByCondition completed: result={}", accountManagementInfoDTO != null ? "found" : "not found");
            if (accountManagementInfoDTO == null) {
                return null;
            }
            accountManagementInfo = BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class);
        } else {
            accountManagementInfo = accountManagementInfos.get(0);
        }
        return accountManagementInfo;
    }

    private AccountManagementInfo getAccountManageMapper(AccountManagementInfoMapper accountManageMapper, CardAuthorizationInfo cardAuthorizationInfo, ParmOrganizationInfo organizationInfo) {
        String customerId = "";
        String customerId2 = "";
        String relationshipIndicator = cardAuthorizationInfo.getRelationshipIndicator();

        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationInfo.getOrganizationNumber(), cardAuthorizationInfo.getProductNumber());

        if (StringUtils.isNoneBlank(parmCardProductInfo.getCorporateType()) && parmCardProductInfo.getCorporateType().equals("C") && cardAuthorizationInfo.getLiability().equals("C")) {
            customerId = cardAuthorizationInfo.getCorporateCustomerId();
        } else if (StringUtils.isNoneBlank(parmCardProductInfo.getCorporateType()) && parmCardProductInfo.getCorporateType().equals("C") && cardAuthorizationInfo.getLiability().equals("P")) {
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else if (StringUtils.isNotEmpty(relationshipIndicator) && "P".equals(relationshipIndicator) && cardAuthorizationInfo.getLiability() == null) {
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else if ("S".equals(relationshipIndicator)) {
            customerId2 = cardAuthorizationInfo.getSupplementaryCustomerId();
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        }
        logger.info("Getting account management info for customerId: {}", customerId);

        List<AccountManagementInfo> accountManagementInfos = accountManageMapper.selectByCustomerIdAndOrganizationNumber(customerId, organizationInfo.getOrganizationNumber());

        if (CollectionUtils.isEmpty(accountManagementInfos)) {
            logger.error("Account management info not found for customerId: {}", customerId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }

        return accountManagementInfos.get(0);
    }

    private CardAuthorizationDTO getCardAuthorizationInfo(RecordedMidBO recordedMidBO, RecordedBO recorded, CardAuthorizationDTO cardAuthorizationInfo, ParmOrganizationInfo organizationInfo, GlDetailDTO glDetailDTO) {
        if (StringUtils.isNotEmpty(recorded.getTxnCardNumber())) {
            logger.info("Card number: {}", recorded.getTxnCardNumber());
            CardAuthorizationInfo cardAuthorization = cardAuthorizationInfoSelfMapper.selectByCardNumber(recorded.getTxnCardNumber());
            cardAuthorizationInfo = BeanMapping.copy(cardAuthorization, CardAuthorizationDTO.class);
            if (ObjectUtils.isEmpty(cardAuthorizationInfo)) {
                logger.info("Card authorization product found: {}", cardAuthorizationInfo.getProductNumber());
                logger.error("Card authorization info not found for cardNumber: {}", recorded.getTxnCardNumber());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey(recorded.getTxnCardNumber(), organizationInfo.getOrganizationNumber());
            if (Objects.isNull(cardBasicInfo)) {
                logger.error("Card basic info not found for customerId: {}", recorded.getCustomerId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            glDetailDTO.setPaymentProcessingBranchCode(cardBasicInfo.getBranchNumber());
            glDetailDTO.setMerchantGstRegistrationNo(cardBasicInfo.getBranchNumber());
        } else {
            CustomerBasicInfo customerBasicInfo = BeanMapping.copy(recordedMidBO.getCustomerBO().getCustomerBasicInfo(), CustomerBasicInfo.class);
            String principalSupplementaryInd = customerBasicInfo.getPrincipalSupplementaryInd();
            //如果是主客户号
            List<CardAuthorizationInfo> cardAuthorizationInfos;
            logger.info("Principal supplementary indicator: {}", principalSupplementaryInd);
            if (StringUtils.equalsIgnoreCase("P", principalSupplementaryInd)) {
                cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByCustomerId(recorded.getCustomerId());
            } else {
                cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryOrSupplementaryCustomerId(organizationInfo.getOrganizationNumber(), recorded.getCustomerId());
            }
            if (!CollectionUtils.isEmpty(cardAuthorizationInfos)) {
                cardAuthorizationInfo = BeanMapping.copy(cardAuthorizationInfos.get(0), CardAuthorizationDTO.class);
                logger.info("Card authorization info found for customerId: {}, indicator: {}", recorded.getCustomerId(), principalSupplementaryInd);
            } else {
                logger.info("No card authorization info found for customer: {}", recorded.getCustomerId());
            }
            glDetailDTO.setPaymentProcessingBranchCode(customerBasicInfo.getBranchNumber());
            glDetailDTO.setMerchantGstRegistrationNo(customerBasicInfo.getBranchNumber());
        }
        return cardAuthorizationInfo;
    }


    private CardAuthorizationInfo getCardAuthorizationInfo(RecordedBO recorded, CardAuthorizationInfo cardAuthorizationInfo, ParmOrganizationInfo organizationInfo, CardAuthorizationInfoSelfMapper cardAuthorMapper, CustomerBasicInfoSelfMapper customerBasicInfoMapper, GlDetailDTO glDetailDTO, CardBasicInfoMapper cardBasicInfoMapper1) {
        if (StringUtils.isNotEmpty(recorded.getTxnCardNumber())) {
            cardAuthorizationInfo = cardAuthorMapper.selectByCardNumber(recorded.getTxnCardNumber());
            if (Objects.isNull(cardAuthorizationInfo)) {
                logger.error("Card authorization info not found for customerId: {}", recorded.getCustomerId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            CardBasicInfo cardBasicInfo = cardBasicInfoMapper1.selectByPrimaryKey(recorded.getTxnCardNumber(), organizationInfo.getOrganizationNumber());
            if (Objects.isNull(cardBasicInfo)) {
                logger.error("Card basic info not found for customerId: {}", recorded.getCustomerId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            glDetailDTO.setPaymentProcessingBranchCode(cardBasicInfo.getBranchNumber());
            glDetailDTO.setMerchantGstRegistrationNo(cardBasicInfo.getBranchNumber());
        } else {
            CustomerBasicInfo customerBasicInfo = customerBasicInfoMapper.selectByOrgAndCustId(organizationInfo.getOrganizationNumber(), recorded.getCustomerId());
            if (Objects.isNull(customerBasicInfo)) {
                logger.error("Customer basic info not found for customerId: {}", recorded.getCustomerId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            String principalSupplementaryInd = "";
            if (StringUtils.isNotBlank(recorded.getTxnCardNumber())) {
                cardAuthorizationInfo = cardAuthorMapper.selectByCardNumber(recorded.getTxnCardNumber());
                if (ObjectUtils.isEmpty(cardAuthorizationInfo)) {
                    logger.error("Card authorization info not found for cardNumber: {}", recorded.getTxnCardNumber());
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
                }
            } else {
                principalSupplementaryInd = customerBasicInfo.getPrincipalSupplementaryInd();
                //如果是主客户号
                List<CardAuthorizationInfo> cardAuthorizationInfos;
                if (StringUtils.equalsIgnoreCase("P", principalSupplementaryInd)) {
                    cardAuthorizationInfos = cardAuthorMapper.selectByCustomerId(recorded.getCustomerId());
                } else {
                    cardAuthorizationInfos = cardAuthorMapper.selectByPrimaryOrSupplementaryCustomerId(organizationInfo.getOrganizationNumber(), recorded.getCustomerId());
                }
                if (!CollectionUtils.isEmpty(cardAuthorizationInfos)) {
                    cardAuthorizationInfo = cardAuthorizationInfos.get(0);
                    logger.info("Card authorization info found for customerId: {}, indicator: {}", recorded.getCustomerId(), principalSupplementaryInd);
                }
            }
            glDetailDTO.setPaymentProcessingBranchCode(customerBasicInfo.getBranchNumber());
            glDetailDTO.setMerchantGstRegistrationNo(customerBasicInfo.getBranchNumber());
        }

        return cardAuthorizationInfo;
    }

    /**
     * 规则--3
     * 构建入账数据
     *
     * @param rejectedTransaction 拒绝交易入账数据
     * @return Recorded
     */
    public RecordedBO buildRecorded(RejectedTransaction rejectedTransaction) {
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(rejectedTransaction.getAccountManagementId());
        recorded.setTxnCardNumber(rejectedTransaction.getCardNumber());
        recorded.setTxnOriginalTransactionDate(rejectedTransaction.getOriginalTransactionDate());
        recorded.setTxnGlobalFlowNumber(rejectedTransaction.getGlobalFlowNumber());
        //入账方式,1：批量入账,0:实时入账

        //因为批量重入账移到日切前了，入账方式改为实时入账
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        //冲减交易费用标识,1=否
        recorded.setTxnReverseFeeIndicator(rejectedTransaction.getReverseFeeIndicator());
        //拒绝重入账标志,1=拒绝重入账交易
        recorded.setTxnRepostFromSuspend("1");
        //交易码
        String transactionCode = rejectedTransaction.getTransactionCode();
        recorded.setTxnTransactionCode(transactionCode);
        //交易来源
        recorded.setTxnTransactionSource(rejectedTransaction.getTransactionSource());
        //2019-01-29，根据新的文档不再从交易码参数表取得交易描述，而是直接取入账拒绝表中的描述
        recorded.setTxnTransactionDescription(rejectedTransaction.getTransactionDescription());

        //交易日期
        recorded.setTxnTransactionDate(rejectedTransaction.getTransactionDate());
        //交易金额
        recorded.setTxnTransactionAmount(rejectedTransaction.getTransactionAmount());
        //交易币种
        recorded.setTxnTransactionCurrency(rejectedTransaction.getTransactionCurrency());
        //入账日期,入账拒绝表中的当前系统处理日（today）
        recorded.setTxnBillingDate(rejectedTransaction.getBillingDate());
        //入账金额
        recorded.setTxnBillingAmount(rejectedTransaction.getBillingAmount());
        //入账币种,交易级账户的币种（currency）
        recorded.setTxnBillingCurrency(rejectedTransaction.getBillingCurrency());
        //清算金额,交易级账户的累计利息（accrue_interest）
        recorded.setTxnSettlementAmount(rejectedTransaction.getBillingAmount());
        //清算币种,交易级账户的币种（currency）
        recorded.setTxnSettlementCurrency(rejectedTransaction.getBillingCurrency());
        //汇率
        recorded.setTxnExchangeRate(rejectedTransaction.getExchangeRate());
        //授权码
        recorded.setTxnAuthorizationCode(rejectedTransaction.getAuthorizationCode());
        //邮编
        recorded.setTxnZipCode(rejectedTransaction.getZipCode());
        //商户编号
        recorded.setTxnMerchantId(rejectedTransaction.getMerchantId());
        //商户名称
        recorded.setTxnMerchantName(rejectedTransaction.getMerchantName());
        //MCC
        recorded.setTxnMerchantCategoryCode(rejectedTransaction.getMerchantCategoryCode());
        //国家码
        recorded.setTxnCountryCode(rejectedTransaction.getCountryCode());
        //省份/州
        recorded.setTxnStateCode(rejectedTransaction.getStateCode());
        //城市
        recorded.setTxnCityCode(rejectedTransaction.getCityCode());
        //参考号
        recorded.setTxnReferenceNumber(rejectedTransaction.getReferenceNumber());
        //授权匹配标志,0=未匹配授权
        recorded.setTxnAuthorizationMatchIndicator(rejectedTransaction.getAuthorizationMatchIndicator());
        //是否恢复授权占用额度标志,N:不需要
        recorded.setTxnReleaseAuthorizationAmount(rejectedTransaction.getReleaseAuthorizationAmount());
        //授权交易对应额度节点编号
        recorded.setTxnLimitNodeId(rejectedTransaction.getLimitNodeId());
        //授权额度占用金额
        recorded.setTxnOutstandingAmount(rejectedTransaction.getOutstandingAmount());
        //交易对手银行号
        recorded.setTxnOpponentBankNumber(rejectedTransaction.getOpponentBankNumber());
        //交易对手账户号
        recorded.setTxnOpponentAccountNumber(rejectedTransaction.getOpponentAccountNumber());
        //交易对手账户名称
        recorded.setTxnOpponentAccountName(rejectedTransaction.getOpponentAccountName());
        //二级商户号
        recorded.setTxnSecondMerchantId(rejectedTransaction.getSecondMerchantId());
        //二级商户名称
        recorded.setTxnSecondMerchantName(rejectedTransaction.getSecondMerchantName());
        //POS输入方式
        recorded.setTxnPosEntryMode(rejectedTransaction.getPosEntryMode());
        //Visa ISA标识
        recorded.setTxnVisaChargeFlag(rejectedTransaction.getVisaChargeFlag());
        //RA标识
        recorded.setTxnReimbursementAttribute(rejectedTransaction.getReimbursementAttribute());
        //IFI标识
        recorded.setTxnIfiIndicator(rejectedTransaction.getIfiIndicator());
        //PSV标识
        recorded.setTxnPsvIndicator(rejectedTransaction.getPsvIndicator());
        //DCC标识
        recorded.setTxnDccIndicator(rejectedTransaction.getDccIndicator());
        //强制入账标识
        recorded.setTxnForcePostIndicator(rejectedTransaction.getForcePostIndicator());
        //降级交易标识
        recorded.setTxnFallBackIndicator(rejectedTransaction.getFallBackIndicator());
        //分期标识
        recorded.setTxnInstallmentIndicator(rejectedTransaction.getInstallmentIndicator());
        //分期订单号
        recorded.setTxnInstallmentOrderId(rejectedTransaction.getInstallmentOrderid());
        //分期期数
        recorded.setTxnInstallmentTerm(rejectedTransaction.getInstallmentTerm());
        //利率参数表id
        recorded.setTxnInterestTableId(rejectedTransaction.getInterestTableId());
        //费用参数表id
        recorded.setTxnFeeTableId(rejectedTransaction.getFeeTableId());
        //1:单信息 2:双信息
        recorded.setMessageIndicator(rejectedTransaction.getMessageIndicator());
        //商户号
        recorded.setTxnMerchantId(rejectedTransaction.getMerchantId());
        return recorded;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchWriter(List<? extends RejectedTransactionBO> items) {
        List<RejectedTransaction> rejectedTransList = new ArrayList<>();

        List<SettlementLogDTO> settlementLogDtos = new ArrayList<>();
        for (RejectedTransactionBO rejectedTransactionBO : items) {
            // 生成settlementLog
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            SettlementLogDTO settlementLogDTO = buildSettlementLog(rejectedTransactionBO.getRecorded(), sqlSession);
            if (settlementLogDTO == null) {
                continue;
            }
            settlementLogDtos.add(settlementLogDTO);

            if (rejectedTransactionBO.getRejectedTransaction() != null) {
                RejectedTransactionDTO rejectedTrans = rejectedTransactionBO.getRejectedTransaction();
                RejectedTransaction rejectedTransaction = new RejectedTransaction();
                rejectedTransaction.setRejectedTransactionId(rejectedTrans.getRejectedTransactionId());
                rejectedTransaction.setVersionNumber(1L);
                rejectedTransaction.setAlreadyHandled("1");
                rejectedTransaction.setUpdateTime(LocalDateTime.now());
                rejectedTrans.setUpdateBy(TransactionConstants.DEFAULT_USER);
                rejectedTransList.add(rejectedTransaction);
            }
        }

        // 写入settlementLog
        logger.info("Calling settlementLogService.addBatch: size={}", settlementLogDtos.size());
        settlementLogService.addBatch(settlementLogDtos);
        logger.info("SettlementLogService.addBatch completed");
        editRejectedTransaction(rejectedTransList);
    }

    /**
     * 根据入账日查询拒绝表
     *
     * @param billingDate
     * @return
     */
    @Override
    public List<RejectedTransactionDTO> findByBillingDate(LocalDate billingDate) {
        List<RejectedTransactionDTO> dtoList;
        try {
            List<RejectedTransaction> rejectedTransactions = rejectedTransactionSelfMapper.selectByPostingDate(billingDate, OrgNumberUtils.getOrg());
            return BeanMapping.copyList(rejectedTransactions, RejectedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Database exception occurred", e);
        }
        return null;
    }

    private SettlementLogDTO buildSettlementLog(RecordedBO recordedBO, SqlSession sqlSession) {
        SettlementLogDTO settlementLog = new SettlementLogDTO();
        settlementLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        settlementLog.setTxnAccountManageId(recordedBO.getTxnAccountManageId());
        settlementLog.setTxnCardNumber(recordedBO.getTxnCardNumber());
        settlementLog.setTxnOriginalTxnDate(recordedBO.getTxnOriginalTransactionDate());
        settlementLog.setTxnGlobalFlowNumber(recordedBO.getTxnGlobalFlowNumber());
        //入账方式,1：批量入账,0:实时入账
        settlementLog.setTxnPostMethod(recordedBO.getTxnPostMethod());
        //冲减交易费用标识,1=否
        settlementLog.setTxnReverseFeeIndicator(recordedBO.getTxnReverseFeeIndicator());
        //拒绝重入账标志,1=拒绝重入账交易
        settlementLog.setTxnRepostFromSuspend(recordedBO.getTxnRepostFromSuspend());
        //交易码
        settlementLog.setTxnTransactionCode(recordedBO.getTxnTransactionCode());
        //交易来源
        settlementLog.setTxnTransactionSource(recordedBO.getTxnTransactionSource());
        //2019-01-29，根据新的文档不再从交易码参数表取得交易描述，而是直接取入账拒绝表中的描述
        settlementLog.setTxnTransactionDescription(recordedBO.getTxnTransactionDescription());
        //交易时间
        settlementLog.setTxnTransactionDate(recordedBO.getTxnTransactionDate());
        //交易金额
        settlementLog.setTxnTransactionAmount(recordedBO.getTxnTransactionAmount());
        //交易币种
        settlementLog.setTxnTransactionCurrency(recordedBO.getTxnTransactionCurrency());
        //入账日期 赋值空，入账逻辑自动获取
        settlementLog.setTxnBillingDate(null);
        //入账金额
        settlementLog.setTxnBillingAmount(recordedBO.getTxnBillingAmount());
        //入账币种,交易级账户的币种（currency）
        settlementLog.setTxnBillingCurrency(recordedBO.getTxnBillingCurrency());
        //清算金额,交易级账户的累计利息（accrue_interest）
        settlementLog.setTxnSettlementAmount(recordedBO.getTxnSettlementAmount());
        //清算币种,交易级账户的币种（currency）
        settlementLog.setTxnSettlementCurrency(recordedBO.getTxnSettlementCurrency());
        //汇率
        settlementLog.setTxnExchangeRate(recordedBO.getTxnExchangeRate());
        //授权码
        settlementLog.setTxnAuthorizationCode(recordedBO.getTxnAuthorizationCode());
        //邮编
        settlementLog.setTxnZipCode(recordedBO.getTxnZipCode());
        //商户编号
        settlementLog.setTxnMerchantId(recordedBO.getTxnMerchantId());
        //商户名称
        settlementLog.setTxnMerchantName(recordedBO.getTxnMerchantName());
        //MCC
        settlementLog.setTxnMerchantCategoryCode(recordedBO.getTxnMerchantCategoryCode());
        //国家码
        settlementLog.setTxnCountryCode(recordedBO.getTxnCountryCode());
        //省份/州
        settlementLog.setTxnStateCode(recordedBO.getTxnStateCode());
        //城市
        settlementLog.setTxnCityCode(recordedBO.getTxnCityCode());
        //参考号
        settlementLog.setTxnReferenceNumber(recordedBO.getTxnReferenceNumber());
        //授权匹配标志,0=未匹配授权
        settlementLog.setTxnAuthMatchIndicator(recordedBO.getTxnAuthorizationMatchIndicator());
        //是否恢复授权占用额度标志,N:不需要
        settlementLog.setTxnReleaseAuthAmount(recordedBO.getTxnReleaseAuthorizationAmount());
        //授权交易对应额度节点编号
        settlementLog.setTxnLimitNodeId(recordedBO.getTxnLimitNodeId());
        //授权额度占用金额
        settlementLog.setTxnOutstandingAmount(recordedBO.getTxnOutstandingAmount());
        //交易对手银行号
        settlementLog.setTxnOpponentBankNum(recordedBO.getTxnOpponentBankNumber());
        //交易对手账户号
        settlementLog.setTxnOpponentAccountNum(recordedBO.getTxnOpponentAccountNumber());
        //交易对手账户名称
        settlementLog.setTxnOpponentAccountName(recordedBO.getTxnOpponentAccountName());
        //二级商户号
        settlementLog.setTxnSecondMerchantId(recordedBO.getTxnSecondMerchantId());
        //二级商户名称
        settlementLog.setTxnSecondMerchantName(recordedBO.getTxnSecondMerchantName());
        //POS输入方式
        settlementLog.setTxnPosEntryMode(recordedBO.getTxnPosEntryMode());
        //Visa ISA标识
        settlementLog.setTxnVisaChargeFlag(recordedBO.getTxnVisaChargeFlag());
        //RA标识
        settlementLog.setTxnReimbursementAttribute(recordedBO.getTxnReimbursementAttribute());
        //IFI标识
        settlementLog.setTxnIfiIndicator(recordedBO.getTxnIfiIndicator());
        //PSV标识
        settlementLog.setTxnPsvIndicator(recordedBO.getTxnPsvIndicator());
        //DCC标识
        settlementLog.setTxnDccIndicator(recordedBO.getTxnDccIndicator());
        //强制入账标识
        settlementLog.setTxnForcePostIndicator(recordedBO.getTxnForcePostIndicator());
        //降级交易标识
        settlementLog.setTxnFallBackIndicator(recordedBO.getTxnFallBackIndicator());
        //分期标识
        settlementLog.setTxnInstallmentIndicator(recordedBO.getTxnInstallmentIndicator());
        //分期订单号
        settlementLog.setTxnInstallmentOrderId(recordedBO.getTxnInstallmentOrderId());
        //分期期数
        settlementLog.setTxnInstallmentTerm(recordedBO.getTxnInstallmentTerm());
        //利率参数表id
        settlementLog.setTxnInterestTableId(recordedBO.getTxnInterestTableId());
        //费用参数表id
        settlementLog.setTxnFeeTableId(recordedBO.getTxnFeeTableId());
        //单信息 双信息
        settlementLog.setMessageIndicator(recordedBO.getMessageIndicator());

        String customerId = null;
        AccountManagementInfoMapper accMapper = sqlSession.getMapper(AccountManagementInfoMapper.class);
        CardAuthorizationInfoMapper cardAuthMapper = sqlSession.getMapper(CardAuthorizationInfoMapper.class);
        if (StringUtils.isNotEmpty(settlementLog.getTxnAccountManageId())) {
            AccountManagementInfo accountManagementInfo = accMapper.selectByPrimaryKey(settlementLog.getTxnAccountManageId());
            customerId = accountManagementInfo == null ? null : accountManagementInfo.getCustomerId();
        }

        if (customerId == null && StringUtils.isNotEmpty(settlementLog.getTxnCardNumber())) {
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthMapper.selectByPrimaryKey(settlementLog.getTxnCardNumber(), OrgNumberUtils.getOrg());
            if (cardAuthorizationInfo == null) {
                logger.error("Card authorization table not found for card: {}", settlementLog.getTxnCardNumber());
                return null;
            }
            if (TransactionConstants.PRIMARY_INDICATOR.equals(cardAuthorizationInfo.getRelationshipIndicator())) {
                //主卡
                customerId = cardAuthorizationInfo.getPrimaryCustomerId();
            } else {
                //附卡
                customerId = cardAuthorizationInfo.getSupplementaryCustomerId();
            }

            if (StringUtils.isNotEmpty(customerId)) {
                settlementLog.setCustomerId(customerId);
                CardAuthorizationDTO cardAuthorizationDTO = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
                logger.info("Calling partitionKeyInitService.partitionKeyGenerator: cardNumber={}", cardAuthorizationDTO.getCardNumber());
                int partitionKey = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);
                logger.info("PartitionKeyInitService.partitionKeyGenerator completed: partitionKey={}", partitionKey);
                settlementLog.setPartitionKey(partitionKey);
            } else {
                logger.error("Customer number not exist for card: {}", settlementLog.getTxnCardNumber());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL, TransactionRepDetailEnum.CUSTOMERNUMBER_NOT_EXIST);
            }
        }


        settlementLog.setCreateTime(LocalDateTime.now());
        settlementLog.setUpdateTime(LocalDateTime.now());
        settlementLog.setUpdateBy("admin");
        settlementLog.setVersionNumber(1L);
        return settlementLog;
    }

    private void editRejectedTransaction(List<RejectedTransaction> rejectedTransList) {
        if (CollectionUtils.isNotEmpty(rejectedTransList)) {

            //批量更新，防止单个sql过长，乐观锁控制更新准确性
            if (rejectedTransList.size() > maxUpdate) {
                List<List<RejectedTransaction>> acctUpdateList = ListUtils.fixedGrouping(rejectedTransList, maxUpdate);
                for (List<RejectedTransaction> rejectedTransactions : acctUpdateList) {
                    int i = rejectedTransactionSelfMapper.batchUpdateAlreadyHandledByIds(rejectedTransactions);
                    //乐观锁控制
                    //目前会返回-1，TODO

                }

            } else {
                int i = rejectedTransactionSelfMapper.batchUpdateAlreadyHandledByIds(rejectedTransList);
                //乐观锁控制
                //目前会返回-1，TODO

            }
        }
    }
}
