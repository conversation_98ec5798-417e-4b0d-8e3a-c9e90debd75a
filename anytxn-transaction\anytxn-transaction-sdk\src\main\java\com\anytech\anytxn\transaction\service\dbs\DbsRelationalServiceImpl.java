package com.anytech.anytxn.transaction.service.dbs;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.DdaMaintenanceFileDetailDTO;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.GiroEnrolmentRejectReportDTO;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.GiroEnrolmentReportDTO;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.GiroEnrolmentUpdateReportDTO;
import com.anytech.anytxn.transaction.base.enums.FileExceptionDataTypeEnum;
import com.anytech.anytxn.transaction.base.enums.GiroStatusEnum;
import com.anytech.anytxn.transaction.base.service.IDbsRelationalService;
import com.anytech.anytxn.transaction.mapper.file.FileProcessExceptionInfoMapper;
import com.anytech.anytxn.transaction.base.domain.model.file.FileProcessExceptionInfo;
import com.anytech.anytxn.transaction.service.TransactionFileWriteHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class DbsRelationalServiceImpl implements IDbsRelationalService {

    private static final Logger logger = LoggerFactory.getLogger(DbsRelationalServiceImpl.class);
    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Resource
    private TransactionFileWriteHandler transactionFileWriteHandler;
    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Resource
    private IMaintenanceLogBisService maintenanceLogService;
    @Resource
    private ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;
    @Resource
    private FileProcessExceptionInfoMapper processExceptionInfoMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void writeInRecord(List<? extends DdaMaintenanceFileDetailDTO> ddaMaintenanceFileDetailDto,
                              AnytxnFilePathConfig dbsInFilePathConfig,
                              AnytxnFilePathConfig reportOutFilePathConfig,
                              String format2, String fileName,
                              OrganizationInfoResDTO organizationInfo) {
        if (!CollectionUtils.isEmpty(ddaMaintenanceFileDetailDto)) {
            List<AccountManagementInfo> managementInfos = new ArrayList<>(16);
            List<GiroEnrolmentReportDTO> giroEnrolmentReportDTOS = new ArrayList<>(16);
            List<GiroEnrolmentUpdateReportDTO> giroEnrolmentUpdateReportDTOS = new ArrayList<>(16);
            List<GiroEnrolmentReportDTO> giroDeEnrolmentReportDtos = new ArrayList<>(16);
            List<GiroEnrolmentRejectReportDTO> giroEnrolmentRejectReportDTOS = new ArrayList<>(16);
            Set<String> space = new HashSet<>();
            space.add("");
            space.add(" ");
            for (DdaMaintenanceFileDetailDTO detailDto : ddaMaintenanceFileDetailDto) {
                String autoPaymentType = detailDto.getFiller8().substring(0, 1);
                //每张卡都有一个CO-BRAND LOYALTY NUMBER
                String ddaReference = detailDto.getDdaReference();
                String status = detailDto.getStatus();
                String autoPaymentFlag = "1";
                if (!StringUtils.isEmpty(status)) {
                    if ("T".equals(status)) {
                        autoPaymentFlag = "0";
                    }
                }
                String receivingAccountNumber = detailDto.getReceivingAccountNumber();
                String receivingBankBic = detailDto.getReceivingBankBic();
                String newReceivingAccountNumber = detailDto.getNewReceivingAccountNumber();
                String newReceivingBankBic = detailDto.getNewReceivingBankBic();
                List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByLoyaltyNumberAndOrganizationNumber(OrgNumberUtils.getOrg(), ddaReference);
                if (CollectionUtils.isEmpty(cardAuthorizationInfos)) {
                    logger.info("Card not found: loyaltyNumber={}, organizationNumber={}", detailDto.getDdaReference(),
                            organizationInfo.getOrganizationNumber());
                    FileProcessExceptionInfo exceptionInfo = FileProcessExceptionInfo.builder()
                            .id(sequenceIdGen.generateId(TenantUtils.getTenantId()))
                            .fileName(fileName)
                            .exceptionDataType(FileExceptionDataTypeEnum.LOYALTY_NUM.getCode()
                                    + ";" + FileExceptionDataTypeEnum.ORG_NUM.getCode())
                            .exceptionData(detailDto.getDdaReference() + ";" + organizationInfo.getOrganizationNumber())
                            .exceptionDesc("根据积分号+机构号查询卡授权信息表,数据不存在")
                            .exceptionTime(organizationInfo.getNextProcessingDay().atTime(LocalTime.now()))
                            .organizationNumber(organizationInfo.getOrganizationNumber())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .updateBy("admin")
                            .versionNumber(1L)
                            .build();
                    processExceptionInfoMapper.insertSelective(exceptionInfo);
                    continue;
                }
                //获取持卡人的第一张卡
//                List<CardAuthorizationInfo> collect = cardAuthorizationInfos.stream().sorted(Comparator.comparing(CardAuthorizationInfo::getCreateTime)).collect(Collectors.toList());
//                CardAuthorizationInfo authorizationInfo = collect.stream().filter(c -> "P".equals(c.getRelationshipIndicator())).findFirst().orElse(null);
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfos.get(0);
                String productNumber = cardAuthorizationInfo.getProductNumber();
                ParmCardProductInfo parmCardProductInfo = cardProductInfoSelfMapper.selectOrgNumberAndProNumber(cardAuthorizationInfo.getOrganizationNumber(), productNumber);
                if (parmCardProductInfo == null) {
                    logger.error("Card product parameter not found: organizationNumber={}, productNumber={}", cardAuthorizationInfo.getOrganizationNumber(), productNumber);
                    FileProcessExceptionInfo exceptionInfo = FileProcessExceptionInfo.builder()
                            .id(sequenceIdGen.generateId(TenantUtils.getTenantId()))
                            .fileName(fileName)
                            .exceptionDataType(FileExceptionDataTypeEnum.ORG_NUM.getCode()
                                    + ";" + FileExceptionDataTypeEnum.CARD_PRO.getCode())
                            .exceptionData(organizationInfo.getOrganizationNumber() + ";" + productNumber)
                            .exceptionDesc("根据机构号:{},卡产品号:{},查询卡产品参数,数据不存在")
                            .exceptionTime(organizationInfo.getNextProcessingDay().atTime(LocalTime.now()))
                            .organizationNumber(organizationInfo.getOrganizationNumber())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .updateBy("admin")
                            .versionNumber(1L)
                            .build();
                    processExceptionInfoMapper.insertSelective(exceptionInfo);
                    continue;
                }
                String accountProductNumber = parmCardProductInfo.getAccountProductNumber();
                String customerId = cardAuthorizationInfo.getPrimaryCustomerId();

                if (!StringUtils.isEmpty(parmCardProductInfo.getLiability()) && "C".equals(parmCardProductInfo.getLiability())) {
                    customerId = cardAuthorizationInfo.getCorporateCustomerId();
                }

                //CustomerAuthorizationInfo customerAuthorizationInfo = authorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), customerId);
                AccountManagementInfo managementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(customerId, accountProductNumber, OrgNumberUtils.getOrg());
                if (managementInfo == null) {
                    logger.info("Account management not found: customerId={}, accountProductNumber={}, organizationNumber={}", customerId, accountProductNumber, OrgNumberUtils.getOrg());
                    FileProcessExceptionInfo exceptionInfo = FileProcessExceptionInfo.builder()
                            .id(sequenceIdGen.generateId(TenantUtils.getTenantId()))
                            .fileName(fileName)
                            .exceptionDataType(FileExceptionDataTypeEnum.CUS_ID.getCode()
                                    + ";" + FileExceptionDataTypeEnum.ACCT_PRO.getCode()
                                    + ";" + organizationInfo.getOrganizationNumber())
                            .exceptionData(customerId + ";" + accountProductNumber)
                            .exceptionDesc("根据客户号+账产品号+机构号查询管理账户,数据不存在")
                            .exceptionTime(organizationInfo.getNextProcessingDay().atTime(LocalTime.now()))
                            .organizationNumber(organizationInfo.getOrganizationNumber())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .updateBy("admin")
                            .versionNumber(1L)
                            .build();
                    processExceptionInfoMapper.insertSelective(exceptionInfo);
                    continue;
                }
                //自扣账户名称
                String receiverName = detailDto.getReceiverName();


                //获取公司卡标志
                String corporateIndicator = cardAuthorizationInfo.getCorporateIndicator();
                String accountManagementId = managementInfo.getAccountManagementId();
                Long versionNumber = managementInfo.getVersionNumber();
                //if (!StringUtils.isEmpty(receivingAccountNumber) && !StringUtils.isEmpty(managementInfo.getAutoPaymentAcctNumber())) {
                //first,需要银行名称一样，不一样出exception
                if (GiroStatusEnum.STATUS_Z.getCode().equals(status)) {
                    logger.info("Status Z processing");
                    if (StringUtils.isEmpty(newReceivingBankBic) && space.contains(newReceivingBankBic)) {
                        logger.info("Status Z: new bank name is empty, skip bank name comparison");
                        updateMaintenanceLog(managementInfo, receivingAccountNumber, newReceivingAccountNumber, fileName, receivingBankBic, newReceivingBankBic, status);

                    } else {
                        logger.info("Status Z: new bank name is not empty");
                        if (receivingBankBic.equals(newReceivingBankBic)) {
                            logger.info("Status Z: old bank name equals new bank name in file");
                            updateMaintenanceLog(managementInfo, receivingAccountNumber, newReceivingAccountNumber, fileName, receivingBankBic, newReceivingBankBic, status);
                        } else {
                            logger.info("Status Z: old bank name not equals new bank name in file, skip maintenance");
                        }
                    }
                } else if (GiroStatusEnum.STATUS_A.getCode().equals(status)) {
                    if (!receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                        //写维护日志
                        AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                        newAccountManagementInfo.setAutoPaymentAcctNumber(receivingAccountNumber);
                        MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                        maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                        maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                        maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                        maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                        maintenanceLog.setOriginalValue(managementInfo.getAutoPaymentAcctNumber());
                        maintenanceLog.setUpdatedValue(receivingAccountNumber);
                        maintenanceLog.setOperatorId(fileName);
                        maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                    }

                }
                if (GiroStatusEnum.STATUS_B.getCode().equals(status) && !StringUtils.isEmpty(newReceivingBankBic) && !newReceivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                    logger.info("Status B: new bank name is not empty and different from database");
                    if (receivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                        logger.info("Status B: old bank name in file equals database value");
                        if (StringUtils.isEmpty(newReceivingAccountNumber) && space.contains(newReceivingAccountNumber)) {
                            logger.info("Status B: new bank account is empty, skip account comparison");
                            updateMaintenanceLog(managementInfo, receivingAccountNumber, newReceivingAccountNumber, fileName, receivingBankBic, newReceivingBankBic, status);
                        } else {
                            logger.info("Status B: new bank account is not empty");
                            if (receivingAccountNumber.equals(newReceivingAccountNumber)) {
                                logger.info("Status B: new bank account equals old bank account in file");
                                updateMaintenanceLog(managementInfo, receivingAccountNumber, newReceivingAccountNumber, fileName, receivingBankBic, newReceivingBankBic, status);
                            }
                        }

                    }
                } else if (GiroStatusEnum.STATUS_A.getCode().equals(status)) {
                    if (!receivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                        //写维护日志
                        AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                        newAccountManagementInfo.setLocalAutoPayBankName(receivingBankBic);
                        MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                        maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                        maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                        maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                        maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                        maintenanceLog.setOriginalValue(managementInfo.getLocalAutoPayBankName());
                        maintenanceLog.setUpdatedValue(receivingBankBic);
                        maintenanceLog.setOperatorId(fileName);
                        maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                    }
                }
                //检查状态 生成相应的报表
                if (!StringUtils.isEmpty(status)) {
                    String autoPaymentBranchNumber = managementInfo.getAutoPaymentBranchNumber();
                    String autoPaymentBankNum = managementInfo.getAutoPaymentBankNumber();
                    String localAutoPayBankName = StringUtils.isEmpty(managementInfo.getLocalAutoPayBankName()) ? "" : managementInfo.getLocalAutoPayBankName().trim();
                    String autoPaymentAcctNumber = StringUtils.isEmpty(managementInfo.getAutoPaymentAcctNumber()) ? "" : managementInfo.getAutoPaymentAcctNumber().trim();
                    if (GiroStatusEnum.STATUS_A.getCode().equals(status)) {
                        AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
                        if (!StringUtils.isEmpty(autoPaymentType) && !StringUtils.isEmpty(managementInfo.getAutoPaymentType())) {
                            if ("0".equals(managementInfo.getAutoPaymentType()) && StringUtils.isEmpty(autoPaymentAcctNumber) && StringUtils.isEmpty(autoPaymentBranchNumber) && StringUtils.isEmpty(autoPaymentBankNum)) {
                                if ("0".equals(autoPaymentType)) {
                                    autoPaymentType = "1";
                                } else if ("1".equals(autoPaymentType)) {
                                    autoPaymentType = "2";
                                }
                            }
                            if (!"0".equals(managementInfo.getAutoPaymentType()) && "1".equals(autoPaymentFlag)) {
                                if ("0".equals(autoPaymentType) && ("4".equals(managementInfo.getAutoPaymentType())
                                        || "3".equals(managementInfo.getAutoPaymentType()))) {
                                    autoPaymentType = "3";
                                } else if ("1".equals(autoPaymentType) && ("3".equals(managementInfo.getAutoPaymentType())
                                        || "4".equals(managementInfo.getAutoPaymentType()))) {
                                    autoPaymentType = "4";
                                }
                                accountManagementInfo.setAutoPaymentAcctNumber(detailDto.getReceivingAccountNumber());
                                accountManagementInfo.setAutoPaymentBranchNumber("9999");
                                accountManagementInfo.setAutoPaymentBankNumber("9999");
                            }
                            if (("0".equals(managementInfo.getAutoPaymentType()) || "1".equals(managementInfo.getAutoPaymentType())
                                    || "2".equals(managementInfo.getAutoPaymentType()))
                                    && !StringUtils.isEmpty(autoPaymentAcctNumber)) {
                                if ("0".equals(autoPaymentType)) {
                                    autoPaymentType = "1";
                                } else if ("1".equals(autoPaymentType)) {
                                    autoPaymentType = "2";
                                }
                            }
                        }
                        //自扣类型更新写维护日志
                        if (!autoPaymentType.equals(managementInfo.getAutoPaymentType() == null ? "" : managementInfo.getAutoPaymentType())) {
                            //写维护日志
                            AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                            newAccountManagementInfo.setAutoPaymentType(autoPaymentType);
                            MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                            maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                            maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                            maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                            maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                            maintenanceLog.setOriginalValue(managementInfo.getAutoPaymentType());
                            maintenanceLog.setUpdatedValue(autoPaymentType);
                            maintenanceLog.setOperatorId(fileName);
                            maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                        }
                        accountManagementInfo.setAccountManagementId(accountManagementId);
                        accountManagementInfo.setAutoPaymentType(autoPaymentType);
                        accountManagementInfo.setReceivingBankBic(receivingBankBic);
                        accountManagementInfo.setAutoPaymentAcctNumber(receivingAccountNumber);
                        accountManagementInfo.setLocalAutoPayBankName(receivingBankBic);
                        accountManagementInfo.setReceiverName(receiverName);
                        accountManagementInfo.setAutoPayAccountNameLocal(receiverName);
                        accountManagementInfo.setVersionNumber(versionNumber);
                        accountManagementInfo.setAutoPaymentFlag(autoPaymentFlag);
                        accountManagementInfo.setUpdateTime(LocalDateTime.now());
                        accountManagementInfo.setUpdateBy(fileName);
                        accountManagementInfo.setExternalReferenceNumber(getCardNumber(cardAuthorizationInfo, managementInfo.getExternalReferenceNumber()));
                        //centralBillingCrdNumber赋值
                        if ("C".equals(managementInfo.getLiability())) {
                            if (StringUtils.isEmpty(managementInfo.getCentralBillingGiroCardNumber())) {
                                logger.info("Corporate card account {} central billing card number is empty, need to assign value", managementInfo.getAccountManagementId());
                                accountManagementInfo.setCentralBillingGiroCardNumber(cardAuthorizationInfo.getCardNumber());
                            } else {
                                logger.info("Corporate card account {} central billing card number has value: {}", managementInfo.getAccountManagementId(), managementInfo.getCentralBillingGiroCardNumber());
                            }
                        }
                        updateAccountManagementInfo(accountManagementInfo);
//                        managementInfos.add(accountManagementInfo);
                    } else if (GiroStatusEnum.STATUS_T.getCode().equals(status)) {
                        String giroBankDetails = receivingBankBic + detailDto.getReceivingAccountNumber();
                        String omniCardBankDetails = localAutoPayBankName + autoPaymentAcctNumber;
                        String paymentType = managementInfo.getAutoPaymentType();
                        if (giroBankDetails.equals(omniCardBankDetails) && !"0".equals(paymentType)) {
                            paymentType = "0";
                            //写维护日志
                            AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                            newAccountManagementInfo.setAutoPaymentType(paymentType);
                            MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                            maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                            maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                            maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                            maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                            maintenanceLog.setOriginalValue(managementInfo.getAutoPaymentType());
                            maintenanceLog.setUpdatedValue(paymentType);
                            maintenanceLog.setOperatorId(fileName);
                            maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                            //更新管理账户
                            AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
                            accountManagementInfo.setAccountManagementId(accountManagementId);
                            accountManagementInfo.setAutoPaymentType(paymentType);
                            accountManagementInfo.setVersionNumber(versionNumber);
                            accountManagementInfo.setAutoPaymentFlag(autoPaymentFlag);
                            accountManagementInfo.setUpdateTime(LocalDateTime.now());
                            accountManagementInfo.setUpdateBy(fileName);
                            accountManagementInfo.setExternalReferenceNumber(getCardNumber(cardAuthorizationInfo, managementInfo.getExternalReferenceNumber()));
                            updateAccountManagementInfo(accountManagementInfo);
//                            managementInfos.add(accountManagementInfo);
                        }
                    } else if (GiroStatusEnum.STATUS_Z.getCode().equals(status) && !StringUtils.isEmpty(newReceivingAccountNumber)) {
                        //需要银行名称一样，不一样出exception
                        if (receivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                            if (detailDto.getReceivingAccountNumber().equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                                AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
                                accountManagementInfo.setAccountManagementId(accountManagementId);
                                accountManagementInfo.setAutoPaymentAcctNumber(receivingAccountNumber);
                                accountManagementInfo.setVersionNumber(versionNumber);
                                accountManagementInfo.setAutoPaymentFlag(autoPaymentFlag);
                                accountManagementInfo.setUpdateTime(LocalDateTime.now());
                                accountManagementInfo.setUpdateBy(fileName);
                                accountManagementInfo.setExternalReferenceNumber(getCardNumber(cardAuthorizationInfo, managementInfo.getExternalReferenceNumber()));
                                updateAccountManagementInfo(accountManagementInfo);
//                            managementInfos.add(accountManagementInfo);
                            } else {
                                logger.info("Status Z: auto payment account number different, not updating management account: {}, {}", detailDto.getReceivingAccountNumber(), managementInfo.getAutoPaymentAcctNumber());
                            }
                        } else {
                            logger.info("Status Z: bank name different, not updating management account: {}, {}", receivingBankBic, managementInfo.getLocalAutoPayBankName());
                        }

                    } else if (GiroStatusEnum.STATUS_B.getCode().equals(status) && !StringUtils.isEmpty(newReceivingBankBic)) {
                        if (detailDto.getReceivingBankBic().equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                            if (receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                                AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
                                accountManagementInfo.setAccountManagementId(accountManagementId);
                                accountManagementInfo.setReceivingBankBic(receivingBankBic);
                                accountManagementInfo.setLocalAutoPayBankName(receivingBankBic);
                                accountManagementInfo.setVersionNumber(versionNumber);
                                accountManagementInfo.setAutoPaymentFlag(autoPaymentFlag);
                                accountManagementInfo.setUpdateTime(LocalDateTime.now());
                                accountManagementInfo.setUpdateBy(fileName);
                                accountManagementInfo.setExternalReferenceNumber(getCardNumber(cardAuthorizationInfo, managementInfo.getExternalReferenceNumber()));
                                updateAccountManagementInfo(accountManagementInfo);
//                        managementInfos.add(accountManagementInfo);
                            } else {
                                logger.info("Status B: auto payment account number different, not updating management account: {}, {}", receivingAccountNumber, managementInfo.getAutoPaymentAcctNumber());
                            }
                        } else {
                            logger.info("Status B: bank name different, not updating management account: {}, {}", detailDto.getReceivingBankBic(), managementInfo.getLocalAutoPayBankName());
                        }

                    }
                    checkStatus(giroEnrolmentReportDTOS, giroEnrolmentUpdateReportDTOS, giroDeEnrolmentReportDtos, giroEnrolmentRejectReportDTOS, status, detailDto, autoPaymentType, managementInfo, corporateIndicator, receivingBankBic);
                }
            }
            //写出报表
            if (!CollectionUtils.isEmpty(giroEnrolmentReportDTOS)) {
                List<String> strings = new ArrayList<>(16);
                for (GiroEnrolmentReportDTO giroEnrolmentReportDto : giroEnrolmentReportDTOS) {
                    strings.add(giroEnrolmentReportDto.toString());
                }
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6201-NewGiroEnrolmentReport.TXT"), strings);
            } else {
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6201-NewGiroEnrolmentReport.TXT"), null);
            }
            if (!CollectionUtils.isEmpty(giroEnrolmentUpdateReportDTOS)) {
                List<String> strings = new ArrayList<>(16);
                for (GiroEnrolmentUpdateReportDTO enrolmentUpdateReportDto : giroEnrolmentUpdateReportDTOS) {
                    strings.add(enrolmentUpdateReportDto.toString());
                }
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6202-GiroEnrolmentUpdateReport.TXT"), strings);
            } else {
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6202-GiroEnrolmentUpdateReport.TXT"), null);
            }
            if (!CollectionUtils.isEmpty(giroDeEnrolmentReportDtos)) {
                List<String> strings = new ArrayList<>(16);
                for (GiroEnrolmentReportDTO giroEnrolmentReportDto : giroDeEnrolmentReportDtos) {
                    strings.add(giroEnrolmentReportDto.toString());
                }
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6203-GiroDeEnrolmentReport.TXT"), strings);
            } else {
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6203-GiroDeEnrolmentReport.TXT"), null);
            }
            if (!CollectionUtils.isEmpty(giroEnrolmentRejectReportDTOS)) {
                List<String> strings = new ArrayList<>(16);
                for (GiroEnrolmentRejectReportDTO rejectReportDto : giroEnrolmentRejectReportDTOS) {
                    strings.add(rejectReportDto.toString());
                }
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6204-GiroEnrolmentExceptionReport.TXT"), strings);
            } else {
                transactionFileWriteHandler.outFile(getReportName(reportOutFilePathConfig, "DCPM6204-GiroEnrolmentExceptionReport.TXT"), null);
            }
        }
    }

    private void updateMaintenanceLog(AccountManagementInfo managementInfo, String receivingAccountNumber, String newReceivingAccountNumber, String fileName, String receivingBankBic, String newReceivingBankBic, String status) {
        if (GiroStatusEnum.STATUS_Z.getCode().equals(status)) {
            logger.info("Status Z processing step 1");
            if (receivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                logger.info("Status Z: old bank name in file equals database value, step 2");
                if (receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                    logger.info("Status Z: old bank account in file equals database value, step 3");
                    if (!StringUtils.isEmpty(newReceivingAccountNumber) && !newReceivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                        logger.info("Status Z: new bank account is not empty and different from database, start maintenance, step 4");
                        //}
                        // if (!StringUtils.isEmpty(newReceivingAccountNumber) && !newReceivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null?"":managementInfo.getAutoPaymentAcctNumber())){
                        receivingAccountNumber = newReceivingAccountNumber;
                        //写维护日志
                        AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                        newAccountManagementInfo.setAutoPaymentAcctNumber(receivingAccountNumber);
                        MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                        maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                        maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                        maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                        maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                        maintenanceLog.setOriginalValue(managementInfo.getAutoPaymentAcctNumber());
                        maintenanceLog.setUpdatedValue(receivingAccountNumber);
                        maintenanceLog.setOperatorId(fileName);
                        maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                    } else {
                        logger.info("Status Z: new bank account is empty or equals database value, skip maintenance, step 4");
                    }
                } else {
                    logger.info("Status Z: old bank account in file not equals database value, skip maintenance, step 3");
                }
            } else {
                logger.info("Status Z: old bank name in file not equals database value, skip maintenance, step 2");
            }
        } else if (GiroStatusEnum.STATUS_B.getCode().equals(status)) {
            logger.info("Status B processing step 1");
            if (receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                logger.info("Status B: old bank account in file equals database value, start maintenance, step 2");
                // if (!newReceivingBankBic.isEmpty() && !newReceivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null?"":managementInfo.getLocalAutoPayBankName())){
                receivingBankBic = newReceivingBankBic;
                //写维护日志
                AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(managementInfo, AccountManagementInfo.class);
                newAccountManagementInfo.setLocalAutoPayBankName(receivingBankBic);
                MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                maintenanceLog.setPrimaryKeyValue(managementInfo.getAccountManagementId());
                maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                maintenanceLog.setOriginalValue(managementInfo.getLocalAutoPayBankName());
                maintenanceLog.setUpdatedValue(receivingBankBic);
                maintenanceLog.setOperatorId(fileName);
                maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, managementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                // }
            } else {
                logger.info("Status B: old bank account in file not equals database value, skip maintenance, step 2");
            }
        }

    }

    /**
     * 检查状态
     *
     * @param giroEnrolmentReportDTOS       List<GiroEnrolmentReportDto>
     * @param giroEnrolmentUpdateReportDTOS List<GiroEnrolmentUpdateReportDto>
     * @param giroDeEnrolmentReportDtos     List<GiroEnrolmentReportDto>
     * @param giroEnrolmentRejectReportDTOS List<GiroEnrolmentRejectReportDto>
     * @param status                        String
     * @param detailDto                     DdaMaintenanceFileDetailDto
     * @param autoPaymentType               String
     * @param managementInfo                AccountManagementInfo
     * @param corporateIndicator            String
     */
    private void checkStatus(List<GiroEnrolmentReportDTO> giroEnrolmentReportDTOS,
                             List<GiroEnrolmentUpdateReportDTO> giroEnrolmentUpdateReportDTOS,
                             List<GiroEnrolmentReportDTO> giroDeEnrolmentReportDtos,
                             List<GiroEnrolmentRejectReportDTO> giroEnrolmentRejectReportDTOS,
                             String status, DdaMaintenanceFileDetailDTO detailDto, String autoPaymentType,
                             AccountManagementInfo managementInfo, String corporateIndicator, String receivingBankBic) {
        logger.info("Giro auto payment enrollment file status: {}", status);
        String giroBankDetails = receivingBankBic + detailDto.getReceivingAccountNumber();
        String localAutoPayBankName = StringUtils.isEmpty(managementInfo.getLocalAutoPayBankName()) ? "" : managementInfo.getLocalAutoPayBankName().trim();
        String autoPaymentAcctNumber = StringUtils.isEmpty(managementInfo.getAutoPaymentAcctNumber()) ? "" : managementInfo.getAutoPaymentAcctNumber().trim();
        String omniCardBankDetails = localAutoPayBankName + autoPaymentAcctNumber;
        String autoPayIndicator = managementInfo.getAutoPaymentType();
        String receivingAccountNumber = detailDto.getReceivingAccountNumber();
        String newReceivingBankBic = detailDto.getNewReceivingBankBic();
        String newRecevingAccountNumber = detailDto.getNewReceivingAccountNumber();
        Set<String> space = new HashSet<>();
        space.add("");
        space.add(" ");
        switch (status) {
            case "A":
                GiroEnrolmentReportDTO giroEnrolmentReportDto = generateNewGiroEnrolmentReport(detailDto, autoPaymentType, receivingBankBic);
                giroEnrolmentReportDTOS.add(giroEnrolmentReportDto);
                break;
            case "R":
            case "N":
            case "L":
                GiroEnrolmentRejectReportDTO giroEnrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, null, receivingBankBic);
                giroEnrolmentRejectReportDTOS.add(giroEnrolmentRejectReportDto);
                break;
            case "T":
                if (!StringUtils.isEmpty(giroBankDetails) && !StringUtils.isEmpty(omniCardBankDetails)) {
                    if (!giroBankDetails.equals(omniCardBankDetails)) {
                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "Bank details not match", receivingBankBic);
                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                    } else {
                        if (!StringUtils.isEmpty(autoPayIndicator)) {
                            //if ("3".equals(autoPayIndicator) || "4".equals(autoPayIndicator)) {
                            // GiroEnrolmentReportDto giroEnrolmentReportDto1 = generateNewGiroEnrolmentReport(detailDto, autoPayIndicator, receivingBankBic);
                            // giroDeEnrolmentReportDtos.add(giroEnrolmentReportDto1);
                            /*} else*/
                            if ("0".equals(autoPayIndicator)) {
                                if (!StringUtils.isEmpty(corporateIndicator)) {
                                    if (!"J".equals(corporateIndicator)) {
                                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "Autopay Inidcator = 0", receivingBankBic);
                                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                                    } else {
                                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "Corporate CB Account terminated", receivingBankBic);
                                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                                    }
                                }
                            } else {
                                GiroEnrolmentReportDTO giroEnrolmentReportDTO1 = generateNewGiroEnrolmentReport(detailDto, autoPayIndicator, receivingBankBic);
                                giroDeEnrolmentReportDtos.add(giroEnrolmentReportDTO1);
                            }
                        }
                    }
                }
                break;
            case "Z":
                if (!StringUtils.isEmpty(receivingAccountNumber) && !StringUtils.isEmpty(managementInfo.getAutoPaymentAcctNumber())) {
                    if (!receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber().trim())) {
                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "Receiving A/C Number not match", receivingBankBic);
                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                    } else {
                        GiroEnrolmentUpdateReportDTO giroEnrolmentUpdateReportDto = generateGiroEnrolmentUpdateReport(detailDto, autoPaymentType, receivingBankBic);
                        giroEnrolmentUpdateReportDTOS.add(giroEnrolmentUpdateReportDto);
                    }
                    if (StringUtils.isEmpty(detailDto.getNewReceivingAccountNumber())) {
                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "newReceivingAccountNumber must not be blank", receivingBankBic);
                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                    }
                    if (!receivingBankBic.equals(managementInfo.getLocalAutoPayBankName() == null ? "" : managementInfo.getLocalAutoPayBankName().trim())) {
                        GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "receivingBankBic and LocalAutoPayBankName not match", receivingBankBic);
                        giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                    }
                } else if (StringUtils.isEmpty(detailDto.getNewReceivingAccountNumber())) {
                    GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "newReceivingAccountNumber must not be blank", receivingBankBic);
                    giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                }
                break;
            case "B":
                if (!StringUtils.isEmpty(newReceivingBankBic) && !StringUtils.isEmpty(localAutoPayBankName)) {
                    if (receivingAccountNumber.equals(managementInfo.getAutoPaymentAcctNumber() == null ? "" : managementInfo.getAutoPaymentAcctNumber().trim())) {
                        logger.info("Status B processing step 1");
                        if (!StringUtils.isEmpty(newRecevingAccountNumber) && !space.contains(newRecevingAccountNumber) && receivingAccountNumber.equals(newRecevingAccountNumber)) {
                            logger.info("Status B processing step 2");
                            if (!newReceivingBankBic.equals(localAutoPayBankName)) {
                                logger.info("Status B: record report, step 3");
                                GiroEnrolmentUpdateReportDTO giroEnrolmentUpdateReportDto = generateGiroEnrolmentUpdateReport(detailDto, autoPaymentType, receivingBankBic);
                                giroEnrolmentUpdateReportDTOS.add(giroEnrolmentUpdateReportDto);
                            } else {
                                logger.info("Status B: not record report, step 3");
                            }
                        } else {
                            logger.info("Status B: not processing, step 2");
                        }
                    } else {
                        logger.info("Status B: not processing, step 1");
                    }
                } else if (StringUtils.isEmpty(newReceivingBankBic)) {
                    GiroEnrolmentRejectReportDTO enrolmentRejectReportDto = generateGiroEnrolmentExceptionReport(detailDto, autoPaymentType, "newReceivingBankBic must not be blank", receivingBankBic);
                    giroEnrolmentRejectReportDTOS.add(enrolmentRejectReportDto);
                }
                break;
            default:
                break;
        }
    }

    /**
     * generateNewGiroEnrolmentReport
     *
     * @param detailDto       DdaMaintenanceFileDetailDto
     * @param autoPaymentType String
     * @return GiroEnrolmentReportDto
     */
    private GiroEnrolmentReportDTO generateNewGiroEnrolmentReport(DdaMaintenanceFileDetailDTO detailDto, String autoPaymentType, String receivingBankBic) {
        String receivingAccountNumber = detailDto.getReceivingAccountNumber();
        String receiverName = detailDto.getReceiverName();
        String ddaReference = detailDto.getDdaReference();
        String status = detailDto.getStatus();
        GiroEnrolmentReportDTO giroEnrolmentReportDto = new GiroEnrolmentReportDTO();
        giroEnrolmentReportDto.setAutoPayIndicator(autoPaymentType);
        giroEnrolmentReportDto.setBankName(receivingBankBic);
        giroEnrolmentReportDto.setBankAcNumber(receivingAccountNumber);
        giroEnrolmentReportDto.setBankAcName(receiverName);
        giroEnrolmentReportDto.setCoBrandLoyaltyNo(ddaReference);
        giroEnrolmentReportDto.setStatus(status);
        return giroEnrolmentReportDto;
    }

    private GiroEnrolmentUpdateReportDTO generateGiroEnrolmentUpdateReport(DdaMaintenanceFileDetailDTO detailDto,
                                                                           String autoPayIndicator, String receivingBankBic) {
        GiroEnrolmentUpdateReportDTO giroEnrolmentUpdateReportDto = new GiroEnrolmentUpdateReportDTO();
        String receivingAccountNumber = detailDto.getReceivingAccountNumber();
        String receiverName = detailDto.getReceiverName();
        String ddaReference = detailDto.getDdaReference();
        String status = detailDto.getStatus();
        String newReceivingAccountNumber = detailDto.getNewReceivingAccountNumber();
        String newReceivingBankBic = detailDto.getNewReceivingBankBic();
        giroEnrolmentUpdateReportDto.setAutoPayIndicator(autoPayIndicator);
        giroEnrolmentUpdateReportDto.setBankName(receivingBankBic);
        giroEnrolmentUpdateReportDto.setBankAcNumber(receivingAccountNumber);
        giroEnrolmentUpdateReportDto.setBankAcName(receiverName);
        giroEnrolmentUpdateReportDto.setCoBrandLoyaltyNo(ddaReference);
        giroEnrolmentUpdateReportDto.setStatus(status);
        giroEnrolmentUpdateReportDto.setNewReceivingAccountNumber(newReceivingAccountNumber);
        giroEnrolmentUpdateReportDto.setNewReceivingBankBic(newReceivingBankBic);
        return giroEnrolmentUpdateReportDto;
    }

    /**
     * generateGiroEnrolmentExceptionReport
     *
     * @param detailDto       DdaMaintenanceFileDetailDto
     * @param autoPaymentType String
     * @return GiroEnrolmentRejectReportDto
     */
    private GiroEnrolmentRejectReportDTO generateGiroEnrolmentExceptionReport(DdaMaintenanceFileDetailDTO detailDto,
                                                                              String autoPaymentType,
                                                                              String reason, String receivingBankBic) {
        String receivingAccountNumber = detailDto.getReceivingAccountNumber();
        String receiverName = detailDto.getReceiverName();
        String ddaReference = detailDto.getDdaReference();
        String reasonCode = detailDto.getReasonCode();
        String status = detailDto.getStatus();
        if (StringUtils.isEmpty(reason)) {
            reason = detailDto.getOtherReason();
        }
        GiroEnrolmentRejectReportDTO giroEnrolmentRejectReportDto = new GiroEnrolmentRejectReportDTO();
        giroEnrolmentRejectReportDto.setAutoPayIndicator(autoPaymentType);
        giroEnrolmentRejectReportDto.setBankName(receivingBankBic);
        giroEnrolmentRejectReportDto.setBankAcNumber(receivingAccountNumber);
        giroEnrolmentRejectReportDto.setBankAcName(receiverName);
        giroEnrolmentRejectReportDto.setCoBrandLoyaltyNo(ddaReference);
        giroEnrolmentRejectReportDto.setReasonCode(reasonCode);
        giroEnrolmentRejectReportDto.setReason(reason);
        giroEnrolmentRejectReportDto.setStatus(status);
        return giroEnrolmentRejectReportDto;
    }

    /**
     * 获取写出报表路径+文件名
     *
     * @param dbsInFilePathConfig AnytxnFilePathConfig
     * @param fileName            String
     * @return String
     */
    private String getReportName(AnytxnFilePathConfig dbsInFilePathConfig, String fileName) {
        String filePath = dbsInFilePathConfig.getCommonPath();
        String pathForSystem = transactionFileWriteHandler.getPathForSystem();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format = dateTimeFormatter.format(nextProcessingDay);
        return filePath + pathForSystem + format + pathForSystem + fileName;
    }

    private String getCardNumber(CardAuthorizationInfo authorizationInfo, String existingERN) {
        if (!StringUtils.isEmpty(existingERN)) {
            return existingERN;
        }
        if (!ObjectUtils.isEmpty(authorizationInfo)) {
            String primaryCustomerId = authorizationInfo.getPrimaryCustomerId();
            String cardNumber = authorizationInfo.getCardNumber();
            String productNumber = authorizationInfo.getProductNumber();
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper
                    .selectByOrgNumAndCustomerIdAndRelationshipIndicator(
                            authorizationInfo.getOrganizationNumber(), primaryCustomerId, "P", productNumber);
            return cardAuthorizationInfo.getCardNumber();
        }
        return null;
    }

    /**
     * 更新管理账户
     *
     * @param managementInfo AccountManagementInfo
     */
    private void updateAccountManagementInfo(AccountManagementInfo managementInfo) {
        accountManagementInfoSelfMapper.updateByPrimaryKeySelective(managementInfo);
    }
}
