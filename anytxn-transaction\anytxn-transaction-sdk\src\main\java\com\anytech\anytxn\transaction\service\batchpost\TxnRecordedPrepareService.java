package com.anytech.anytxn.transaction.service.batchpost;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.account.domain.dto.AccMaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccStaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;
import com.anytech.anytxn.business.base.account.service.IAccountCommonService;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountAbsstatusDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAcctCustReleationDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.service.ICardAcctCustReleationService;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.LimitCustCreditInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.LimitCustUsedInfoDTO;
import com.anytech.anytxn.business.base.limit.domain.dto.PartnerMarginDTO;
import com.anytech.anytxn.business.base.limit.enums.PartnerModeEnum;
import com.anytech.anytxn.business.base.mapping.domain.dto.MappingDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.LimitControlUnitDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountAbsstatus;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateDownTopReferenceSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustUsedInfoMapper;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerInfoMapper;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerMarginMapper;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.business.dao.limit.model.LimitCustUsedInfo;
import com.anytech.anytxn.business.dao.limit.model.PartnerInfo;
import com.anytech.anytxn.business.dao.limit.model.PartnerMargin;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardProductCurrencyRelationResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.BlockCodePlasticResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.enums.ServerTypeEnum;
import com.anytech.anytxn.parameter.base.common.service.IBlockCodePlasticService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.product.IAcctProductMainInfoService;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoMapper;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.domain.bo.AbsBO;
import com.anytech.anytxn.transaction.base.domain.bo.AccountBO;
import com.anytech.anytxn.transaction.base.domain.bo.CardBO;
import com.anytech.anytxn.transaction.base.domain.bo.CustomerBO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.enums.AccountStatusEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.CardStatusEnum;
import com.anytech.anytxn.transaction.base.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.ForcePostIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.TransactionAttributeEnum;
import com.anytech.anytxn.transaction.base.enums.TransactionRepDetailEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.service.IRejectTransactionService;
import com.anytech.anytxn.transaction.service.AccountsPaymentAllocationServiceImpl;
import com.anytech.anytxn.transaction.service.SpecificBlockCodeChargeOffServiceImpl;
import com.anytech.anytxn.transaction.service.TransactionRoutingRuleHandle;
import com.anytech.anytxn.transaction.service.TxnRecordedServiceImpl;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.service.transcommon.CreditTransTypeService;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 入账逻辑：020-数据准备处理
 * @author: zhangnan
 * @create: 2020-06-30
 **/
@Slf4j
@Service
public class TxnRecordedPrepareService {


    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private AccountAbsstatusSelfMapper accountAbsstatusSelfMapper;
    @Autowired
    private IBlockCodePlasticService blockCodePlasticService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Autowired
    private IRejectTransactionService rejectTransactionService;
    @Autowired
    private ParmCardCurrencyInfoMapper parmCardCurrencyInfoMapper;
    @Autowired
    private AccountsPaymentAllocationServiceImpl accountsPaymentAllocationService;
    @Autowired
    private IAccountCommonService accountCommonService;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;
    @Autowired
    private LimitCustCreditInfoMapper limitCustCreditInfoMapper;
    @Autowired
    private LimitCustUsedInfoMapper limitCustUsedInfoMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private CorporateDownTopReferenceSelfMapper corporateDownTopReferenceSelfMapper;
    @Autowired
    private TransactionRoutingRuleHandle transactionRoutingRuleHandle;
    @Autowired
    private IAcctProductMainInfoService acctProductMainInfoService;
    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Resource
    private SpecificBlockCodeChargeOffServiceImpl specificBlockCodeChargeOffService;

    @Autowired
    private PartnerMarginMapper partnerMarginMapper;

    @Autowired
    private CorporateCustomerInfoSelfMapper corporateCustomerInfoSelfMapper;

    @Autowired
    private PartnerInfoMapper partnerInfoMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Autowired
    private ICardAcctCustReleationService cardAcctCustReleationService;

    @Autowired
    private TxnRecordedServiceImpl txnRecordedService;

    /**
     * 入账数据预加载
     *
     * @param recorded 入账参数
     * @return 返回组装结果，如果返回null，说明入账拒绝
     */
    public List<RecordedMidBO> recordedPreload(RecordedBO recorded) {
        // 除还款外通常只会产生一个RecordedBO
        List<RecordedMidBO> result = new ArrayList<>(2);
        RecordedMidBO rootRecorded = new RecordedMidBO();
        rootRecorded.setRecorded(recorded);
        // 管理账户号
        String txnAccountManageId = recorded.getTxnAccountManageId();
        String organizationNumber;
        String customerId;
        // >> 管理账户号加载区
        if (StringUtils.isNotEmpty(txnAccountManageId)) {
            // 加载管理账户
            buildAccountManagement(rootRecorded, txnAccountManageId);

            // 读取管理账户中的机构号和客户号
            AccountManagementInfoDTO accountManagementInfo = rootRecorded.getAccountBO().getAccountManagementInfo();
            // 机构号
            organizationNumber = accountManagementInfo.getOrganizationNumber();
            // 客户号
            customerId = LiabilityEnum.CORPORATE.getCode().equals(accountManagementInfo.getLiability()) ? accountManagementInfo.getCorporateCustomerId() : accountManagementInfo.getCustomerId();
            // >> 卡号加载区
            List<CardAcctCustReleationDTO> cardAcctCustReleationDTOList = cardAcctCustReleationService.selectByOrgNumberAndMid(organizationNumber, accountManagementInfo.getAccountManagementId());
            if (CollectionUtils.isEmpty(cardAcctCustReleationDTOList)) {
                if (log.isDebugEnabled()) {
                    log.debug("获取管理账户的索引表失败，入账拒绝！管理账户号：{}", txnAccountManageId);
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_MANAGEMENT_REJECT);
            }
            rootRecorded.setServerType(cardAcctCustReleationDTOList.get(0).getServerType());
        } else {
            String txnCardNumber = recorded.getTxnCardNumber();
            if (StringUtils.isEmpty(txnCardNumber)) {
                if (log.isDebugEnabled()) {
                    log.debug("入账参数无卡号信息，入账拒绝！");
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_REJECT);
            }
            // 加载卡授权
            buildCardAuth(rootRecorded, txnCardNumber);

            // 读取卡授权中的机构号和客户号
            CardAuthorizationDTO cardAuthorization = rootRecorded.getCardBO().getAuthInfo();
            // 机构号
            organizationNumber = cardAuthorization.getOrganizationNumber();
            // 客户号
            customerId = LiabilityEnum.CORPORATE.getCode().equals(cardAuthorization.getLiability()) ? cardAuthorization.getCorporateCustomerId() : cardAuthorization.getPrimaryCustomerId();
        }

        // 预加载对账表(此步骤优先其他数据加载，如果后续入账拒绝，不影响对账表更新操作)
        CustReconciliationControlDTO control = custReconciliationControlService.getControl(customerId, organizationNumber);
        CustAccountBO.threadCustAccountBO.get().setCustReconciliationControl(control);

        // 加载机构
        buildOrganization(rootRecorded, organizationNumber);
        // 加载交易码
        buildTransactionCode(rootRecorded, organizationNumber);
        // 加载交易类型
        //buildTransactionType(rootRecorded, organizationNumber);

        String type = AccountBO.checkTransactionType(rootRecorded.getTransactionCodeResDTO());
        rootRecorded.setRecordedTransType(type);
        // 卡产品编号
        if (rootRecorded.getCardBO() == null) {
            rootRecorded.setCardBO(new CardBO());
        }
        String txnCardNumber = recorded.getTxnCardNumber();
        String cardProductNum = "000000";
        if (StringUtils.isNotEmpty(txnCardNumber)) {
            if (rootRecorded.getCardBO().getAuthInfo() == null) {
                // 加载卡授权
                buildCardAuth(rootRecorded, txnCardNumber);
            }
            // 重新赋值卡产品编号
            CardAuthorizationDTO cardAuthorization = rootRecorded.getCardBO().getAuthInfo();
            cardProductNum = cardAuthorization.getProductNumber();
        }
        rootRecorded.getCardBO().setCardProductNumber(cardProductNum);
        // 加载客户授权表

        buildCustomerAuth(rootRecorded, organizationNumber, customerId);
        //加载客户基础信息
        buildCustomeBasic(rootRecorded, organizationNumber, customerId);

        // 加载卡产品
        buildCardProductInfo(rootRecorded, organizationNumber);

        // >> 卡号加载区
        if (StringUtils.isEmpty(txnAccountManageId)) {

            // 加载卡片封锁码
            buildCardBlock(rootRecorded, organizationNumber);
            // 判断还款
            if (rootRecorded.getTransactionCodeResDTO().getTransactionAttribute().equals("7") && rootRecorded.getTransactionCodeResDTO().getDebitCreditIndicator().equals("C")) {
                // 调取账户间还款分配
                List<RecordedBO> recordeds = accountsPaymentAllocationService.accountsPaymentAllocation(recorded, rootRecorded);

                recordeds.forEach(x -> {
                    String manageId = x.getTxnAccountManageId();
                    // 加载管理账户
                    RecordedMidBO recoreBo = BeanMapping.copy(rootRecorded, RecordedMidBO.class);
                    buildAccountManagement(recoreBo, manageId);

                    RecordedMidBO boCopy = new RecordedMidBO();
                    recoreBo.setRecorded(x);
                    BeanMapping.copy(recoreBo, boCopy);

                    // 还款分配结果入结果集，后续操作不能再处理rootRecorded
                    result.add(boCopy);
                });
            } else {
                // 匹配交易核算规则 + 加载账产品
                getAccoutProductInfo(rootRecorded);
                // 加载卡片币种对照表
                buildCurrency(rootRecorded, organizationNumber);
                // 读取管理账户号
                AccountManagementInfoDTO accountManagementInfo = buildAccountManagementByCondition(rootRecorded, customerId, rootRecorded.getProductInfoResDTO().getProductNumber(), organizationNumber, recorded.getTxnBillingCurrency(), false);
                // 如果管理账户读取失败或状态（0/1/2）进行开户
//                if (accountManagementInfo == null || !StringUtils.equalsAny(accountManagementInfo.getAccountStatus(), "0", "1", "2")) {
//                    // 开户
//                    newAccount(organizationNumber, customerId, rootRecorded);
//                    // 加载新建的管理账户，且已在之前开户
//                    buildAccountManagementByCondition(rootRecorded, customerId, rootRecorded.getProductInfoResDTO().getProductNumber(), organizationNumber, recorded.getTxnBillingCurrency(), true);
//                }

                if (accountManagementInfo == null) {
                    log.error("匹配交易核算规则失败，入账拒绝！");
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_REJECT);
                }
            }
        }
        // 非还款分配处理
        if (CollectionUtils.isEmpty(result)) {
            result.add(rootRecorded);
        }

        for (RecordedMidBO x : result) {
            // 加载卡片封锁码
            buildCardBlock(x, organizationNumber);
            // 加载账产品
            buildProductInfo(x, organizationNumber);
            // 账户封锁码
            buildAccountBlock(x, organizationNumber);
            // 不良abs
            buildBadAbs(x, organizationNumber);
            x.buildTxn();

            // 非批中处理
            //内部调用和批量调用均不需要执行
            if (!CustAccountBO.isBatch() && !recorded.isRecordedForSelf()) {
                notBatchLoad(x);
            }

            this.getControl(recorded, x);
        }

        // 标记再次入账则为内嵌入账
        recorded.setRecordedForSelf(true);

        return result;
    }

    /**
     * 非批中加载预加载数据
     *
     * @param recordedMidBO
     */
    private void notBatchLoad(RecordedMidBO recordedMidBO) {
        AccountManagementInfoDTO managementInfo = recordedMidBO.getAccountBO().getAccountManagementInfo();
        if (LiabilityEnum.CORPORATE.getCode().equals(managementInfo.getLiability())) {
            managementInfo.setCustomerId(managementInfo.getCorporateCustomerId());
        }
        CustAccountBO custAccountBO = CustAccountBO.threadCustAccountBO.get();

        //如果已经加载过，不再加载
        List<AccountBalanceInfoDTO> balanceInfoDTOs = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getBalanceByManagementId(managementInfo.getAccountManagementId());
        if (balanceInfoDTOs != null && balanceInfoDTOs.size() > 0) {
            return;
        }

        //其他常用常量
        custAccountBO.setCustomerId(managementInfo.getCustomerId());

        //交易账户
        List<AccountBalanceInfo> balanceInfos = accountBalanceInfoSelfMapper.selectAccountBalanceInfoByManagementId(managementInfo.getAccountManagementId(), PartitionKeyUtils.partitionKey(managementInfo.getCustomerId()));
        if (CollectionUtils.isNotEmpty(balanceInfos)) {
            custAccountBO.getCustomerBO().setBalanceInfo(BeanMapping.copyList(balanceInfos, AccountBalanceInfoDTO.class));
        }

        //统计账户
        List<AccountStatisticsInfo> accountStatisticsInfos = accountStatisticsInfoSelfMapper.selectByManageId(managementInfo.getAccountManagementId());
        if (CollectionUtils.isNotEmpty(accountStatisticsInfos)) {
            custAccountBO.getCustomerBO().setStatisticsInfo(BeanMapping.copyList(accountStatisticsInfos, AccountStatisticsInfoDTO.class));
        }

        boolean corporLiabilityFlag = LiabilityEnum.CORPORATE.getCode().equals(managementInfo.getLiability());
        String customerId = corporLiabilityFlag ? managementInfo.getCorporateCustomerId() : managementInfo.getCustomerId();
        // 授信额度信息
        List<LimitCustCreditInfo> limitCustCreditInfoAllList = new ArrayList<>(16);
        List<LimitCustCreditInfo> limitCustCreditInfos = null;
        //已用额度信息
        List<LimitCustUsedInfo> limitUsedInfoAll = new ArrayList<>(16);
        List<LimitCustUsedInfo> limitUsedInfos = null;
        if (CustAccountBO.isFromAuth()) {
            //入账请求是从授权过来的,用缓存的的额度数据
            Map<String, LimitCustCreditInfoDTO> limitCustCreditInfoMap = CustAccountBO.threadCustAccountBO.get().getLimitBO().getLimitCustCreditInfoMap();
            List<LimitCustCreditInfoDTO> custCreditInfoDtos = limitCustCreditInfoMap.values().stream()
                    .filter(x -> x.getCustomerId().equals(customerId) && OrgNumberUtils.getOrg().equals(managementInfo.getOrganizationNumber())).collect(Collectors.toList());
            limitCustCreditInfos = BeanMapping.copyList(custCreditInfoDtos, LimitCustCreditInfo.class);

            //用信信息
            Map<String, LimitCustUsedInfoDTO> limitCustUsedInfoMap = CustAccountBO.threadCustAccountBO.get().getLimitBO().getLimitCustUsedInfoMap();
            List<LimitCustUsedInfoDTO> limitCustUsedInfoDtos = limitCustUsedInfoMap.values().stream()
                    .filter(x -> x.getCustomerId().equals(customerId) && OrgNumberUtils.getOrg().equals(managementInfo.getOrganizationNumber())).collect(Collectors.toList());
            limitUsedInfos = BeanMapping.copyList(limitCustUsedInfoDtos, LimitCustUsedInfo.class);
        } else {
            limitCustCreditInfos = limitCustCreditInfoMapper.
                    selectByOrgNumberAndCustomerId(customerId, managementInfo.getOrganizationNumber());

            limitUsedInfos =
                    limitCustUsedInfoMapper.selectByOrgNumberAndCustomerId(customerId, managementInfo.getOrganizationNumber());
        }

        if (CollectionUtils.isNotEmpty(limitCustCreditInfos)) {
            limitCustCreditInfoAllList.addAll(limitCustCreditInfos);
        }
        if (CollectionUtils.isNotEmpty(limitUsedInfos)) {
            limitUsedInfoAll.addAll(limitUsedInfos);
        }
//中行批量临时注释
//        if(corporLiabilityFlag){
//            List<CorporateDownTopReference> list = corporateDownTopReferenceSelfMapper.selectByChildId(customerId);
//            if(CollectionUtils.isNotEmpty(limitCustCreditInfoAllList)){
//                for (CorporateDownTopReference corporateDownTopReference : list) {
//                    List<LimitCustCreditInfo> crelist = limitCustCreditInfoMapper.selectByOrgNumberAndCustomerId(corporateDownTopReference.getCorporateParentId(), managementInfo.getOrganizationNumber());
//                    if(CollectionUtils.isNotEmpty(crelist)){
//                        limitCustCreditInfoAllList.addAll(crelist);
//                    }
//                    List<LimitCustUsedInfo> usedInfoList = limitCustUsedInfoMapper.selectByOrgNumberAndCustomerId(corporateDownTopReference.getCorporateParentId(), managementInfo.getOrganizationNumber());
//                    if(CollectionUtils.isNotEmpty(usedInfoList)){
//                        limitUsedInfoAll.addAll(usedInfoList);
//                    }
//                }
//            }
//        }
        if (CollectionUtils.isNotEmpty(limitCustCreditInfoAllList)) {
            custAccountBO.getLimitBO().setLimitCustCreditInfo(BeanMapping.copyList(limitCustCreditInfoAllList, LimitCustCreditInfoDTO.class));
        }
        if (CollectionUtils.isNotEmpty(limitUsedInfoAll)) {
            custAccountBO.getLimitBO().setLimitCustUsedInfo(BeanMapping.copyList(limitUsedInfoAll, LimitCustUsedInfoDTO.class));
        }
//中行批量临时注释
        // 公司自下而上索引表
//        List<CorporateDownTopReference> corporateDownTopReferenceList = corporateDownTopReferenceSelfMapper.selectByChildId(customerId);
//        List<CorporateDownTopReferenceDTO> corporateDownTopReferenceDTOList = BeanMapping.copyList(corporateDownTopReferenceList, CorporateDownTopReferenceDTO.class);
//        if(CollectionUtils.isNotEmpty(corporateDownTopReferenceDTOList)){
//            custAccountBO.getCustomerBO().setCorporateDownTopReferenceDTOList(corporateDownTopReferenceDTOList);
//        }
    }


    /**
     * 入账拒绝
     *
     * @param recorded
     * @param rejectCode
     * @return
     */
    private void rejectRecorded(RecordedBO recorded, String rejectCode) {
        rejectTransactionService.rejectTransaction(recorded, rejectCode);
    }

    /**
     * 加载管理账户
     *
     * @param rootRecorded
     * @return
     */
    public void buildAccountManagement(RecordedMidBO rootRecorded, String txnAccountManageId) {
        AccountManagementInfoDTO accountManagementInfo = null;
        if (StringUtils.isNotEmpty(txnAccountManageId)) {
            // 非批中处理
            if (!CustAccountBO.isBatch()) {
                if (CustAccountBO.isFromAuth()) {
                    //授权的入账请求从缓存中获取
                    accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByPrimaryKey(txnAccountManageId);
                } else {

//                    AccountManagementInfo model = accountManagementInfoSelfMapper.selectVaildByMid(txnAccountManageId);
//                    accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);
//
//                    if(null==accountManagementInfo){
//                        // 还款分配后的管理账户可能存在与缓存中
//                        if (CustAccountBO.threadCustAccountBO.get() != null) {
//                            accountManagementInfo = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(txnAccountManageId);
//                        }
//                    }
                    //中行压测改造 优先使用缓存 临时更改
                    accountManagementInfo = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(txnAccountManageId);
                    if (null == accountManagementInfo) {
                        AccountManagementInfo model = accountManagementInfoMapper.selectByPrimaryKey(txnAccountManageId);

                        accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);
                    }


                }


                // 批中处理
            } else {
                accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByPrimaryKey(txnAccountManageId);
            }
        }

        if (accountManagementInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取管理账户失败，入账拒绝！管理账户号：{}", txnAccountManageId);
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_MANAGEMENT_REJECT);
        }

        if (rootRecorded.getAccountBO() == null) {
            rootRecorded.setAccountBO(new AccountBO());
        }
        rootRecorded.getAccountBO().setAccountManagementInfo(accountManagementInfo);
        rootRecorded.getRecorded().setCorporateCustomerId(accountManagementInfo.getCorporateCustomerId());

        // 后续测试是否有影响，批中只处理这一个管理账户
        CustAccountBO.threadCustAccountBO.get().getCustomerBO().setManagementInfo(Lists.newArrayList(accountManagementInfo));
    }

    /**
     * 条件加载管理账户
     *
     * @param rootRecorded
     * @return
     */
    private AccountManagementInfoDTO buildAccountManagementByCondition(RecordedMidBO rootRecorded, String customerId, String productNumber, String orgNumber, String currency, boolean newAccount) {
        AccountManagementInfoDTO accountManagementInfo = null;
        CardAuthorizationDTO cardAuthorizationDTO = rootRecorded.getCardBO().getAuthInfo();
        String liability = cardAuthorizationDTO.getLiability();
        //公司清偿
        if (LiabilityEnum.CORPORATE.getCode().equals(liability)) {
            AccountManagementInfo model = accountManagementInfoSelfMapper.selectByCorpCusIdAndProNumAndOrgNoAndCurrency(cardAuthorizationDTO.getCorporateCustomerId(), productNumber, orgNumber, currency);
            accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);
            //非公司清偿
        } else {
            // 非批中且不是新建管理账户，使用mapper查询
            if (!CustAccountBO.isBatch() && !newAccount) {
                if (CustAccountBO.isFromAuth()) {
                    //授权过来的入账，从缓存中取
                    accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByCondition(customerId, productNumber, currency);
                    if (null == accountManagementInfo) {
                        AccountManagementInfo model = accountManagementInfoSelfMapper.selectByCusIdAndProNumAndOrgNoAndCurrency(customerId, productNumber, orgNumber, currency);
                        accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);
                    }
                } else {
                    AccountManagementInfo model = accountManagementInfoSelfMapper.selectByCusIdAndProNumAndOrgNoAndCurrency(customerId, productNumber, orgNumber, currency);
                    accountManagementInfo = model == null ? null : BeanMapping.copy(model, AccountManagementInfoDTO.class);

                    // 入账内嵌入账时尝试从库中获取
                    //ObjectUtils uat环境启动失败
                    if (null == accountManagementInfo) {
                        // 还款分配后的管理账户可能存在与缓存中
                        if (CustAccountBO.threadCustAccountBO.get() != null) {
                            accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByCondition(customerId, productNumber, currency);
                        }
                    }
                }


                // 批中或已开户直接读缓存
            } else {
                accountManagementInfo = txnRecordedAccountCacheService.accountManagementInfoSelectByCondition(customerId, productNumber, currency);
            }
        }
        if (accountManagementInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取管理账户失败，入账拒绝！客户号：{}，产品编号：{}, 币种：{}", customerId, productNumber, currency);
            }
            return null;
        }

        if (rootRecorded.getAccountBO() == null) {
            rootRecorded.setAccountBO(new AccountBO());
        }
        rootRecorded.getAccountBO().setAccountManagementInfo(accountManagementInfo);

        // 后续测试是否有影响，批中只处理这一个管理账户
        CustAccountBO.threadCustAccountBO.get().getCustomerBO().setManagementInfo(Lists.newArrayList(accountManagementInfo));

        return accountManagementInfo;
    }

    /**
     * 加载卡产品
     *
     * @param cardNum 卡号
     * @return CardBO对象
     */
    public void buildCardAuth(RecordedMidBO rootRecorded, String cardNum) {
        CardAuthorizationDTO cardAuthorizationInfo = null;
        if (StringUtils.isNotEmpty(cardNum)) {
            // 非批中，使用mapper查询
            if (!CustAccountBO.isBatch()) {
                if (CustAccountBO.isFromAuth()) {
                    //授权过来的入账请求，数据从缓存中获取
                    cardAuthorizationInfo = CustAccountBO.threadCustAccountBO.get().getAuthBO().getCardAuthorizationByCarNum(cardNum);

                } else {
                    CardAuthorizationInfo model = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNum, OrgNumberUtils.getOrg());
                    cardAuthorizationInfo = model == null ? null : BeanMapping.copy(model, CardAuthorizationDTO.class);
                }
                // 批中直接读缓存
            } else {
                cardAuthorizationInfo = txnRecordedAccountCacheService.cardAuthorizationSelectByPrimaryKey(cardNum);
            }
        }

        if (cardAuthorizationInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取卡授权失败，入账拒绝！卡号：{}", cardNum);
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_REJECT);
        }
        String status = cardAuthorizationInfo.getStatus();
        String primaryCustomerId = cardAuthorizationInfo.getPrimaryCustomerId();
        String productNumber = cardAuthorizationInfo.getProductNumber();
        RecordedBO recorded = rootRecorded.getRecorded();
        String txnTransactionSource = recorded.getTxnTransactionSource() == null ? "" : recorded.getTxnTransactionSource();
        if (("4".equals(status) || "8".equals(status))
                && (TransactionSourceEnum.INNER_FEE.getCode().equals(txnTransactionSource)
                || TransactionSourceEnum.TRIGGER_BY_SYS.getCode().equals(txnTransactionSource))) {
            AccountManagementInfoDTO acctInfoDto = null;
            String liability = null;
            if (Objects.nonNull(rootRecorded.getAccountBO())) {
                acctInfoDto = rootRecorded.getAccountBO().getAccountManagementInfo();
                liability = acctInfoDto.getLiability();
            }
            if (Objects.nonNull(acctInfoDto) &&
                    !org.springframework.util.StringUtils.isEmpty(liability) && "C".equals(liability)) {
                //检查是否是公司卡的费息交易，并做换记账卡处理
                cardAuthorizationInfo = corpInnerTransCardSelection(cardAuthorizationInfo, acctInfoDto);
            } else {
                List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.
                        selectByCusIdAndCardProNum(primaryCustomerId, productNumber);
                if (!CollectionUtils.isEmpty(cardAuthorizationInfos)) {
                    if (cardAuthorizationInfos.size() == 1) {
                        cardAuthorizationInfo = cardAuthorizationInfos.get(0) == null ? null :
                                BeanMapping.copy(cardAuthorizationInfos.get(0), CardAuthorizationDTO.class);
                    } else {
                        cardAuthorizationInfos.sort(Comparator.comparing(CardAuthorizationInfo
                                ::getCreateTime));
                        List<CardAuthorizationInfo> collect = cardAuthorizationInfos.stream().filter(
                                c -> StringUtils.equalsAny(c.getStatus(),
                                        CardStatusEnum.ACTIVE_STATUS.getCode(),
                                        CardStatusEnum.NEW_STATUS.getCode(),
                                        CardStatusEnum.STATIC_STATUS.getCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(collect)) {
                            cardAuthorizationInfo = cardAuthorizationInfos.get(0) == null ? null :
                                    BeanMapping.copy(cardAuthorizationInfos.get(0),
                                            CardAuthorizationDTO.class);
                        } else {
                            cardAuthorizationInfo = collect.get(0) == null ? null :
                                    BeanMapping.copy(collect.get(0), CardAuthorizationDTO.class);
                        }
                    }
                }
            }
        }
        rootRecorded.getRecorded().setTxnCardNumber(cardAuthorizationInfo != null ?
                cardAuthorizationInfo.getCardNumber() : cardNum);
        rootRecorded.setCardBO(new CardBO(cardAuthorizationInfo));

        CustAccountBO.threadCustAccountBO.get().getAuthBO().initCardAuthorizationMap(cardAuthorizationInfo);
    }

    private CardAuthorizationDTO corpInnerTransCardSelection(CardAuthorizationDTO cardAuthorizationDTO, AccountManagementInfoDTO acctInfoDto) {
        String corpCustId = cardAuthorizationDTO.getCorporateCustomerId();
        String prdNum = cardAuthorizationDTO.getProductNumber();
        String acctStatus = acctInfoDto.getAccountStatus();
        String cardStatus = cardAuthorizationDTO.getStatus();
        if (!CardStatusEnum.ACTIVE_STATUS.getCode().equals(cardStatus)) {
            //找出该主客户下该产品的所有主卡
            List<CardAuthorizationInfo> cardAuthorizationList = cardAuthorizationInfoSelfMapper
                    .selectByPrdNumAndCustId(corpCustId, prdNum);
            if (CollectionUtils.isNotEmpty(cardAuthorizationList)) {
                //只有一张主卡
                if (cardAuthorizationList.size() == 1) {
                    if (CardStatusEnum.SHUT_DOWN.getCode().equals(cardStatus)
                            && AccountStatusEnum.ACTIVE.getCode().equals(acctStatus)) {
                        if (cardAuthorizationList.get(0).getCardNumber().
                                equals(cardAuthorizationDTO.getCardNumber())) {
                            return cardAuthorizationDTO;
                        }
                    }
                    //若同产品有多张主卡
                } else if (cardAuthorizationList.size() > 1) {
                    //筛选1的活跃主卡
                    List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationList
                            .stream().filter(x -> CardStatusEnum.ACTIVE_STATUS.getCode()
                                    .equals(x.getStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cardAuthorizationInfos)) {
                        //公司卡同产品多活跃主卡，正序取第一张
                        cardAuthorizationInfos.sort(Comparator.comparing(
                                CardAuthorizationInfo::getCreateTime));
                        return BeanMapping.copy(cardAuthorizationInfos.get(0),
                                CardAuthorizationDTO.class);
                    }
                }
            }
        }
        return cardAuthorizationDTO;
    }

    /**
     * 加载机构
     *
     * @param organizationNumber
     * @return
     */
    private void buildOrganization(RecordedMidBO rootRecorded, String organizationNumber) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(organizationNumber);

        if (organizationInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取机构失败，入账拒绝！机构号：{}", organizationNumber);
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORG_REJECT);
        }
        rootRecorded.setOrganizationInfoResDTO(organizationInfo);
    }

    /**
     * 加载交易码
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildTransactionCode(RecordedMidBO rootRecorded, String organizationNumber) {
        TransactionCodeResDTO transactionCode = null;
        try {
            transactionCode = transactionCodeService.findTransactionCode(organizationNumber, rootRecorded.recorded.getTxnTransactionCode());
        } catch (Exception e) {

        }

        if (transactionCode == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取交易码失败，入账拒绝！交易码：{}", rootRecorded.recorded.getTxnTransactionCode());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TRANSACTION_CODE_REJECT);
        }
        rootRecorded.setTransactionCodeResDTO(transactionCode);
    }

    /**
     * 加载客户授权
     *
     * @param rootRecorded
     * @param organizationNumber
     * @param customerId
     * @return
     */
    private void buildCustomerAuth(RecordedMidBO rootRecorded, String organizationNumber, String customerId) {
        CustomerAuthorizationInfoDTO customerAuthorizationInfo = null;

        if (StringUtils.isNotEmpty(customerId)) {
            // 非批中，使用mapper查询
            if (!CustAccountBO.isBatch()) {
                if (CustAccountBO.isFromAuth()) {
                    //授权过来的入账请求，数据从缓存中获取
                    customerAuthorizationInfo = CustAccountBO.threadCustAccountBO.get().getCustomerAuthorizationInfo();
                } else {
                    CustomerAuthorizationInfo model = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), customerId);
                    customerAuthorizationInfo = model == null ? null : BeanMapping.copy(model, CustomerAuthorizationInfoDTO.class);
                }

                // 批中直接读缓存
            } else {
                customerAuthorizationInfo = CustAccountBO.threadCustAccountBO.get().getCustomerAuthorizationInfo();
            }
        }

        if (customerAuthorizationInfo == null || !customerAuthorizationInfo.getCustomerId().equals(customerId)) {
            if (log.isDebugEnabled()) {
                log.debug("获取客户授权失败，入账拒绝！交易码：{}", rootRecorded.recorded.getTxnTransactionCode());
            }
            if (TransactionConstants.TA_VA_PAYMENT_CODE.equals(rootRecorded.recorded.getTxnTransactionCode())) {
                //花花世界改造，如果是Partner的VA充值，由于没有卡号，需要通过查询公司客户信息获取机构号
                CorporateCustomerInfo corporateCustomerInfo = corporateCustomerInfoSelfMapper.selectByOrgNumAndCusId(organizationNumber, customerId);
                if (corporateCustomerInfo != null && StringUtils.isNotEmpty(corporateCustomerInfo.getPartnerId())) {
                    PartnerInfo partnerInfo = partnerInfoMapper.selectByPartnerId(corporateCustomerInfo.getPartnerId());
                    if (Objects.nonNull(partnerInfo) && PartnerModeEnum.PA.getCode().equals(partnerInfo.getPartnerMode())) {
                        //只有PA模式才会占用保证金
                        PartnerMargin partnerMargin = partnerMarginMapper.selectByPartnerIdAndCurrency(corporateCustomerInfo.getPartnerId(), rootRecorded.getRecorded().getTxnBillingCurrency());
                        rootRecorded.setPartnerMarginDTO(BeanMapping.copy(partnerMargin, PartnerMarginDTO.class));
                        rootRecorded.getPartnerMarginDTO().setOverPaymentTransInd(partnerInfo.getOverPaymentTransInd());
                        if (StringUtils.isBlank(rootRecorded.recorded.getOverPaymentTransInd())) {
                            rootRecorded.recorded.setOverPaymentTransInd(partnerInfo.getOverPaymentTransInd());
                        }
                        rootRecorded.setPartnerMarginLogList(new HashSet<>(4));
                    }
                }
            }
            return;
            //  throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_CUSTOMER_AUTH_REJECT);
        }
        rootRecorded.setCustomerBO(new CustomerBO(BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfoDTO.class)));

        CustAccountBO.threadCustAccountBO.get().setCustomerAuthorizationInfo(customerAuthorizationInfo);

        if (StringUtils.isNotEmpty(customerAuthorizationInfo.getPartnerId())) {
            PartnerInfo partnerInfo = partnerInfoMapper.selectByPartnerId(customerAuthorizationInfo.getPartnerId());
            if (Objects.nonNull(partnerInfo) && PartnerModeEnum.PA.getCode().equals(partnerInfo.getPartnerMode())) {
                //只有PA模式才会占用保证金
                PartnerMargin partnerMargin = partnerMarginMapper.selectByPartnerIdAndCurrency(customerAuthorizationInfo.getPartnerId(), rootRecorded.getRecorded().getTxnBillingCurrency());
                rootRecorded.setPartnerMarginDTO(BeanMapping.copy(partnerMargin, PartnerMarginDTO.class));
                rootRecorded.getPartnerMarginDTO().setOverPaymentTransInd(partnerInfo.getOverPaymentTransInd());
                if (StringUtils.isBlank(rootRecorded.recorded.getOverPaymentTransInd())) {
                    rootRecorded.recorded.setOverPaymentTransInd(partnerInfo.getOverPaymentTransInd());
                }
                rootRecorded.setPartnerMarginLogList(new HashSet<>(4));
            }
        }
    }


    /**
     * 加载客户基础信息
     *
     * @param rootRecorded
     * @param organizationNumber
     * @param customerId
     * @return
     */
    private void buildCustomeBasic(RecordedMidBO rootRecorded, String organizationNumber, String customerId) {
        CustomerBasicInfoDTO customerBasicInfoDTO = null;

        if (StringUtils.isNotEmpty(customerId)) {
            // 非批中，使用mapper查询
            if (!CustAccountBO.isBatch()) {
                if ((customerBasicInfoDTO = CustAccountBO.threadCustAccountBO.get().getCustomerBasicInfoDTO()) == null) {
                    CustomerBasicInfo model = customerBasicInfoSelfMapper.selectByOrgAndCustId(organizationNumber, customerId);
                    customerBasicInfoDTO = model == null ? null : BeanMapping.copy(model, CustomerBasicInfoDTO.class);
                }
                // 批中直接读缓存
            } else {
                customerBasicInfoDTO = CustAccountBO.threadCustAccountBO.get().getCustomerBasicInfoDTO();
            }
        }

        if (customerBasicInfoDTO == null || !customerBasicInfoDTO.getCustomerId().equals(customerId)) {
            if (log.isDebugEnabled()) {
                log.debug("获取客户基础信息失败，入账拒绝！交易码：{}", rootRecorded.recorded.getTxnTransactionCode());
            }
            return;
            // throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_CUSTOMER_BASIC_REJECT);
        }
        rootRecorded.getCustomerBO().setCustomerBasicInfo(customerBasicInfoDTO);
        CustAccountBO.threadCustAccountBO.get().setCustomerBasicInfoDTO(customerBasicInfoDTO);
    }


    /**
     * 加载账产品
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildProductInfo(RecordedMidBO rootRecorded, String organizationNumber) {
        // 已加载成功不需要再加载
        if (rootRecorded.getAcctProductInfoDTO() != null) {
            return;
        }
        AccountManagementInfoDTO accountManagementInfo = rootRecorded.getAccountBO().getAccountManagementInfo();
        List<ProductInfoResDTO> productInfo;
        AcctProductInfoDTO byOrgAndProductNum = acctProductMainInfoService.findByOrgAndProductNum(rootRecorded.getOrganizationNumber(), accountManagementInfo.getProductNumber());
        if (byOrgAndProductNum == null) {
            log.warn("根据机构号:{},账产品号:{}查询账产品参数 数据不存在", rootRecorded.getOrganizationNumber(), rootRecorded.getCardProductInfoResDTO().getProductNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.ENTRY_RECORDED);
        }
        if (byOrgAndProductNum.getProductInfoResDTOS() == null || byOrgAndProductNum.getProductInfoResDTOS().size() == 0) {
            try {
                productInfo = productInfoService.findProductInfo(organizationNumber, accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
            } catch (Exception e) {
                log.info("读取账产品失败，写入入账拒绝", e);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_PRODUCT_REJECT);
            }

            if (CollectionUtils.isEmpty(productInfo)) {
                if (log.isDebugEnabled()) {
                    log.debug("获取账产品失败，入账拒绝！管理账户号：{}", accountManagementInfo.getAccountManagementId());
                }
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_PRODUCT_REJECT);
            }
        } else {
            productInfo = byOrgAndProductNum.getProductInfoResDTOS().stream().filter(p ->
                    p.getCurrencyCode().equals(accountManagementInfo.getCurrency()))
                    .collect(Collectors.toList());
        }
        rootRecorded.setAcctProductInfoDTO(byOrgAndProductNum);
        rootRecorded.setProductInfoResDTO(productInfo.get(0));
    }

    /**
     * 加载账户封锁码
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildAccountBlock(RecordedMidBO rootRecorded, String organizationNumber) {
        // 缓存没有存储
        BlockCodeAccountResDTO blockCodeAccount;

        try {
            blockCodeAccount = blockCodeAccountService.findBlockCodeAccount(organizationNumber, rootRecorded.getProductInfoResDTO().getAccountBlockCodeTableId(), rootRecorded.getAccountBO().getAccountManagementInfo().getBlockCode());
        } catch (Exception e) {
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_BLOCK_REJECT);
        }
        if (blockCodeAccount == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取账户封锁码失败，入账拒绝！管理账户号：{}", rootRecorded.getAccountBO().getAccountManagementInfo().getAccountManagementId());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACCOUNT_BLOCK_REJECT);
        }
        rootRecorded.getAccountBO().indicatorConfig = blockCodeAccount;
    }

    /**
     * 加载不良abs
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildBadAbs(RecordedMidBO rootRecorded, String organizationNumber) {
        List<AccountAbsstatus> badAbs = accountAbsstatusSelfMapper.selectByCondition(rootRecorded.getAccountBO().getAccountManagementInfo().getAccountManagementId(),
                organizationNumber,
                rootRecorded.getAccountBO().getAccountManagementInfo().getAbsProductCodeCurr(),
                "3");

        rootRecorded.setAbsBO(new AbsBO());
        // 空值不处理
        if (CollectionUtils.isNotEmpty(badAbs)) {
            List<AccountAbsstatusDTO> list = BeanMapping.copyList(badAbs, AccountAbsstatusDTO.class);
            // 对比缓存
            CustAccountBO.threadCustAccountBO.get().checkTamsAbsStatus(list);
            rootRecorded.getAbsBO().setAbsStatusList(list);
        }
    }

    /**
     * 加载卡产品
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildCardProductInfo(RecordedMidBO rootRecorded, String organizationNumber) {
        CardAuthorizationDTO cardAuthorization = rootRecorded.getCardBO().getAuthInfo();
        //无卡号的情况这里没有值
        if (cardAuthorization == null) {
            return;
        }
        CardProductInfoResDTO cardProductInfo = cardProductInfoService.findByOrgAndProductNum(organizationNumber, cardAuthorization.getProductNumber());
        if (cardProductInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取卡产品失败，入账拒绝！卡号：{}", cardAuthorization.getCardNumber());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_PRODUCT_REJECT);
        }
        rootRecorded.setCardProductInfoResDTO(cardProductInfo);
        rootRecorded.setServerType(cardProductInfo.getServerType());
    }

    /**
     * 加载卡片封锁码
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildCardBlock(RecordedMidBO rootRecorded, String organizationNumber) {
        CardProductInfoResDTO cardProductInfo = rootRecorded.getCardProductInfoResDTO();
        CardAuthorizationDTO cardAuthorization = rootRecorded.getCardBO().getAuthInfo();
        if (null == cardProductInfo || null == cardAuthorization) {
            log.error("卡产品信息或卡片授权信息为空,cardProductInfo:{},cardAuthorization:{}", cardProductInfo, cardAuthorization);
            return;
        }
        BlockCodePlasticResDTO blockCodePlastic = blockCodePlasticService.findBlockCodePlastic(organizationNumber, cardProductInfo.getPlasticBlockCodeTableId(), cardAuthorization.getBlockCode());

        if (blockCodePlastic == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取卡片封锁码失败，入账拒绝！卡号：{}", cardAuthorization.getCardNumber());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARD_BLOCK_REJECT);
        }
        rootRecorded.getCardBO().setIndicatorConfig(blockCodePlastic);
    }

    /**
     * 调用交易分配和入账核算规则获取账户产品
     *
     * @param rootRecorded RecordedBO
     */
    private void getAccoutProductInfo(RecordedMidBO rootRecorded) {
        Set<String> acctProductSet = new HashSet<>();
        String accountProductNum = "";
        if (Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), rootRecorded.transactionCodeResDTO.getDebitCreditIndicator())) {
            List<String> transactionTypes;
            if (CollectionUtils.isNotEmpty(rootRecorded.recorded.getLimitControlUnits())) {
                List<LimitControlUnitDTO> limitControlUnits = rootRecorded.recorded.getLimitControlUnits();
                transactionTypes = limitControlUnits.stream().map(LimitControlUnitDTO::getTransactionType).collect(Collectors.toList());
            } else {
                TransactionCtrlUnitDTO transactionCtrlUnitDTO = transactionRoutingRuleHandle.transactionRoutingRule(rootRecorded);
                List<TransactionCtrlUnitDebitDTO> transactionCtrlUnitDebits = transactionCtrlUnitDTO.getTransactionCtrlUnitDebits();
                transactionTypes = transactionCtrlUnitDebits.stream().map(TransactionCtrlUnitDebitDTO::getTransactionTypeCode).collect(Collectors.toList());
            }
            for (String transactionType : transactionTypes) {
                String acctProduct = matchRecordedRule(rootRecorded, transactionType);
                if (StringUtils.isNotEmpty(acctProduct)) {
                    acctProductSet.add(acctProduct);
                }
            }
            if (acctProductSet.isEmpty()) {
                log.error("匹配交易核算规则失败，入账拒绝！");
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_REJECT);
            }
            if (acctProductSet.size() > 1) {
                log.error("不同交易类型匹配出多个账户产品,{}", JSON.toJSONString(acctProductSet));
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_MATCH_MORE_ACCOUNT_REJECT);
            }
            accountProductNum = acctProductSet.stream().findFirst().get();

        } else if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), rootRecorded.transactionCodeResDTO.getDebitCreditIndicator())) {
            if (CollectionUtils.isNotEmpty(rootRecorded.recorded.getLimitControlUnits())) {
                List<LimitControlUnitDTO> limitControlUnits = rootRecorded.recorded.getLimitControlUnits();
                List<String> transactionTypes = limitControlUnits.stream().map(LimitControlUnitDTO::getTransactionType).collect(Collectors.toList());
                for (String transactionType : transactionTypes) {
                    String acctProduct = matchRecordedRule(rootRecorded, transactionType);
                    if (StringUtils.isNotEmpty(acctProduct)) {
                        acctProductSet.add(acctProduct);
                    }
                }
                if (acctProductSet.isEmpty()) {
                    log.error("匹配交易核算规则失败，入账拒绝！");
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_REJECT);
                }
                if (acctProductSet.size() > 1) {
                    log.error("不同交易类型匹配出多个账户产品,{}", JSON.toJSONString(acctProductSet));
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_MATCH_MORE_ACCOUNT_REJECT);
                }
                accountProductNum = acctProductSet.stream().findFirst().get();
            } else {
                TransactionCtrlUnitDTO transactionCtrlUnitDTO = transactionRoutingRuleHandle.transactionRoutingRule(rootRecorded);
                String creditTransType = CreditTransTypeService.getCreditTransType(rootRecorded, transactionCtrlUnitDTO.getTransactionCtrlUnitCredit());
                accountProductNum = matchRecordedRule(rootRecorded, creditTransType);
                if (org.springframework.util.StringUtils.isEmpty(accountProductNum)) {
                    log.error("匹配贷记交易核算规则，未找到账产品，入账拒绝！");
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_REJECT);
                }
            }

        } else {
            accountProductNum = matchRecordedRule(rootRecorded, null);
        }

        List<ProductInfoResDTO> productInfoResDTO;
        try {
            productInfoResDTO = productInfoService.findProductInfo(rootRecorded.getOrganizationInfoResDTO().getOrganizationNumber(), accountProductNum, rootRecorded.recorded.getTxnBillingCurrency());
        } catch (Exception e) {
            log.error("查询账户产品异常", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_PRODUCT_REJECT, e);
        }
        if (CollectionUtils.isNotEmpty(productInfoResDTO)) {
            rootRecorded.setProductInfoResDTO(productInfoResDTO.get(0));
            return;
        } else {
            if (log.isDebugEnabled()) {
                log.debug("加载账产品失败！账产品编号: {}, 币种: {}", accountProductNum, rootRecorded.recorded.getTxnBillingCurrency());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_RULE_ACCOUNT_PRODUCT_REJECT);
        }
    }

    /**
     * 匹配核算交易规则，并加载账产品
     *
     * @return
     */
    private String matchRecordedRule(RecordedMidBO rootRecorded, String transactionType) {
        AcctProductInfoDTO byOrgAndProductNum = acctProductMainInfoService.findByOrgAndProductNum(rootRecorded.getOrganizationNumber(), rootRecorded.getCardProductInfoResDTO().getAccountProductNumber());
        if (byOrgAndProductNum == null) {
            log.warn("根据机构号:{},账产品号:{}查询账产品参数 数据不存在", rootRecorded.getOrganizationNumber(), rootRecorded.getCardProductInfoResDTO().getProductNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, TransactionRepDetailEnum.ENTRY_RECORDED);
        }
        rootRecorded.setAcctProductInfoDTO(byOrgAndProductNum);
        String attribute = byOrgAndProductNum.getAcctProductMainInfoResDTO().getAttribute();
        Map<String, Object> params = new HashMap<>();
        params.put("txnTransactionCode", rootRecorded.getTransactionCodeResDTO().getTransactionCode());
        params.put("txnTransactionTypeCode", transactionType);
        params.put("txnTransactionSource", rootRecorded.recorded.getTxnTransactionSource());
        params.put("txnBillingCurrency", rootRecorded.recorded.getTxnBillingCurrency());
        params.put("txnProductNumber", rootRecorded.getCardBO().getAuthInfo().getProductNumber());
        params.put("merchantNum", rootRecorded.recorded.getTxnMerchantId());
        params.put("productAttribute", attribute);

        //基于规则类型找到规则匹配器
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher("AccountingRecordRules", OrgNumberUtils.getOrg());
        if (ruleMatcher != null) {
            DataInputDTO dataInput = new DataInputDTO(params, "AccountingRecordRules");
            Map<String, Object> ruleMap = ruleMatcher.execute(dataInput);
            if (ruleMap != null && !ruleMap.isEmpty()) {
                Map.Entry<String, Object> entry = ruleMap.entrySet().iterator().next();
                // 正常结果存储是id
                return String.valueOf(entry.getValue());
            }
        }
        return null;
    }


    /**
     * 加载币种对照表
     *
     * @param rootRecorded
     * @param organizationNumber
     * @return
     */
    private void buildCurrency(RecordedMidBO rootRecorded, String organizationNumber) {
        ParmCardCurrencyInfo parmCardCurrencyInfo = parmCardCurrencyInfoMapper.selectOneByOrgProduct(organizationNumber, rootRecorded.getCardBO().getAuthInfo().getProductNumber(), rootRecorded.recorded.getTxnBillingCurrency());
        if (parmCardCurrencyInfo == null) {
            if (log.isDebugEnabled()) {
                log.debug("获取卡币种对照表失败，入账拒绝！卡产品编号: {}， 币种: {}", rootRecorded.getCardBO().getAuthInfo().getProductNumber(), rootRecorded.recorded.getTxnBillingCurrency());
            }
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CURRENCY_REJECT);
        }
        rootRecorded.setCardProductCurrencyRelationResDTO(BeanMapping.copy(parmCardCurrencyInfo, CardProductCurrencyRelationResDTO.class));
    }

    /**
     * 开户
     *
     * @param organizationNumber
     * @param customerId
     * @param rootRecordedMidBO
     * @return
     */
    private boolean newAccount(String organizationNumber, String customerId, RecordedMidBO rootRecordedMidBO) {
        AccStaDTO accStaDTO = new AccStaDTO();
        AccMaDTO accMaDTO = new AccMaDTO();
        accMaDTO.setCustomerId(customerId);
        accMaDTO.setOrganizationNumber(organizationNumber);
        accMaDTO.setProductNumber(rootRecordedMidBO.getProductInfoResDTO().getProductNumber());
        accMaDTO.setCurrency(rootRecordedMidBO.recorded.getTxnBillingCurrency());
        accMaDTO.setAccountStatus("2");
        accMaDTO.setAccountStatusSetDate(rootRecordedMidBO.getOrganizationInfoResDTO().getNextProcessingDay());
        String liability = rootRecordedMidBO.getCardBO().getAuthInfo().getLiability();
        accMaDTO.setIsCorpoAccount(LiabilityEnum.CORPORATE.getCode().equals(liability) ? true : false);
        String result = accountCommonService.createAccountAndStatisticInfo(accMaDTO, accStaDTO, true);
        if (StringUtils.isNotEmpty(result) && "1".equals(result)) {
            return true;
        }
        List<MappingDTO> mappingList = new ArrayList<>();
//        TODO 暂时注释
//        MappingDTO accountManagementMappingDTO = new MappingDTO(organizationNumber, accMaDTO.getAccountManagementId(), IdentityType.MID, customerId);
//        mappingList.add(accountManagementMappingDTO);
//        AnyTxnHttpResponse<Boolean> httpResponse = mappingFeign.saveMapping(organizationNumber, mappingList);
//        if (!httpResponse.getData()) {
//            log.error("插入mapping库失败,{}", JSON.toJSONString(mappingList));
//            //TODOzhangnan20210127 1、后续必要的话写入错误日志表，加定时补偿机制。2、交易由于被多数工程使用，暂时不引入mq
//        }
        return true;
    }

    /**
     * 入账检查
     *
     * @param recordedMidBO
     * @param recorded
     */
    public void txnRecordCheck(RecordedMidBO recordedMidBO, RecordedBO recorded) {
                   /* 3) 入账逻辑检查
                当入账的卡号为0时不进行
            */
        //强制入账标识检查
        if (!ForcePostIndicatorEnum.FORCE_POST.getCode().equals(recorded.getTxnForcePostIndicator())) {
            //入账拒绝记录管理账户id
            recorded.setTxnAccountManageId(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());

            /* 验证卡片层入账检查逻辑 */
            if (isCardNumNotNull(recorded)) {
                recordedMidBO.checkCardStatus();
                recordedMidBO.checkCardBlockCode();
            }
            /* 验证账户层入账检查逻辑 */
            recordedMidBO.checkAccountStatus();
            recordedMidBO.checkPostingIndicator();

            /* 验证交易层入账检查逻辑 */
            recordedMidBO.checkTransactionDate();
            recordedMidBO.checkTransactionAmount();
            recordedMidBO.checkBillingAmount();

            /* 验证远期交易 但机构日期后到单个客户日切前这段时间不验证*/

            CustReconciliationControlDTO custReconciliationControlDTO = CustAccountBO.threadCustAccountBO.get().getCustReconciliationControl();
            LocalDate reconciliationDate = custReconciliationControlDTO.getReconciliationDate();
            if (!reconciliationDate.isBefore(recordedMidBO.getOrganizationInfoResDTO().getAccruedThruDay())) {
                recordedMidBO.checkForwardTransaction();
            }

            //入账限制天数
            recordedMidBO.checkLimitDays();


            /* 验证客户层入账检查逻辑 */
            if (!LiabilityEnum.CORPORATE.getCode().equals(recordedMidBO.accountBO.getAccountManagementInfo().getLiability()) && ServerTypeEnum.CARD_ACCT_CUST_SERVER.getCode().equals(recordedMidBO.getServerType())) {
                recordedMidBO.checkCustomerFinanceStatus();
            }

            checkTransactionAbs(recordedMidBO, recorded);

            //超限检查
            txnRecordedService.setExcessAmountFlag(recorded, recordedMidBO);

        }
        /* 不良abs检查 */
        recordedMidBO.checkBadAbs();

        //账户在核销封锁码下 发生的特殊交易 拒绝
        specificBlockCodeChargeOffService.blockCodeChargeOffProcessor(recordedMidBO, recorded);

        //入账字段调整
        fixRecorded(recorded, recordedMidBO);
    }

    //如果存在管理账户ID，并且当期资产包编号非空，则根据管理账户ID+当期资产包编号，读取ABS状态表，
    // 并且读到的记录abs状态=A-已出表，如果能读到记录，则不允许借记交易入账，不允许交易流水中带有分期订单号的交易入账
    private void checkTransactionAbs(RecordedMidBO recordedMidBO, RecordedBO recorded) {
        AccountManagementInfoDTO accountManagementInfo = recordedMidBO.accountBO.getAccountManagementInfo();
        TransactionCodeResDTO transactionCodeResDTO = recordedMidBO.getTransactionCodeResDTO();
        if (null != accountManagementInfo) {
            String absProductCodeCurr = accountManagementInfo.getAbsProductCodeCurr();
            if (null != absProductCodeCurr && !"".equals(absProductCodeCurr)) {
                //交易账户出表的判断
                List<AccountAbsstatus> absList = accountAbsstatusSelfMapper.selectByMidOrgAssetNoAndAbsStatus(accountManagementInfo.getAccountManagementId(),
                        accountManagementInfo.getOrganizationNumber(), absProductCodeCurr, "A");
                if (null != absList && !absList.isEmpty()) {
                    if (DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode().equals(transactionCodeResDTO.getDebitCreditIndicator())
                            && (
                            !(TransactionAttributeEnum.CONSUME_INTEREST.getCode().equals(transactionCodeResDTO.getTransactionAttribute())
                                    || TransactionAttributeEnum.CASH_INTEREST.getCode().equals(transactionCodeResDTO.getTransactionAttribute())
                                    || TransactionAttributeEnum.CONSUME_FEE.getCode().equals(transactionCodeResDTO.getTransactionAttribute())
                                    || TransactionAttributeEnum.CASH_FEE.getCode().equals(transactionCodeResDTO.getTransactionAttribute()))
                                    || (null != recorded.getTxnInstallmentOrderId() && !"".equals(recorded.getTxnInstallmentOrderId()))
                    )
                    ) {
                        log.error("管理账户出表的异常,管理账户id：{}", accountManagementInfo.getAccountManagementId());
                        throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_TRANS_ABS_ERROR);
                    }
                }
            }

        }
    }

    /**
     * 读取客户对账控制表（判断业务日期是否当前累计日，如果是需要机构日期进行日切操作）
     */
    public CustReconciliationControlDTO getControl(RecordedBO recorded, RecordedMidBO recordedMidBO) {

        CustReconciliationControlDTO control = txnRecordedAccountCacheService.getCustReconciliationControl();
        //入账日期与外围无关，入账自己计算
        if (control != null) {
            OrganizationInfoResDTO organizationInfoResDTO = recordedMidBO.getOrganizationInfoResDTO();
            LocalDate accruedDay = organizationInfoResDTO.getAccruedThruDay();
            LocalDate today = organizationInfoResDTO.getToday();
            LocalDate nextProcessingDate = organizationInfoResDTO.getNextProcessingDay();

            // 新入账日期
            LocalDate billingDate = custReconciliationControlService.getBillingDate(control, accruedDay, today, nextProcessingDate);
            recorded.setTxnBillingDate(billingDate);
            recordedMidBO.getRecorded().setTxnBillingDate(billingDate);
            recordedMidBO.getTransactionBO().setTxnBillingDate(billingDate);
        }

        //取现手续费：交易来源为2-内生并且交易属性为交易属性为6
        //分期手续费/提前结清手续费：交易来源为2-内生并且交易属性为H
        String transactionSource = recorded.getTxnTransactionSource() == null ? ""
                : recorded.getTxnTransactionSource();
        String transactionAttribute = recordedMidBO.getTransactionCodeResDTO().getTransactionAttribute();
        boolean innerFlag = TransactionSourceEnum.INNER_FEE.getCode().equals(transactionSource);
        boolean cashFeeFlag = !TransactionAttributeEnum.CASH_FEE.getCode().equals(transactionAttribute);
        boolean installFeeFlag = !TransactionAttributeEnum.INSTALL_FEE.getCode().equals(transactionAttribute);
        boolean vaSysFlag = TransactionSourceEnum.TRIGGER_BY_SYS.getCode().equals(transactionSource);
        if ((innerFlag && cashFeeFlag && installFeeFlag) || vaSysFlag) {
            recorded.setTxnTransactionDate(recorded.getTxnBillingDate()
                    .atTime(LocalTime.of(23, 59, 59)));
            recordedMidBO.getRecorded().setTxnTransactionDate(recorded.getTxnBillingDate()
                    .atTime(LocalTime.of(23, 59, 59)));
            recordedMidBO.getTransactionBO().setTxnTransactionDate(recorded.getTxnBillingDate()
                    .atTime(LocalTime.of(23, 59, 59)));
        }

        // 如果交易日期为空，则赋值入账日期（录入/内生/衍生入账）都有可能是空
        // 该部分之前不支持修改内生入账修改交易日期但是存在空指针问题，因此取消了限制，如果后续还存在问题请梳理一下逻辑
        if (recordedMidBO.getTransactionBO().getTxnTransactionDate() == null) {
            // 新交易日期
            recorded.setTxnTransactionDate(recorded.getTxnBillingDate().atTime(LocalTime.now()));
            recordedMidBO.getRecorded().setTxnTransactionDate(recorded.getTxnBillingDate().atTime(LocalTime.now()));
            recordedMidBO.getTransactionBO().setTxnTransactionDate(recorded.getTxnBillingDate().atTime(LocalTime.now()));
        }

        recordedMidBO.setControl(control);

        return control;
    }

    /**
     * 原交易匹配处理
     *
     * @param recordedMidBO 入账信息
     */
    public void processOriginTransaction(RecordedMidBO recordedMidBO) {
        if (!"7".equals(recordedMidBO.transactionCodeResDTO.getTransactionAttribute())
                && "C".equals(recordedMidBO.transactionCodeResDTO.getDebitCreditIndicator())) {
            if (StringUtils.isNotBlank(recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber())) {

                List<PostedTransaction> postedTransactions = txnRecordedAccountCacheService.
                        postedTransactionSelectByOrgNumAndOriginGlobalFlowNum(recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber(), recordedMidBO.recorded.getTxnOriginalGlobalFlowNumber(), recordedMidBO.getAccountBO().getAccountManagementInfo().getCustomerId());
                if (postedTransactions != null) {
                    for (PostedTransaction postedTransaction : postedTransactions) {
                        if ("D".equals(postedTransaction.getDebitCreditIndcator())) {
                            recordedMidBO.recorded.setTxnOriginalPostedTransactionId(postedTransaction.getPostedTransactionId());
                            recordedMidBO.recorded.setTxnOriginalTransactionBalanceId(postedTransaction.getTransactionBalanceId());
                            recordedMidBO.recorded.setTxnParentTransactionAccountId(postedTransaction.getTransactionBalanceId());
                            recordedMidBO.recorded.setTxnOriginalTransactionDate(postedTransaction.getTransactionDate());
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验卡片号码是否为空，若不为空返回true，否则返回false
     *
     * @param recorded 接口参数
     * @return boolean
     */
    private boolean isCardNumNotNull(RecordedBO recorded) {
        return StringUtils.isNotEmpty(recorded.getTxnCardNumber());
    }

    /**
     * 对入账一些字段进行修复
     * 1 添加流水号
     */
    private void fixRecorded(RecordedBO recorded, RecordedMidBO recordedMidBO) {
        //流水号
        if (StringUtils.isEmpty(recorded.getTxnGlobalFlowNumber())) {
            recorded.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        }
        //有时对象不一样
        if (StringUtils.isEmpty(recordedMidBO.recorded.getTxnGlobalFlowNumber())) {
            recordedMidBO.recorded.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        }

    }
}
