package com.anytech.anytxn.transaction.batch.job.customer.statement.step.statementfile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.business.base.accounting.enums.WaiveFeeTakeTypeEnum;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.common.core.enums.AccountStatusEnum;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.enums.RelationshipIndicatorEnum;
import com.anytech.anytxn.common.core.enums.TransactionAttributeEnum;
import com.anytech.anytxn.common.core.utils.AsciiConvertsUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.base.customer.enums.AddressTypeEnum;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAddressInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAddressInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.parameter.account.mapper.InterestBearingSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBalancePricingTableSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmLateFeeTableSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.model.InterestBearing;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBalancePricingTable;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLateFeeTable;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmStatementProcess;
import com.anytech.anytxn.parameter.base.common.service.IParamStatementMessageService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCountryCode;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementAccountBodyDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementAccountHeaderDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementAdditionalMsgDTO;
import com.anytech.anytxn.transaction.base.constants.StatementConstants;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementCustomerFileBodyDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementCustomerHeaderRecordDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementCustomerTailRecordDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementFileBodyContext;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementInstallmentPlanRecordDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementRepaymentPeriodMsgDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementTransDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementTransactionDetailMDTO;
import com.anytech.anytxn.transaction.base.domain.dto.statement.StatementTransactionDetailRecordDTO;
import com.anytech.anytxn.transaction.base.enums.AdditionalInformationEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.batch.job.customer.statement.step.AbstractStatementProcess;
import com.anytech.anytxn.transaction.batch.job.customer.StatementFileNameEnum;
import com.anytech.anytxn.transaction.batch.job.customer.statement.utils.StatementFileUtils;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sukang
 * @Date: 2021/11/12 15:51
 * <p>
 * Fields with data type 'C' are all left justified, with trailing spaces.
 * Fields with data type 'N' are all right justified, with leading zeros
 * <p>
 * C 左对齐 右边补空格
 * N 右对齐 左边补0
 * <p>
 * 1203 + 2;
 * <p>
 * <p>
 * STMTFT   1 - paper
 * HSTMTFT  0 - no, 2- email, 3 - SMS
 */
public class StatementCustomerBodyProcess extends AbstractStatementProcess implements ItemProcessor<AccountStatementInfo, StatementCustomerFileBodyDTO> {

    private static final Logger logger = LoggerFactory.getLogger(StatementCustomerBodyProcess.class);


    @Value("#{jobParameters['job.model']}")
    private String model;
    private List<String> accProductList;
    private List<String> skipAccountProduct;
    private static final String ON_BONUS_NA="N.A";
    private static final String NO_BONUS_SIGN="-";
    @Resource
    private LimitCustCreditInfoMapper limitCustCreditInfoMapper;

    private SqlSessionFactory sqlSessionBusinessFactory;

    private SqlSessionFactory sqlSessionFactoryCommon;

    private ParmOrganizationInfo parmOrganizationInfo;

    public static final Map<String, AtomicInteger> CUSTOMER_COUNTER = new HashMap<>();

    @Resource
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;


    @Resource
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;

    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Resource
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Resource
    private InstallOrderMapper installOrderMapper;

    @Resource
    private PostedTransactionSelfMapper postedTransactionSelfMapper;

    @Resource
    private CustomerAddressInfoSelfMapper customerAddressInfoSelfMapper;

    @Resource
    private CardBasicInfoSelfMapper cardBasicInfoSelfMapper;

    @Resource
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;


    @Resource
    private ParmCountryCodeSelfMapper parmCountryCodeSelfMapper;

    @Resource
    private ParmAcctProductMainInfoSelfMapper acctProductMainInfoSelfMapper;

    @Resource
    private ParmStatementProcessSelfMapper parmStatementProcessSelfMapper;

    @Resource
    private CorporateCustomerInfoSelfMapper corporateCustomerInfoSelfMapper;


    @Resource
    private ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;

    @Resource
    private IParamStatementMessageService iParamStatementMessageService;

    @Resource
    private InstallPlanSelfMapper installPlanSelfMapper;

    @Resource
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;

    @Resource
    private ParmTransactionCodeSelfMapper parmTransactionCodeSelfMapper;

    @Resource
    private ParmLateFeeTableSelfMapper lateFeeTableSelfMapper;
    @Resource
    private ParmProductInfoSelfMapper acctProductInfoSelfMapper;

    @Resource
    ParmStatementProcessSelfMapper statementProcessSelfMapper;

    @Resource
    ParmBalancePricingTableSelfMapper balancePricingTableSelfMapper;
    @Resource
    InterestBearingSelfMapper bearingSelfMapper;

    List<ParmTransactionCode> parmTransactionCodes = new ArrayList<>(500);

    Map<String, ParmTransactionCode> parmTransactionCodeHashMap;

    Map<String, Map<String, String>> statementMsgMap;


    static {

        AtomicInteger paperStatementFile = new AtomicInteger(1);
        CUSTOMER_COUNTER.putIfAbsent("1", paperStatementFile);

        AtomicInteger statementFile = new AtomicInteger(1);
        CUSTOMER_COUNTER.putIfAbsent("0", statementFile);
        CUSTOMER_COUNTER.putIfAbsent("2", statementFile);
        CUSTOMER_COUNTER.putIfAbsent("3", statementFile);
    }

    public StatementCustomerBodyProcess(SqlSessionFactory sqlSessionBusinessFactory,
                                        ParmOrganizationInfo parmOrganizationInfo,
                                        SqlSessionFactory sqlSessionFactoryCommon,List<String> accProductList,List<String> skipaccProductList) {
        this.sqlSessionBusinessFactory = sqlSessionBusinessFactory;
        this.parmOrganizationInfo = parmOrganizationInfo;
        this.sqlSessionFactoryCommon = sqlSessionFactoryCommon;
        this.accProductList= accProductList;
        this.skipAccountProduct= skipaccProductList;
    }

    private void initTransCode() {

        //获取交易码值
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(this.parmTransactionCodes)) {
            List<ParmTransactionCode> transactionCodes = parmTransactionCodeSelfMapper.selectAll(OrgNumberUtils.getOrg());
            parmTransactionCodes.addAll(transactionCodes);

            this.parmTransactionCodeHashMap = parmTransactionCodes.stream()
                    .collect(Collectors.toMap(ParmTransactionCode::getTransactionCode, Function.identity()));
        }

        if (MapUtils.isEmpty(this.statementMsgMap)) {
            this.statementMsgMap = iParamStatementMessageService.getAllStatementMsg();
        }

    }

    @Override
    public StatementCustomerFileBodyDTO process(AccountStatementInfo accountStatementInfo) {

        if (CUSTOMER_COUNTER.get("1").get() % 2000 == 0) {
            logger.info("Paper statement type CT index is {}", CUSTOMER_COUNTER.get("1").get());
        }

        if (CUSTOMER_COUNTER.get("0").get() % 2000 == 0) {
            logger.info("Email statement type CT index is {}", CUSTOMER_COUNTER.get("0").get());
        }


        try {
            return processStatementBody(accountStatementInfo);
        } catch (Exception e) {
            logger.error("Account management ID {} process error", accountStatementInfo.getAccountManagementId(), e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_ERROR);
        }

    }


    public StatementCustomerFileBodyDTO processStatementBody(AccountStatementInfo accountStatementInfo) {

        StatementFileBodyContext statementFileBodyContext = initCustomerAccountManagerInfo(accountStatementInfo,
                accountManagementInfoSelfMapper, acctProductMainInfoSelfMapper);

        if (StringUtils.equalsAny(statementFileBodyContext.getParmAcctProductMainInfo().getAttribute(),
                "T", "V")) {
            return null;
        }

        AccountManagementInfo accountManagementInfo = statementFileBodyContext.getAccountManagementInfo();

        String customerId = isCorporateLiability(accountManagementInfo.getLiability())
                ? accountManagementInfo.getCorporateCustomerId() : accountManagementInfo.getCustomerId();


        StatementCustomerFileBodyDTO fileBodyDTO = new StatementCustomerFileBodyDTO();

        /*
           1.如果模式是公司账户, 则不处理个人账户
           2.如果模式是个人账户, 则不处理公司账户
         */
        if (personalAndCorporateDistinguish(fileBodyDTO, model, accountManagementInfo)) {
            return fileBodyDTO;
        }

        //账单文件数据核心处理开始
        initTransCode();
        initCustomerBasicInfo(statementFileBodyContext, corporateCustomerInfoSelfMapper, customerBasicInfoSelfMapper);
        initStatementInfoMap(statementFileBodyContext);
        initPostedTransaction(statementFileBodyContext);


        if (statementFileBodyContext.processFlag()) {
            return null;
        }

        /*客户的本期账单相关的信息查询结束, 开始赋值*/
        fileBodyDTO.setCustomerId(customerId);
        fileBodyDTO.setStatementType(getStatementFileType(statementFileBodyContext));


        StatementCustomerHeaderRecordDTO customerHeaderRecordDTO = new StatementCustomerHeaderRecordDTO();
        customerHeaderRecordDTO.setRecordType(StatementConstants.CUSTOMER_HEADER);
        customerHeaderRecordDTO.setSequenceNumber(
                String.valueOf(CUSTOMER_COUNTER.get(fileBodyDTO.getStatementType()).getAndIncrement()));

//        customerHeaderRecordDTO.setUniqueReferenceNumber(String.valueOf(IdGeneratorManager.numberId16Generator().generateId()));TODO 待依赖包更新
        customerHeaderRecordDTO.setCustomerId(customerId);
        customerHeaderRecordDTO.setProductCategory("");


        //设置客户的地址信息
        if (statementFileBodyContext.getCorpLiquidation()) {
            setCorpAddressInfo(customerHeaderRecordDTO, statementFileBodyContext);
        } else {
            setCustomerAddressInfo(customerHeaderRecordDTO, statementFileBodyContext);
        }
        //账单寄送地址不从公司客户信息/个人客户信息上取
        //设置客户的账单信息 (上一账单日)
        setCustomerStatementDate(customerHeaderRecordDTO, statementFileBodyContext);

        List<StatementAccountBodyDTO> statementAccountBodyS = getStatementAccountBodyS(customerId, statementFileBodyContext);
        if (CollectionUtils.isEmpty(statementAccountBodyS)){
            return null;
        }
        //设置客户的账户AC信息
        fileBodyDTO.setStatementAccountBodyS(statementAccountBodyS);

        //按照规则设置还款日期
        setCustomerPaymentDueDate(customerHeaderRecordDTO, statementFileBodyContext);

        customerHeaderRecordDTO.setPreviousBalance(statementFileBodyContext.getStatementInfoMap()
                .get(statementFileBodyContext.getAccountManagementId()).getBeginBalance());

        customerHeaderRecordDTO.setPreviousBalanceSign(getAmountSign(customerHeaderRecordDTO.getPreviousBalance()));
        customerHeaderRecordDTO.setMinimumPayment(getMinimumPayment(statementFileBodyContext.getStatementInfoMap()));
        customerHeaderRecordDTO.setCombinedCreditLimit(BigDecimal.ZERO);
        customerHeaderRecordDTO.setCombinedCreditLimitBarcoding("0");

        fileBodyDTO.setStatementCustomerHeaderRecordDTO(customerHeaderRecordDTO);

        Optional<BigDecimal> outStandingBalance = fileBodyDTO.getStatementAccountBodyS()
                .stream().map(t -> t.getAccountHeaderDTO().getOutstandingBalance())
                .reduce(BigDecimal::add);

        customerHeaderRecordDTO.setOutstandingBalance(outStandingBalance.orElse(BigDecimal.ZERO));
        customerHeaderRecordDTO.setOutstandingBalanceSign(getAmountSign(customerHeaderRecordDTO.getOutstandingBalance()));

        //设置客户的交易信息
        setCustomerTransData(statementFileBodyContext, customerHeaderRecordDTO);

        //设置账户的还款信息,由于需要用到账户和客户的相同数据因此放在账户和客户数据构建完成后
        setAccountPeriodRepayment(fileBodyDTO, statementFileBodyContext);

        //其它数据
        customerHeaderRecordDTO.setEmailAddress(statementFileBodyContext.getCustomerBasicInfo().getEmail());
        customerHeaderRecordDTO.setCorporateId("");
        customerHeaderRecordDTO.setDirectMailIndicator("Y");
        customerHeaderRecordDTO.setFiller("");
        setEmbossName(fileBodyDTO, customerHeaderRecordDTO, statementFileBodyContext);

        //设置StatementMessage
        setAdditoinalStatementMessage(fileBodyDTO, statementFileBodyContext);


        //按照账户组装交易信息
        setTransInformation(statementFileBodyContext, fileBodyDTO);


        //設置客戶總計信息
        setCustomerTailRecord(fileBodyDTO, statementFileBodyContext);


        return fileBodyDTO;
    }


    private void initPostedTransaction(StatementFileBodyContext statementFileBodyContext) {
        List<PostedTransaction> postTransRecords = getPostTransRecords(statementFileBodyContext.getStatementInfoMap());

        statementFileBodyContext.setPostedTransactions(postTransRecords);
    }

    private void initStatementInfoMap(StatementFileBodyContext statementFileBodyContext) {

        //获取该客户的所有管理账户对应的账单信息和上一账单信息
        List<AccountStatementInfo> statementInfos = accountStatementInfoSelfMapper.selectStatementAndLastStatementByMid(
                statementFileBodyContext.getAccountManagementId(), parmOrganizationInfo.getAccruedThruDay());


        AccountStatementInfo currentPeriodStatementInfo = statementInfos.get(0);
        statementFileBodyContext.setStatementInfoMap(ImmutableMap.of(currentPeriodStatementInfo.getAccountManagementId(), currentPeriodStatementInfo));


        if (org.apache.commons.collections4.CollectionUtils.size(statementInfos) == 2) {
            statementFileBodyContext.setLastStatementInfo(statementInfos.get(1));
        }

    }


    private List<PostedTransaction> getPostTransRecords(Map<String, AccountStatementInfo> statementInfoMap) {
        //交易流水中只需要C D的交易 排除掉M N的
        List<PostedTransaction> postedTransRecords = new ArrayList<>();
        statementInfoMap.values().forEach(t -> postedTransRecords.addAll(
                postedTransactionSelfMapper.selectByAccStaIdInStatementFile(t.getStatementId(), t.getOrganizationNumber())));
        return postedTransRecords.stream()
                .filter(t -> StringUtils.equalsAny(t.getDebitCreditIndcator(), "C", "D")
                        && !StringUtils.equalsAny(t.getIfiIndicator(), "N"))
                .collect(Collectors.toList());
    }

    /**
     * 1.如果模式是公司账户, 则不处理个人账户
     * 2.如果模式是个人账户, 则不处理公司账户
     */
    private boolean personalAndCorporateDistinguish(StatementCustomerFileBodyDTO fileBodyDTO,
                                                    String model,
                                                    AccountManagementInfo accountManagementInfo) {

        if (StatementFileNameEnum.isCorporateModel(model) && !isCorporateAccount(accountManagementInfo)) {
            fileBodyDTO.setGenerateStatementFile(false);
            return true;
        }

        if (!StatementFileNameEnum.isCorporateModel(model) && isCorporateAccount(accountManagementInfo)) {
            fileBodyDTO.setGenerateStatementFile(false);
            return true;
        }

        return false;
    }


    /**
     * 设置客户的账单开始日期和账单结束日期
     * 1. 如果是首期则取 上月的的客户账单日到当前账单日之间
     * 2. 如果非首期, 则取上个账单表的账单日到当前账单日之间
     */
    private void setCustomerStatementDate(StatementCustomerHeaderRecordDTO customerHeaderRecordDTO,
                                          StatementFileBodyContext statementFileBodyContext) {

        Map<String, AccountStatementInfo> statementInfoMap = statementFileBodyContext.getStatementInfoMap();
        AccountManagementInfo accountManagementInfo = statementFileBodyContext.getAccountManagementInfo();
        CustomerBasicInfo customerBasicInfo = statementFileBodyContext.getCustomerBasicInfo();


        AccountStatementInfo accountStatementInfo = statementInfoMap.get(accountManagementInfo.getAccountManagementId());

        if (StringUtils.isBlank(accountStatementInfo.getLastStatementId()) || Objects.equals("0", accountStatementInfo.getLastStatementId())) {
            customerHeaderRecordDTO.setStatementStartDate(StatementFileUtils.formatterDmy.format(LocalDate.of(parmOrganizationInfo.getToday().getYear(),
                    parmOrganizationInfo.getAccruedThruDay().getMonth().minus(1), customerBasicInfo.getCycleDay())));
        } else {
            AccountStatementInfo lastStatementInfo = statementFileBodyContext.getLastStatementInfo();
            customerHeaderRecordDTO.setStatementStartDate(StatementFileUtils.formatterDmy.format(lastStatementInfo.getStatementDate()));
        }


        customerHeaderRecordDTO.setStatementEndDate(StatementFileUtils.formatterDmy.format(accountStatementInfo.getStatementDate()));
    }


    private void setEmbossName(StatementCustomerFileBodyDTO fileBodyDTO, StatementCustomerHeaderRecordDTO customerHeaderRecordDTO,
                               StatementFileBodyContext statementFileBodyContext) {


        String cardholderNumber = fileBodyDTO.getStatementAccountBodyS().get(0).getAccountHeaderDTO().getCardholderNumber();
        CardBasicInfo cardBasicInfo = cardBasicInfoSelfMapper.selectByCardNumberInStatementFile(cardholderNumber, statementFileBodyContext.getOrgNumber());
        customerHeaderRecordDTO.setFullName(getEmbossingName(cardBasicInfo, statementFileBodyContext));

        statementFileBodyContext.setCardBasicInfo(cardBasicInfo);

        if (isCorporateLiability(statementFileBodyContext.getAccountManagementInfo().getLiability())) {
            customerHeaderRecordDTO.setFullName(statementFileBodyContext.getCorporateCustomerInfo().getName());
        }

    }


    private void setCustomerTailRecord(StatementCustomerFileBodyDTO fileBodyDTO, StatementFileBodyContext statementFileBodyContext) {

        StatementCustomerTailRecordDTO statementCustomerTailRecordDTO = new StatementCustomerTailRecordDTO();
        statementCustomerTailRecordDTO.setRecordType(StatementConstants.CUSTOMER_TAIL);
        statementCustomerTailRecordDTO.setNumberOfAccounts(String.valueOf(fileBodyDTO.getStatementAccountBodyS().size()));

        long tdtCount = 0L;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fileBodyDTO.getTransactionDetailRecordDtoS())) {
            tdtCount = fileBodyDTO.getTransactionDetailRecordDtoS()
                    .stream().filter(t -> t instanceof StatementTransactionDetailRecordDTO)
                    .filter(t -> {
                        StatementTransactionDetailRecordDTO s = (StatementTransactionDetailRecordDTO) t;
                        return Objects.equals(s.getRecordType(), "TD");
                    }).count();
        }


        statementCustomerTailRecordDTO.setNumberOfTransactions(String.valueOf(tdtCount));

        long count = fileBodyDTO.getStatementAccountBodyS().stream().filter(t -> t.getStatementRepaymentPeriodMsgDTO() != null).count();
        statementCustomerTailRecordDTO.setNumberOfStatement(String.valueOf(count));


        //
        statementCustomerTailRecordDTO.setMessagesFlyerInsertIndicator(getMessagesFlyerInsertIndicator(statementFileBodyContext));

        fileBodyDTO.setStatementCustomerTailRecordDTO(statementCustomerTailRecordDTO);


    }


    /*
        1. 只要有一个账户是激活状态则为Y,都不是激活状态就是N
     */
    private String getMessagesFlyerInsertIndicator(StatementFileBodyContext statementFileBodyContext) {

        List<String> statusList = statementFileBodyContext.getAccountManagementInfos().stream()
                .map(AccountManagementInfo::getAccountStatus).collect(Collectors.toList());

        return statusList.contains(AccountStatusEnum.ACTIVE.getCode()) ? "Y" : "N";
    }


    private void setTransInformation(StatementFileBodyContext statementFileBodyContext,
                                     StatementCustomerFileBodyDTO fileBodyDTO) {


        List<StatementTransDTO> statementTransDtoS = new ArrayList<>();

        fileBodyDTO.getStatementAccountBodyS().forEach(accountHeader -> {
            setPostTransaction(statementFileBodyContext, accountHeader, statementTransDtoS);

            //设置分期计划
            setInstallPlan(statementFileBodyContext.getPostedTransactions(), statementTransDtoS);
        });

        fileBodyDTO.setTransactionDetailRecordDtoS(statementTransDtoS);

    }

    /**
     * 截取Tp的长度信息, 不能超过32位
     */
    private String getTpDesc(String desc) {
        String newDesc = "";
        if (StringUtils.isBlank(desc)) {
            return "";
        }

        if (desc.length() < 32) {
            newDesc = desc;
        } else {
            newDesc = desc.substring(0, 32);
        }

        return AsciiConvertsUtils.nonAsciiReplaceBlank(newDesc);
    }


    private void setInstallPlan(List<PostedTransaction> postedTransRecords,
                                List<StatementTransDTO> statementTransDtoS) {


        List<String> installOrderIds = postedTransRecords.stream()
                .filter(postedTransaction -> StringUtils.isNotBlank(postedTransaction.getInstallmentOrderId()))
                .map(PostedTransaction::getInstallmentOrderId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(installOrderIds)) {
            return;
        }

        List<InstallOrder> installOrders = installOrderSelfMapper.selectIds(installOrderIds);

        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<StatementInstallmentPlanRecordDTO> planRecordDTOList = installOrders.stream().map(e -> {

            StatementInstallmentPlanRecordDTO planRecordDTO = new StatementInstallmentPlanRecordDTO();
            planRecordDTO.setRecordType(StatementConstants.INSTALL_DETAIL);
            planRecordDTO.setSequenceNumber(String.valueOf(atomicInteger.incrementAndGet()));
            planRecordDTO.setCardholderNumber(e.getCardNumber());
            planRecordDTO.setProgramCode(e.getProductCode());
            InstallProductInfo installProductInfo = installProductInfoSelfMapper.selectByIndex(e.getOrganizationNumber(), e.getProductCode());
            installProductInfo.setProductDesc(installProductInfo.getProductDesc());
            planRecordDTO.setProgramDescription(getTpDesc(installProductInfo.getProductDesc()));
            planRecordDTO.setMerchantId(e.getMerchantId());

            if (StringUtils.isNotBlank(e.getMerchantName())) {
                planRecordDTO.setMerchantName(getTpDesc(e.getMerchantName()));
            } else {
                planRecordDTO.setMerchantName(getTpDesc(e.getTransactionDesc()));
            }

            planRecordDTO.setInstallmentEnrolmentDate(StatementFileUtils.formatYmd.format(e.getTransactionDate()));

            InstallPlan installPlan = installPlanSelfMapper.selectPlansByOrderIdByTerm(e.getOrderId(), 1);

            //首期金额
            planRecordDTO.setFirstInstallmentAmount(installPlan.getTermAmount().add(installPlan.getFeeAmount())
                    .subtract(installPlan.getDerateAmount()).subtract(installPlan.getPrincipalDerateFeeAmount()));
            planRecordDTO.setFirstInstallmentAmountSign(getAmountSign(planRecordDTO.getFirstInstallmentAmount()));

            //
            planRecordDTO.setMonthlyInstallmentAmount(e.getTermAmount());
            planRecordDTO.setMonthlyInstallmentAmountSign(getAmountSign(planRecordDTO.getMonthlyInstallmentAmount()));
            planRecordDTO.setInterestRate("0");
            planRecordDTO.setMonthlyInstallmentInterestAmount(BigDecimal.ZERO);
            planRecordDTO.setMonthlyInstallmentInterestAmountSign("+");


            planRecordDTO.setTotalInstallmentAmount(e.getInstallmentAmount());
            planRecordDTO.setTotalInstallmentAmountSign(getAmountSign(planRecordDTO.getTotalInstallmentAmount()));


            planRecordDTO.setRemainingInstallmentAmount(e.getUnpostedAmount());
            planRecordDTO.setRemainingInstallmentAmountSign(getAmountSign(planRecordDTO.getRemainingInstallmentAmount()));


            planRecordDTO.setTotalNumberOfInstallment(String.valueOf(e.getTerm()));
            //已出账单的分期数量
            planRecordDTO.setTotalNumberOfBilledInstallment(String.valueOf(e.getPostedTerm()));

            planRecordDTO.setFiller("");
            return planRecordDTO;
        }).collect(Collectors.toList());

        statementTransDtoS.addAll(planRecordDTOList);
    }


    /**
     * TDP
     * TDT
     * TDT
     * ...
     * TDS
     * TDM(账户有自扣才存在)
     */
    private void setPostTransaction(StatementFileBodyContext statementFileBodyContext,
                                    StatementAccountBodyDTO accountHeader,
                                    List<StatementTransDTO> statementTransDTOS) {
        List<PostedTransaction> postedTransRecords = statementFileBodyContext.getPostedTransactions();

        postedTransRecords.forEach(t -> {
            if (StringUtils.isBlank(t.getCardNumber())) {
                t.setCardNumber(accountHeader.getAccountHeaderDTO().getCardholderNumber());
            } else {
                t.setCardNumber(t.getCardNumber().trim());
            }
        });

        List<String> cardNumbers = postedTransRecords.stream()
                .map(PostedTransaction::getCardNumber).collect(Collectors.toList());

        cardNumbers.add(accountHeader.getAccountHeaderDTO().getCardholderNumber());
        cardNumbers = cardNumbers.stream().distinct().collect(Collectors.toList());

        Map<String, CardBasicInfo> cardBasicInfoMap = cardNumbers.stream().collect(Collectors.toMap(Function.identity(),
                t -> getCardBasicInfo(t, statementFileBodyContext),
                (key1, key2) -> key1));


        Map<String, CardAuthorizationInfo> cardAuthorizationInfoMap = cardNumbers.stream().collect(Collectors.toMap(Function.identity(),
                t -> {
                    if (statementFileBodyContext.getCardAuthorizationInfosCardNumbers().containsKey(t)) {
                        return statementFileBodyContext.getCardAuthorizationInfosCardNumbers().get(t);
                    } else {
                        return cardAuthorizationInfoSelfMapper.selectByCardNumberInStatementFile(t);
                    }
                }, (key1, key2) -> key1));


        String basicSupplementaryCardIndicator = getSupplementaryCardIndicator(accountHeader.getAccountHeaderDTO().getCardAuthorizationInfo());


        //先构建一个TDP
        StatementTransactionDetailRecordDTO tdpRecordDTO = new StatementTransactionDetailRecordDTO();
        tdpRecordDTO.setRecordType(StatementConstants.TRANS_DETAIL);
        tdpRecordDTO.setDetailRecordTag("P");
        tdpRecordDTO.setBillingCurrencyCode(getCurrencyCode(accountHeader.getAccountHeaderDTO().getBillingCurrency()));
        // P时不创建 直接给空格
        tdpRecordDTO.setTransactionDate("");
        tdpRecordDTO.setPostingDate("");
        tdpRecordDTO.setDescription("PREVIOUS BALANCE");
        tdpRecordDTO.setCardholderNumber(accountHeader.getAccountHeaderDTO().getCardholderNumber());
        tdpRecordDTO.setCardholderName(getEmbossingName(cardBasicInfoMap.get(accountHeader.getAccountHeaderDTO().getCardholderNumber()), statementFileBodyContext));
        tdpRecordDTO.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(accountHeader.getAccountHeaderDTO().getCardholderNumber())));
        tdpRecordDTO.setBatchReferenceNumber("");
        tdpRecordDTO.setTransactionReferenceNumber("");
        tdpRecordDTO.setAmount(statementFileBodyContext.getStatementInfoMap()
                .get(statementFileBodyContext.getAccountManagementId()).getBeginBalance());
        tdpRecordDTO.setAmountSign(getAmountSign(tdpRecordDTO.getAmount()));
        tdpRecordDTO.setCardLimit("");
        tdpRecordDTO.setFiller("");


        Map<String, List<PostedTransaction>> sortPostTrans = sortPostTrans(postedTransRecords, statementFileBodyContext);

        if (MapUtils.isEmpty(sortPostTrans)) {
            buildTdtWithNoTransaction(tdpRecordDTO, cardBasicInfoMap, cardAuthorizationInfoMap,
                    accountHeader, statementTransDTOS, statementFileBodyContext);
        } else {

            //先获取取现利息 消费利息的交易码信息
            List<String> interestCodes = new ArrayList<>(4);
            interestCodes.addAll(getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR,
                    Arrays.asList(TransactionAttributeEnum.CASH_INTEREST.getCode(), TransactionAttributeEnum.CONSUME_INTEREST.getCode())));
            //利息 为贷记属性的不合并
            /*interestCodes.addAll(getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.CREDIT_INDICATOR,
                    Arrays.asList(TransactionAttributeEnum.CASH_INTEREST.getCode(), TransactionAttributeEnum.CONSUME_INTEREST.getCode())));
            */
            buildTdtWithTransaction(sortPostTrans, tdpRecordDTO,
                    cardBasicInfoMap, cardAuthorizationInfoMap,
                    interestCodes, accountHeader, statementTransDTOS, statementFileBodyContext);

        }

        //如果账户是自扣状态 构建一个TDM
        if (Objects.equals("1", statementFileBodyContext.getAccountManagementInfo().getAutoPaymentFlag())
                && StringUtils.equalsAny(statementFileBodyContext.getAccountManagementInfo().getAutoPaymentType(), "3", "4")) {
            StatementTransactionDetailMDTO tdmTag = new StatementTransactionDetailMDTO();
            tdmTag.setRecordType(StatementConstants.TRANS_DETAIL);
            tdmTag.setDetailRecordTag("M");
            tdmTag.setCardholderNumber(accountHeader.getAccountHeaderDTO().getCardholderNumber());

            CardBasicInfo cardBasicInfo = getCardBasicInfo(accountHeader.getAccountHeaderDTO().getCardholderNumber(), statementFileBodyContext);

            tdmTag.setCardholderName(getEmbossingName(cardBasicInfo, statementFileBodyContext));
            tdmTag.setBillingCurrencyCode(getCurrencyCode(accountHeader.getAccountHeaderDTO().getBillingCurrency()));
            tdmTag.setBasicSupplementaryCardIndicator(basicSupplementaryCardIndicator);
            tdmTag.setTransactionDate("");
            tdmTag.setPostingDate("");
            tdmTag.setDescription(getTdmDescription(statementFileBodyContext.getAccountManagementInfo()));
            tdmTag.setFiller("");
            if (tdmTag.getDescription() != null) {
                statementTransDTOS.add(tdmTag);
            }
        }

    }

    private CardBasicInfo getCardBasicInfo(String cardholderNumber, StatementFileBodyContext statementFileBodyContext) {

        if (Objects.equals(cardholderNumber, statementFileBodyContext.getCardBasicInfo().getCardNumber())) {
            return statementFileBodyContext.getCardBasicInfo();
        } else {
            return cardBasicInfoSelfMapper.selectByCardNumberInStatementFile(cardholderNumber, statementFileBodyContext.getOrgNumber());
        }
    }


    private void buildTdtWithTransaction(Map<String, List<PostedTransaction>> sortPostTrans,
                                         StatementTransactionDetailRecordDTO tdpRecordDTO,
                                         Map<String, CardBasicInfo> cardBasicInfoMap,
                                         Map<String, CardAuthorizationInfo> cardAuthorizationInfoMap,
                                         List<String> interestCodes,
                                         StatementAccountBodyDTO accountHeader,
                                         List<StatementTransDTO> statementTransDTOS,
                                         StatementFileBodyContext statementFileBodyContext) {


        String firstCardNumber = sortPostTrans.keySet().stream().findFirst().orElse(null);

        if (!Objects.equals(firstCardNumber, tdpRecordDTO.getCardholderNumber())) {
            //如果 交易流水里排在最前的卡号和TDP(总是取的主卡号)不一致, 需要单独构建一个TDP 以及TDP的TDS
            statementTransDTOS.add(tdpRecordDTO);
            StatementTransactionDetailRecordDTO tdsTag = new StatementTransactionDetailRecordDTO();
            tdsTag.setRecordType(StatementConstants.TRANS_DETAIL);
            tdsTag.setDetailRecordTag("S");
            tdsTag.setCardholderNumber(tdpRecordDTO.getCardholderNumber());
            tdsTag.setCardholderName(getEmbossingName(cardBasicInfoMap.get(tdpRecordDTO.getCardholderNumber()), statementFileBodyContext));
            tdsTag.setBillingCurrencyCode(getCurrencyCode(accountHeader.getAccountHeaderDTO().getBillingCurrency()));
            tdsTag.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(tdpRecordDTO.getCardholderNumber())));
            tdsTag.setTransactionDate("");
            tdsTag.setPostingDate("");
            tdsTag.setDescription(getSubTotalDesc(tdpRecordDTO.getCardholderNumber()));
            tdsTag.setBatchReferenceNumber("");
            tdsTag.setTransactionReferenceNumber("");
            tdsTag.setAmount(getTdsAmount(statementTransDTOS));
            tdsTag.setAmountSign(getAmountSign(tdsTag.getAmount()));
            tdsTag.setCardLimit("");
            tdsTag.setFiller("");
            statementTransDTOS.add(tdsTag);
        }

        AtomicInteger atomicInteger = new AtomicInteger(1);
        sortPostTrans.forEach((cardNumber, value) -> {
            List<StatementTransDTO> tdtList = new ArrayList<>(value.size());

            if (Objects.equals(firstCardNumber, tdpRecordDTO.getCardholderNumber()) && atomicInteger.getAndIncrement() == 1) {
                tdtList.add(tdpRecordDTO);
            }

            //构建流水集合
            value.stream()
                    .filter(t -> !interestCodes.contains(t.getPostingTransactionCode()))
                    .forEach(element -> {
                        StatementTransactionDetailRecordDTO tdtTrans = new StatementTransactionDetailRecordDTO();
                        tdtTrans.setRecordType(StatementConstants.TRANS_DETAIL);
                        tdtTrans.setDetailRecordTag("T");
                        tdtTrans.setCardholderNumber(element.getCardNumber());
                        tdtTrans.setCardholderName(getEmbossingName(cardBasicInfoMap.get(element.getCardNumber()), statementFileBodyContext));
                        tdtTrans.setBillingCurrencyCode(getCurrencyCode(element.getPostingCurrencyCode()));
                        tdtTrans.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(element.getCardNumber())));
                        tdtTrans.setTransactionDate(StatementFileUtils.formatYmd.format(element.getTransactionDate()));
                        tdtTrans.setPostingDate(StatementFileUtils.formatYmd.format(element.getPostingDate()));
                        tdtTrans.setDescription(getTdtdesc(element));
                        tdtTrans.setBatchReferenceNumber("");
                        tdtTrans.setTransactionReferenceNumber(element.getRetrievalReferenceNumber());
                        tdtTrans.setAmount(element.getPostingAmount());

                        tdtTrans.setAmountSign(Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), element.getDebitCreditIndcator()) ? "-" : "+");
                        tdtTrans.setCardLimit("");
                        tdtTrans.setFiller("");
                        tdtList.add(tdtTrans);

                        //@todo 如果tdt中含有附加字段则 需要新增多个TDA
                        buildTdaTransRecord(tdtTrans, tdtList, element);
                    });


            //再将本卡号的所有利息信息合并
            setTdtInterestInfo(value, interestCodes, cardBasicInfoMap,
                    cardAuthorizationInfoMap, tdtList, statementFileBodyContext);

            statementTransDTOS.addAll(tdtList);
            //最后构建一个TDS 统计信息
            StatementTransactionDetailRecordDTO tdsTag = new StatementTransactionDetailRecordDTO();
            tdsTag.setRecordType(StatementConstants.TRANS_DETAIL);
            tdsTag.setDetailRecordTag("S");
            tdsTag.setCardholderNumber(cardNumber);
            tdsTag.setCardholderName(getEmbossingName(cardBasicInfoMap.get(cardNumber), statementFileBodyContext));
            tdsTag.setBillingCurrencyCode(getCurrencyCode(accountHeader.getAccountHeaderDTO().getBillingCurrency()));
            tdsTag.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(cardNumber)));
            tdsTag.setTransactionDate("");
            tdsTag.setPostingDate("");
            tdsTag.setDescription(getSubTotalDesc(cardNumber));
            tdsTag.setBatchReferenceNumber("");
            tdsTag.setTransactionReferenceNumber("");
            tdsTag.setAmount(getTdsAmount(tdtList));
            tdsTag.setAmountSign(getAmountSign(tdsTag.getAmount()));
            tdsTag.setCardLimit("");
            tdsTag.setFiller("");
            statementTransDTOS.add(tdsTag);

        });

    }


    /**
     * Airline Charge	IATA	AirLine ticket no	     LINE1: Ticket/ Document Number
     * 		                                             NAME	LINE2:Passenger Name
     * 		                                             DATE1	LINE3:
     * 		                                             DATE2	LINE4:
     *
     * 	DCI Inbound Transactions	AirLine ticket no	 LINE1: TICNO
     * 		                                             NAME	Line 2: NAME: (PASNG)
     * 		                                             DATE1	Line 3: DATE1: (DATE1) (CARR1) (ROUTO)/(ROUT1) FARE=(FARE1)
     DATE2	Line 4: DATE2: (DATE2) (CARR2) (ROUT2) FARE=(FARE2)

     * 	SIA	AirLine ticket no	                         LINE1: Airline Ticket No
     * 		                                             NAME	LINE2:Passenger Name
     * 		                                             DATE1	LINE3:
     * 		                                             DATE2	LINE4:
     *
     * ATM Charge	DCI Inbound Transactions		     LINE1: INSTU
     * 			                                         Line 2: CITST
     *                                                   Line 3: (Amount in charge currency) (Charge currency) BILLED AS SGD
     * Partial Extraction From Batch Interfaces
     * Foreign Currency and Insurance Charge
     */
    private void buildTdaTransRecord(StatementTransactionDetailRecordDTO tdtTrans,
                                     List<StatementTransDTO> tdtList,
                                     PostedTransaction element) {
        JSONObject jsonObject = null;
        if (StringUtils.isNotBlank(element.getJsonReserved())) {
            try {
                jsonObject = JSON.parseObject(element.getJsonReserved());

            } catch (Exception e) {
                return;
            }
            JSONArray airLine = (JSONArray) jsonObject.get("airLineInfo");
            JSONArray atmInfo = (JSONArray) jsonObject.get("atmInfo");
            JSONArray addendumInfos = (JSONArray) jsonObject.get("addendumInfo");
            if (!ObjectUtils.isEmpty(airLine)) {
                JSONObject jsonObj = (JSONObject) airLine.get(0);

                String ticketNumber = jsonObj.get(AdditionalInformationEnum.TICKET_NUMBER.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.TICKET_NUMBER.getCode()).toString();
                String passengerName = jsonObj.get(AdditionalInformationEnum.PASSENGER_NAME.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.PASSENGER_NAME.getCode()).toString();
                String currencyType = jsonObj.get(AdditionalInformationEnum.CURRENCY_TYPE.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.CURRENCY_TYPE.getCode()).toString();
                String transactionAmount = jsonObj.get(AdditionalInformationEnum.TRANSACTION_AMOUNT.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.TRANSACTION_AMOUNT.getCode()).toString();
                String tripInfos = jsonObj.get(AdditionalInformationEnum.TRIP_INFOS.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.TRIP_INFOS.getCode()).toString();
                logger.warn("Aviation additional info key fields: ticketNumber={}, passengerName={}", ticketNumber, passengerName);
                StatementTransactionDetailRecordDTO line1 = getTdtTransCopy(tdtTrans);
                line1.setDescription("TICKET NO: " + ticketNumber);
                tdtList.add(line1);
                StatementTransactionDetailRecordDTO line2 = getTdtTransCopy(tdtTrans);
                line2.setDescription("NAME: " + passengerName);
                tdtList.add(line2);
                //循环行程信息
                if (StringUtils.isNotEmpty(tripInfos)) {
                    JSONArray tripInfoArray = JSON.parseArray(tripInfos);
                    for (int i = 0; i < tripInfoArray.size(); i++) {
                        JSONObject tripInfo = (JSONObject) tripInfoArray.get(i);
                        String departureDate = tripInfo.get(AdditionalInformationEnum.DEPARTURE_DATE.getCode()) + "";
                        if (StringUtils.isNotBlank(departureDate) && 6 == departureDate.trim().length()) {
                            String newDepartureDate = departureDate.trim();
                            String month = newDepartureDate.substring(2, 4);
                            String day = newDepartureDate.substring(4, 6);
                            departureDate = day + month;
                        }
                        String carriter = tripInfo.get(AdditionalInformationEnum.CARRITER.getCode()) == null ? null : tripInfo.get(AdditionalInformationEnum.CARRITER.getCode()).toString().toString();
                        String startingPoint = tripInfo.get(AdditionalInformationEnum.STARTING_POINT.getCode()) == null ? null : tripInfo.get(AdditionalInformationEnum.STARTING_POINT.getCode()).toString().toString();
                        String disembarkationPoint = tripInfo.get(AdditionalInformationEnum.DISEMBARKATION_POINT.getCode()) == null ? null : tripInfo.get(AdditionalInformationEnum.DISEMBARKATION_POINT.getCode()).toString().toString();
                        String fareBas = tripInfo.get(AdditionalInformationEnum.FARE_BASIS.getCode()) == null ? null : tripInfo.get(AdditionalInformationEnum.FARE_BASIS.getCode()).toString().toString();
                        String fareBasis = null;
                        if (fareBas.length() >= 6) {
                            fareBasis = fareBas.substring(0, 6);
                        } else {
                            fareBasis = fareBas;
                        }
                        if (i == 0) {
                            StringBuilder info = new StringBuilder(departureDate);
                            info.append(" ").append(carriter).append(" ").append(startingPoint).append("/").append(disembarkationPoint).append(" ").append("FARE=").append(fareBasis);
                            StatementTransactionDetailRecordDTO date1 = getTdtTransCopy(tdtTrans);
                            date1.setDescription("DATE1: " + info.toString());
                            tdtList.add(date1);
                        } else {
                            StringBuilder info = new StringBuilder(departureDate);
                            info.append(" ").append(carriter).append(" ").append(disembarkationPoint).append(" ").append("FARE=").append(fareBasis);
                            StatementTransactionDetailRecordDTO date = getTdtTransCopy(tdtTrans);
                            StringBuilder datai = new StringBuilder("DATE");
                            datai.append(Integer.toString(i + 1)).append(": ").append(info);
                            date.setDescription(datai.toString());
                            tdtList.add(date);
                        }

                    }
                }

                if (!StringUtils.equals(currencyType, "SGD")) {
                    //外币附加一条记录
                    StringBuilder currencyInfo = new StringBuilder(transactionAmount);
                    currencyInfo.append(" ").append(currencyType).append(" ").append("BILLED AS SGD");
                    StatementTransactionDetailRecordDTO statementRecordDTO = getTdtTransCopy(tdtTrans);
                    statementRecordDTO.setDescription(currencyInfo.toString());
                    tdtList.add(statementRecordDTO);
                }
                return;
            }
            //atm附加信息
            if (!ObjectUtils.isEmpty(atmInfo)) {
                JSONObject jsonObj = (JSONObject) atmInfo.get(0);

                String establishmentName = jsonObj.get(AdditionalInformationEnum.establishment_name.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.establishment_name.getCode()).toString();
                String establishmentCity = jsonObj.get(AdditionalInformationEnum.establishment_city.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.establishment_city.getCode()).toString();
                String currencyType = jsonObj.get(AdditionalInformationEnum.CURRENCY_TYPE.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.CURRENCY_TYPE.getCode()).toString();
                String transactionAmount = jsonObj.get(AdditionalInformationEnum.TRANSACTION_AMOUNT.getCode()) == null ? null : jsonObj.get(AdditionalInformationEnum.TRANSACTION_AMOUNT.getCode()).toString();
                if (StringUtils.isEmpty(establishmentName) || StringUtils.isEmpty(establishmentCity) || StringUtils.isEmpty(currencyType) || StringUtils.isEmpty(transactionAmount)) {
                    logger.warn("ATM additional info key fields are empty, please check additional info from posted_transaction: {}", jsonObj);
                    return;
                }
                StatementTransactionDetailRecordDTO line1 = getTdtTransCopy(tdtTrans);
                line1.setDescription(" " + establishmentName);
                tdtList.add(line1);
                StatementTransactionDetailRecordDTO line2 = getTdtTransCopy(tdtTrans);
                line2.setDescription(" " + establishmentCity.toUpperCase());
                tdtList.add(line2);
                if (!StringUtils.equals(currencyType, "SGD")) {
                    //外币附加一条记录
                    StringBuilder currencyInfo = new StringBuilder(transactionAmount);
                    currencyInfo.append(" ").append(currencyType).append(" ").append("BILLED AS SGD");
                    StatementTransactionDetailRecordDTO statementRecordDTO = getTdtTransCopy(tdtTrans);
                    statementRecordDTO.setDescription(currencyInfo.toString());
                    tdtList.add(statementRecordDTO);
                }
                return;
            }
            //vicom的附加信息
            if (!ObjectUtils.isEmpty(addendumInfos)) {

                for (Object addendumInfo : addendumInfos) {
                    Map addendumInfoMap = JSONObject.parseObject(JSONObject.toJSONString(addendumInfo), Map.class);
                    for (Object key : addendumInfoMap.keySet()) {
                        Object value = addendumInfoMap.get(key);
                        if (!ObjectUtils.isEmpty(value)) {
                            StatementTransactionDetailRecordDTO line1 = getTdtTransCopy(tdtTrans);
                            line1.setDescription(" " + value);
                            tdtList.add(line1);
                        }
                    }
                }
            }
            //vicom附加信息若是外币也展示外币信息
            if (!StringUtils.equals(element.getTransactionCurrencyCode(), parmOrganizationInfo.getOrganizationCurrency())) {
                //外币附加一条记录
                StringBuilder currencyInfo = new StringBuilder(element.getTransactionAmount() + "");
                ParmCurrencyCode parmCurrencyCode = parmCurrencyCodeSelfMapper.selectByCurrencyCode(element.getTransactionCurrencyCode());
                currencyInfo.append(" ").append(parmCurrencyCode == null ? "" : parmCurrencyCode.getAlphabeticCode()).append(" ").append("BILLED AS SGD");
                StatementTransactionDetailRecordDTO statementRecordDTO = getTdtTransCopy(tdtTrans);
                statementRecordDTO.setDescription(currencyInfo.toString());
                tdtList.add(statementRecordDTO);
            }
        } else {
            //非航空信息，非atm信息但是是外币交易的展示外币信息
            if (!StringUtils.equals(element.getTransactionCurrencyCode(), parmOrganizationInfo.getOrganizationCurrency())) {
                //外币附加一条记录
                StringBuilder currencyInfo = new StringBuilder(element.getTransactionAmount() + "");
                ParmCurrencyCode parmCurrencyCode = parmCurrencyCodeSelfMapper.selectByCurrencyCode(element.getTransactionCurrencyCode());
                currencyInfo.append(" ").append(parmCurrencyCode == null ? "" : parmCurrencyCode.getAlphabeticCode()).append(" ").append("BILLED AS SGD");
                StatementTransactionDetailRecordDTO statementRecordDTO = getTdtTransCopy(tdtTrans);
                statementRecordDTO.setDescription(currencyInfo.toString());
                tdtList.add(statementRecordDTO);
            }
        }
    }


    private StatementTransactionDetailRecordDTO getTdtTransCopy(StatementTransactionDetailRecordDTO tdtTrans) {
        StatementTransactionDetailRecordDTO tda1 = new StatementTransactionDetailRecordDTO();
        tda1.setRecordType(StatementConstants.TRANS_DETAIL);
        tda1.setDetailRecordTag("A");
        tda1.setCardholderNumber(tdtTrans.getCardholderNumber());
        tda1.setCardholderName(tdtTrans.getCardholderName());
        tda1.setBillingCurrencyCode(tdtTrans.getBillingCurrencyCode());
        tda1.setBasicSupplementaryCardIndicator(tdtTrans.getBasicSupplementaryCardIndicator());
        tda1.setTransactionDate("00000000");
        tda1.setPostingDate("00000000");
        tda1.setDescription("");
        tda1.setBatchReferenceNumber("");
        tda1.setTransactionReferenceNumber("");
        tda1.setAmount(BigDecimal.ZERO);
        tda1.setAmountSign("");
        tda1.setCardLimit(tdtTrans.getCardLimit());
        tda1.setFiller("");
        return tda1;
    }


    /**
     * 针对于没有交易记录的账单信息 需要添加期初余额信息
     */
    private void buildTdtWithNoTransaction(StatementTransactionDetailRecordDTO tdpRecordDTO,
                                           Map<String, CardBasicInfo> cardBasicInfoMap,
                                           Map<String, CardAuthorizationInfo> cardAuthorizationInfoMap,
                                           StatementAccountBodyDTO accountHeader,
                                           List<StatementTransDTO> statementTransDTOS,
                                           StatementFileBodyContext statementFileBodyContext) {

        String cardholderNumber = accountHeader.getAccountHeaderDTO().getCardholderNumber();

        tdpRecordDTO.setCardholderNumber(cardholderNumber);
        tdpRecordDTO.setCardholderName(getEmbossingName(cardBasicInfoMap.get(cardholderNumber), statementFileBodyContext));
        tdpRecordDTO.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(cardholderNumber)));
        statementTransDTOS.add(tdpRecordDTO);


        StatementTransactionDetailRecordDTO tdsTag = new StatementTransactionDetailRecordDTO();
        tdsTag.setRecordType(StatementConstants.TRANS_DETAIL);
        tdsTag.setDetailRecordTag("S");
        tdsTag.setCardholderNumber(cardholderNumber);
        tdsTag.setCardholderName(getEmbossingName(cardBasicInfoMap.get(cardholderNumber), statementFileBodyContext));
        tdsTag.setBillingCurrencyCode(getCurrencyCode(accountHeader.getAccountHeaderDTO().getBillingCurrency()));
        tdsTag.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(cardAuthorizationInfoMap.get(cardholderNumber)));
        tdsTag.setTransactionDate("");
        tdsTag.setPostingDate("");
        tdsTag.setDescription(getSubTotalDesc(cardholderNumber));
        tdsTag.setBatchReferenceNumber("");
        tdsTag.setTransactionReferenceNumber("");
        tdsTag.setAmount(getTdsAmount(statementTransDTOS));
        tdsTag.setAmountSign(getAmountSign(tdsTag.getAmount()));
        tdsTag.setCardLimit("");
        tdsTag.setFiller("");
        statementTransDTOS.add(tdsTag);

    }


    private String getSupplementaryCardIndicator(CardAuthorizationInfo cardAuthorizationInfo) {
        return Objects.equals(cardAuthorizationInfo.getRelationshipIndicator(),
                RelationshipIndicatorEnum.MIAN_CARD.getCode()) ? "B" : "A";
    }


    private void setTdtInterestInfo(List<PostedTransaction> postedTransRecords,
                                    List<String> interestCodes,
                                    Map<String, CardBasicInfo> cardBasicInfoMap,
                                    Map<String, CardAuthorizationInfo> cardAuthorizationInfoMap,
                                    List<StatementTransDTO> tdtList,
                                    StatementFileBodyContext statementFileBodyContext) {


        List<PostedTransaction> transactionList = postedTransRecords
                .stream()
                .filter(t -> interestCodes.contains(t.getPostingTransactionCode()))
                .sorted(Comparator.comparing(PostedTransaction::getPostingDate).reversed())
                .collect(Collectors.toList());

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(transactionList)) {
            return;
        }

        //获取最新的那一条记录
        PostedTransaction postedTransaction = transactionList.get(0);

        Optional<BigDecimal> debitAmount = transactionList.stream().filter(t ->
                Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), t.getDebitCreditIndcator()))
                .map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add);

        Optional<BigDecimal> creditAmount = transactionList.stream().filter(t ->
                Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), t.getDebitCreditIndcator()))
                .map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add);

        BigDecimal totalInterest = debitAmount.orElse(BigDecimal.ZERO).subtract(creditAmount.orElse(BigDecimal.ZERO));
        postedTransaction.setTransactionAmount(totalInterest.abs());
        postedTransaction.setPostingAmount(totalInterest.abs());


        StatementTransactionDetailRecordDTO tdtTrans = new StatementTransactionDetailRecordDTO();
        tdtTrans.setRecordType(StatementConstants.TRANS_DETAIL);
        tdtTrans.setDetailRecordTag("T");
        tdtTrans.setCardholderNumber(postedTransaction.getCardNumber());
        tdtTrans.setCardholderName(getEmbossingName(cardBasicInfoMap.get(postedTransaction.getCardNumber()), statementFileBodyContext));
        tdtTrans.setBillingCurrencyCode(getCurrencyCode(postedTransaction.getPostingCurrencyCode()));

        tdtTrans.setBasicSupplementaryCardIndicator(getSupplementaryCardIndicator(
                cardAuthorizationInfoMap.get(postedTransaction.getCardNumber())));

        tdtTrans.setTransactionDate(StatementFileUtils.formatYmd.format(postedTransaction.getTransactionDate()));
        tdtTrans.setPostingDate(StatementFileUtils.formatYmd.format(postedTransaction.getPostingDate()));
        tdtTrans.setDescription("INTEREST CHARGE");
        tdtTrans.setBatchReferenceNumber("");
        tdtTrans.setTransactionReferenceNumber(postedTransaction.getRetrievalReferenceNumber());
        tdtTrans.setAmount(postedTransaction.getPostingAmount());


        String sign = totalInterest.compareTo(BigDecimal.ZERO) < 0 ? "-" : "+";

        tdtTrans.setAmountSign(sign);
        tdtTrans.setCardLimit("");
        tdtTrans.setFiller("");
        tdtList.add(tdtTrans);
    }


    /*
        根据交易流水再次构建TDT集合,并且合并利息
        先按照主卡附卡排序, 再按照入账日期排序
    */
    private Map<String, List<PostedTransaction>> sortPostTrans(List<PostedTransaction> postedTransRecords,
                                                               StatementFileBodyContext statementFileBodyContext) {

        Map<String, List<PostedTransaction>> postTransMap = postedTransRecords
                .stream()
                .collect(Collectors.groupingBy(PostedTransaction::getCardNumber));

        postTransMap.forEach((key, value) -> value.sort(Comparator.comparing(PostedTransaction::getPostingDate)));


        List<CardAuthorizationInfo> cardAuthorizationInfos = statementFileBodyContext.getCardAuthorizationInfos();
        Map<String, List<PostedTransaction>> postTransLinkedMap = new LinkedHashMap<>();

        cardAuthorizationInfos.forEach(e -> {
            if (postTransMap.containsKey(e.getCardNumber())) {
                postTransLinkedMap.put(e.getCardNumber(), postTransMap.get(e.getCardNumber()));
            }
        });

        return postTransLinkedMap;
    }


    /**
     * 获取商户描述,如果商户描述为空则取交易类型的描述
     */
    private String getTdtdesc(PostedTransaction element) {

        element.setTransactionDescription(AsciiConvertsUtils.nonAsciiReplaceBlank(element.getTransactionDescription()));


        DecimalFormat decimalFormat = new DecimalFormat("00");

        ParmTransactionCode parmTransactionCode = this.parmTransactionCodeHashMap.get(element.getPostingTransactionCode());

        String installmentDesc = "";

        if (StringUtils.isNotBlank(element.getInstallmentOrderId())
                && StringUtils.isNotBlank(element.getInstallmentCurrentTerm())
                && TransactionConstants.INSTALL_ATTRIBUTE.contains(parmTransactionCode.getTransactionAttribute())) {
            InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(element.getInstallmentOrderId());
            installmentDesc = decimalFormat.format(Integer.parseInt(element.getInstallmentCurrentTerm()))
                    + "/"
                    + decimalFormat.format(installOrder.getTerm());
        }

        if (StringUtils.isNotBlank(element.getMerchantName())) {
            return element.getMerchantName().trim().toUpperCase() + " " + installmentDesc;
        }

        String toUpperCase = element.getTransactionDescription().trim().toUpperCase();

        if (toUpperCase.length() + installmentDesc.length() > 45) {
            return toUpperCase.substring(installmentDesc.length() + 1) + installmentDesc;
        }
        return toUpperCase + installmentDesc;
    }


    private BigDecimal getTdsAmount(List<StatementTransDTO> statementTransDtos) {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        for (StatementTransDTO statementTransDTO : statementTransDtos) {
            StatementTransactionDetailRecordDTO item = (StatementTransactionDetailRecordDTO) statementTransDTO;
            if (Objects.equals("+", item.getAmountSign())) {
                bigDecimal = bigDecimal.add(item.getAmount().abs());
            } else {
                bigDecimal = bigDecimal.subtract(item.getAmount().abs());
            }
        }

        return bigDecimal;
    }


    private String getSubTotalDesc(String cardNumber) {
        return String.format("SUB TOTAL -  %s", cardNumber);
    }


    private String getCurrencyCode(String currency) {
        ParmCurrencyCode parmCurrencyCode = this.parmCurrencyCodeSelfMapper.selectByCurrencyCode(currency);

        if (parmCurrencyCode == null) {
            return currency;
        }
        return parmCurrencyCode.getAlphabeticCode();
    }


    private String getEmbossingName(CardBasicInfo cardBasicInfo, StatementFileBodyContext statementFileBodyContext) {


        if (isCorporateLiability(statementFileBodyContext.getAccountManagementInfo().getLiability()) &&
                cardBasicInfo.getCardNumber().startsWith("11")) {
            return statementFileBodyContext.getCorporateCustomerInfo().getName();
        }

        return Optional.ofNullable(AsciiConvertsUtils.nonAsciiReplaceBlank(cardBasicInfo.getEmbossingName1())).orElse("");
    }


    private Map<String, String> getStatementMsg(String accountProductCode) {
        Map.Entry<String, Map<String, String>> stringMapEntry = this.statementMsgMap.entrySet()
                .stream()
                .filter(e -> e.getKey().contains(accountProductCode))
                .findFirst()
                .orElse(null);

        if (stringMapEntry == null) {
            return Collections.emptyMap();
        }

        return stringMapEntry.getValue();
    }


    private String getTdmDescription(AccountManagementInfo accountManagementInfo) {
        Map<String, String> statementMsg = getStatementMsg(accountManagementInfo.getProductNumber());
        return Optional.ofNullable(statementMsg.get("CAA")).orElse("");
    }


    private void setAdditoinalStatementMessage(StatementCustomerFileBodyDTO fileBodyDTO,
                                               StatementFileBodyContext statementFileBodyContext) {

        AccountManagementInfo accountManagementInfo = statementFileBodyContext.getAccountManagementInfo();
        Map<String, String> messageMap = getStatementMsg(accountManagementInfo.getProductNumber());


        if (MapUtils.isNotEmpty(messageMap)) {
            StatementAdditionalMsgDTO statementMsgRecordDTO = new StatementAdditionalMsgDTO();
            statementMsgRecordDTO.setRecordType(StatementConstants.STATEMENT_MESSAGE);
            statementMsgRecordDTO.setMessageSequence("1");
            statementMsgRecordDTO.setMessageLine1(messageMap.get("001"));
            statementMsgRecordDTO.setMessageLine2(messageMap.get("002"));
            statementMsgRecordDTO.setMessageLine3(messageMap.get("003"));
            statementMsgRecordDTO.setMessageLine4(messageMap.get("004"));
            statementMsgRecordDTO.setMessageLine5(messageMap.get("005"));
            statementMsgRecordDTO.setMessageLine6(messageMap.get("006"));
            statementMsgRecordDTO.setMessageLine7(messageMap.get("007"));
            statementMsgRecordDTO.setMessageLine8(messageMap.get("008"));
            statementMsgRecordDTO.setMessageLine9(messageMap.get("009"));
            statementMsgRecordDTO.setMessageLine10(messageMap.get("010"));
            fileBodyDTO.setAdditionalMsgDtoS(Collections.singletonList(statementMsgRecordDTO));
        }
    }


    /**
     *
     * '-' means payment due date field is blank
     Account Block code  credit balance   zero balance               debit balance
                                                                current         overdue

     Blank                 -                 -              PaymentDueDate      IMMEDIATE

     VA                    -                  -             IMMEDIATE           IMMEDIATE


     NOt blank             -                  -             IMMEDIATE           IMMEDIATE
     *
     */
    private void setCustomerPaymentDueDate(StatementCustomerHeaderRecordDTO customerHeaderRecordDTO,
                                           StatementFileBodyContext statementFileBodyContext) {

        AccountManagementInfo managementInfo = statementFileBodyContext.getAccountManagementInfo();

        AccountStatementInfo accountStatementInfo = statementFileBodyContext.getStatementInfoMap()
                .get(managementInfo.getAccountManagementId());

        BigDecimal closeBalance = accountStatementInfo.getCloseBalance();

        if (closeBalance.compareTo(BigDecimal.ZERO) <= 0) {
            customerHeaderRecordDTO.setPaymentDueDate("");
            return;
        }

        boolean cycle = managementInfo.getCycleDue() >= 2;

        if (StringUtils.isBlank(accountStatementInfo.getBlockCode()) && !cycle) {
            customerHeaderRecordDTO.setPaymentDueDate(StatementFileUtils.formatYmd
                    .format(accountStatementInfo.getPaymentDueDate()));
            return;
        }

        customerHeaderRecordDTO.setPaymentDueDate("IMMEDIATE");

    }


    private void setCustomerAddressInfo(StatementCustomerHeaderRecordDTO customerHeaderRecordDTO,
                                        StatementFileBodyContext statementFileBodyContext) {

        CustomerBasicInfo customerBasicInfo = statementFileBodyContext.getCustomerBasicInfo();
        String statementAddressType = statementFileBodyContext.getAccountManagementInfo().getStatementAddressType();
//        log.info("管理账户----{}",statementFileBodyContext.getAccountManagementInfo().toString());
        //log.info("-----客户号---{}-----账单地址类型-----{}",statementFileBodyContext.getAccountManagementInfo().getCustomerId(),statementAddressType);
        //如果邮寄地址为家庭地址则空白
        if (Objects.equals(AddressTypeEnum.HOME_ADDRESS.getCode(), statementAddressType)) {
            customerHeaderRecordDTO.setCompanyName("");

        } else if (Objects.equals(AddressTypeEnum.COMPANY_ADDRESS.getCode(), statementAddressType)) {
            customerHeaderRecordDTO.setCompanyName(customerBasicInfo.getEmployerName());
        }

        CustomerAddressInfo customerAddressInfo = this.customerAddressInfoSelfMapper
                .selectByCustomerIdAndType(customerBasicInfo.getOrganizationNumber(), customerBasicInfo.getCustomerId(), statementAddressType);


        if (customerAddressInfo == null) {
            customerAddressInfo = new CustomerAddressInfo();
        }

        customerHeaderRecordDTO.setMailingAddressLine1(customerAddressInfo.getAddress());
        customerHeaderRecordDTO.setMailingAddressLine2(customerAddressInfo.getAddress2());
        customerHeaderRecordDTO.setMailingAddressLine3(customerAddressInfo.getAddress3());
        customerHeaderRecordDTO.setMailingAddressLine4(customerAddressInfo.getAddress4());
        customerHeaderRecordDTO.setMailingAddressLine5(customerAddressInfo.getAddress5());
        customerHeaderRecordDTO.setMailingAddressPostalCode(customerAddressInfo.getZipcode());
        customerHeaderRecordDTO.setMailingAddressCity(customerAddressInfo.getCity());
        customerHeaderRecordDTO.setMailingAddressState(customerAddressInfo.getDistrict());

        //将国家码值替换成全称
        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectAlpha2CountryCode(customerAddressInfo.getCountryCode());
        customerHeaderRecordDTO.setMailingAddressCountry(parmCountryCode == null
                ? customerAddressInfo.getCountryCode() : parmCountryCode.getDescription().toUpperCase());

    }

    private void setCorpAddressInfo(StatementCustomerHeaderRecordDTO customerHeaderRecordDTO,
                                    StatementFileBodyContext statementFileBodyContext) {

        CorporateCustomerInfo corporateCustomerInfo = statementFileBodyContext.getCorporateCustomerInfo();
        if (corporateCustomerInfo == null) {
            corporateCustomerInfo = new CorporateCustomerInfo();
        }
        logger.info("Corporate customer ID: {}", corporateCustomerInfo.getCorporateCustomerId());
        CustomerAddressInfo customerAddressInfo = customerAddressInfoSelfMapper.selectByCustomerIdAndType(OrgNumberUtils.getOrg(), corporateCustomerInfo.getCorporateCustomerId(), AddressTypeEnum.MAILING_ADDRESS.getCode());
        if (customerAddressInfo == null) {
            customerAddressInfo = new CustomerAddressInfo();
        }
        customerHeaderRecordDTO.setMailingAddressLine1(customerAddressInfo.getAddress());
        customerHeaderRecordDTO.setMailingAddressLine2(customerAddressInfo.getAddress2());
        customerHeaderRecordDTO.setMailingAddressLine3(customerAddressInfo.getAddress3());
        customerHeaderRecordDTO.setMailingAddressLine4(customerAddressInfo.getAddress4());
        customerHeaderRecordDTO.setMailingAddressLine5(customerAddressInfo.getAddress5());
        customerHeaderRecordDTO.setMailingAddressPostalCode(customerAddressInfo.getZipcode());
        customerHeaderRecordDTO.setMailingAddressCity(customerAddressInfo.getCity());
        customerHeaderRecordDTO.setMailingAddressState(customerAddressInfo.getProvince());
        //将国家码值替换成全称
        ParmCountryCode parmCountryCode = parmCountryCodeSelfMapper.selectAlpha2CountryCode(customerAddressInfo.getCountryCode());
        customerHeaderRecordDTO.setMailingAddressCountry(parmCountryCode == null
                ? customerAddressInfo.getCountryCode() : parmCountryCode.getDescription().toUpperCase());

    }


    private void setAccountPeriodRepayment(StatementCustomerFileBodyDTO fileBodyDTO,
                                           StatementFileBodyContext statementFileBodyContext) {

        Map<String, AccountStatementInfo> statementInfoMap = statementFileBodyContext.getStatementInfoMap();

        StatementCustomerHeaderRecordDTO headerRecordDTO = fileBodyDTO.getStatementCustomerHeaderRecordDTO();

        fileBodyDTO.getStatementAccountBodyS().forEach(t -> {
            StatementAccountHeaderDTO accountHeaderDTO = t.getAccountHeaderDTO();
            AccountStatementInfo accountStatementInfo = statementInfoMap.get(accountHeaderDTO.getAccountManagerId());

            ParmProductInfo parmProductInfo = acctProductInfoSelfMapper.selectByProdNumberAndOrganizationNumber(
                    accountHeaderDTO.getAccountProduct(), OrgNumberUtils.getOrg())
                    .stream().findFirst().orElse(null);

            if (parmProductInfo == null) {
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }

            //left: consume  right: cash
            ImmutablePair<InterestBearing, InterestBearing> interestBearing = getInterestBearing(parmProductInfo);



            if (currentHavePaymentAmount(accountHeaderDTO, statementFileBodyContext, parmProductInfo)) {

                StatementRepaymentPeriodMsgDTO statementRepaymentPeriodMsgDTO = new StatementRepaymentPeriodMsgDTO();
                statementRepaymentPeriodMsgDTO.setRecordType(StatementConstants.REPAYMENT_MSG);
                statementRepaymentPeriodMsgDTO.setCardholderNumber(accountHeaderDTO.getCardholderNumber());
                statementRepaymentPeriodMsgDTO.setOutstandingBalance(accountHeaderDTO.getOutstandingBalance());
                statementRepaymentPeriodMsgDTO.setOutstandingBalanceSign(accountHeaderDTO.getOutstandingBalanceSign());

                //这里设置为还款日 DDMMYYYY
                if (Objects.equals("IMMEDIATE", headerRecordDTO.getPaymentDueDate())
                        || StringUtils.isBlank(headerRecordDTO.getPaymentDueDate())) {
                    LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate();
                    statementRepaymentPeriodMsgDTO.setPaymentDueDate(StatementFileUtils.formatYmd.format(paymentDueDate));
                } else {
                    statementRepaymentPeriodMsgDTO.setPaymentDueDate(headerRecordDTO.getPaymentDueDate());
                }

                statementRepaymentPeriodMsgDTO.setInterestRate1(interestBearing.getLeft().getBaseRate().multiply(new BigDecimal("100")));
                statementRepaymentPeriodMsgDTO.setInterestRate2(interestBearing.getRight().getBaseRate().multiply(new BigDecimal("100")));
                statementRepaymentPeriodMsgDTO.setMinimumAmount(accountHeaderDTO.getMinimumPayment());


                //获取相关计算参数


                ParmLateFeeTable parmLateFeeTable = lateFeeTableSelfMapper.selectByOrgAndTid(
                        OrgNumberUtils.getOrg(), parmProductInfo.getLateFeeTableId());

                ParmStatementProcess statementProcess = statementProcessSelfMapper.isExists(OrgNumberUtils.getOrg(),
                        parmProductInfo.getStatementProcessingTableId());


                ImmutablePair<Integer, BigDecimal> payPair = getPayOffPeriodAndAmountCharge(interestBearing.getLeft(), accountStatementInfo, statementProcess);


                statementRepaymentPeriodMsgDTO.setPayOffPeriod(new BigDecimal(payPair.getLeft().toString()));
                statementRepaymentPeriodMsgDTO.setTotalAmountCharge(payPair.getRight());
                statementRepaymentPeriodMsgDTO.setTotalAmountChargeSign(getAmountSign(payPair.getRight()));


                statementRepaymentPeriodMsgDTO.setLatePaymentCharge(getLatePaymentCharge(parmLateFeeTable));

                statementRepaymentPeriodMsgDTO.setTotalOutstandingBalanceIn6months(getSixMouthOutStanding(
                        interestBearing.getLeft(), accountStatementInfo, statementRepaymentPeriodMsgDTO, statementProcess));

                statementRepaymentPeriodMsgDTO.setTotalOutstandingBalanceIn6monthsSign(getAmountSign(
                        statementRepaymentPeriodMsgDTO.getTotalOutstandingBalanceIn6months()));

                statementRepaymentPeriodMsgDTO.setRevolvingIndicator("Y");
                t.setStatementRepaymentPeriodMsgDTO(statementRepaymentPeriodMsgDTO);

            }
        });
    }


    //都不能为空  如果为空利息可以给0
    private ImmutablePair<InterestBearing, InterestBearing> getInterestBearing(ParmProductInfo parmProductInfo) {


        InterestBearing consume = null;
        InterestBearing cash = null;

        //坤海和个人, 消费的交易类型区分
        String transactionTypeCode = parmProductInfo.getProductNumber().startsWith("IF") ? "IF001" : "DCS01";
        ParmBalancePricingTable consumePricing = balancePricingTableSelfMapper.selectByIndex(
                parmProductInfo.getOrganizationNumber(), parmProductInfo.getBalancePricingTableId(), transactionTypeCode);


        if (Objects.nonNull(consumePricing)) {
            consume = bearingSelfMapper.selectByOrgAndTableId(consumePricing.getOrganizationNumber(),
                    consumePricing.getInterestBearingTableId());
        }

        ParmBalancePricingTable cashPricing = balancePricingTableSelfMapper.selectByIndex(
                parmProductInfo.getOrganizationNumber(), parmProductInfo.getBalancePricingTableId(), "DCS02");

        if (Objects.nonNull(cashPricing)) {
            cash = bearingSelfMapper.selectByOrgAndTableId(consumePricing.getOrganizationNumber(),
                    cashPricing.getInterestBearingTableId());
        }

        if (cash == null || Objects.equals(cash.getInterestMark(), "N")) {
            cash = new InterestBearing();
            cash.setBaseRate(BigDecimal.ZERO);
            cash.setPenaltyRate(BigDecimal.ZERO);
        }

        if (consume == null || Objects.equals(consume.getInterestMark(), "N")) {
            consume = new InterestBearing();
            consume.setBaseRate(BigDecimal.ZERO);
            consume.setPenaltyRate(BigDecimal.ZERO);
        }


        return ImmutablePair.of(consume, cash);
    }


    /**
     * 获取违约金 收取金额
     *
     * @todo 违约金收取方式暂时先按照产品设定的固定金额收取, 后续将最小客户级中的违约金计算方式抽取成公共逻辑
     */
    private BigDecimal getLatePaymentCharge(ParmLateFeeTable parmLateFeeTable) {
        String chargeOption = parmLateFeeTable.getChargeOption();

        if (WaiveFeeTakeTypeEnum.FIXED.getCode().equals(chargeOption)) {
            // 固定金额收取
            return parmLateFeeTable.getFixedAmnt();
        }
        return BigDecimal.ZERO;
    }


    /**
     * 当期的贷记交易的总额 < 上一期的账单余额 - 参数配置的还款容差(说明有欠款)
     * ( 总贷记 > 上期账单余额-全额还款容差  或者  有消费利息 )
     */
    private boolean currentHavePaymentAmount(StatementAccountHeaderDTO accountHeaderDTO,
                                             StatementFileBodyContext statementFileBodyContext,
                                             ParmProductInfo parmProductInfo) {

        AccountStatementInfo accountStatementInfo = statementFileBodyContext.getStatementInfoMap().get(statementFileBodyContext.getAccountManagementId());

        if (accountStatementInfo == null) {
            return false;
        }


        AccountStatementInfo lastStatementInfo = statementFileBodyContext.getLastStatementInfo();

        List<PostedTransaction> postedTransRecords = statementFileBodyContext.getPostedTransactions();

        BigDecimal lastOutstandingBalance = lastStatementInfo == null ? BigDecimal.ZERO : lastStatementInfo.getCloseBalance();

        BigDecimal repaymentAmount = postedTransRecords.stream()
                .filter(t -> Objects.equals(t.getDebitCreditIndcator(), DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode()))
                .map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //获取全额还款容差
        BigDecimal fullPaymentTolerance = getFullPaymentTolerance(parmProductInfo);

        boolean hasPaymentFlag = repaymentAmount.compareTo(lastOutstandingBalance.subtract(fullPaymentTolerance)) < 0;


        List<String> consumeTransCodes = getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR,
                Collections.singletonList(TransactionAttributeEnum.CONSUME_INTEREST.getCode()));

        List<PostedTransaction> postedTransactionList = postedTransRecords.stream()
                .filter(t -> consumeTransCodes.contains(t.getPostingTransactionCode()))
                .collect(Collectors.toList());

        return accountHeaderDTO.getOutstandingBalance().compareTo(BigDecimal.ZERO) > 0
                && (hasPaymentFlag || org.apache.commons.collections4.CollectionUtils.isNotEmpty(postedTransactionList));
    }


    private BigDecimal getFullPaymentTolerance(ParmProductInfo parmProductInfo) {
        ParmStatementProcess parmStatementProcesses = parmStatementProcessSelfMapper
                .isExists(parmProductInfo.getOrganizationNumber(), parmProductInfo.getStatementProcessingTableId());
        return parmStatementProcesses == null ? BigDecimal.ZERO : parmStatementProcesses.getPayoffVariance();

    }


    private BigDecimal getSixMouthOutStanding(InterestBearing consume,
                                              AccountStatementInfo accountStatementInfo,
                                              StatementRepaymentPeriodMsgDTO statementRepaymentPeriodMsgDTO,
                                              ParmStatementProcess statementProcess) {
        BigDecimal baseRate = consume.getBaseRate().add(consume.getPenaltyRate());

        LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate().plusMonths(1);

        BigDecimal currentDueAmount = accountStatementInfo.getCloseBalance();

        final int totalMonth = 6;

        for (int i = 0; i < totalMonth; i++) {
            int mouthDays = getCurrentMountDays(paymentDueDate);
            int yearDays = getCurrentYearDays(paymentDueDate);

            BigDecimal divide = currentDueAmount.multiply(baseRate).multiply(new BigDecimal(mouthDays))
                    .divide(new BigDecimal(yearDays), 2, BigDecimal.ROUND_HALF_UP);

            divide = getMinFeeValue(divide, statementProcess);
            currentDueAmount = currentDueAmount.add(divide).add(statementRepaymentPeriodMsgDTO.getLatePaymentCharge());

            paymentDueDate = paymentDueDate.plusMonths(1);
        }

        return currentDueAmount;
    }


    private BigDecimal getMinFeeValue(BigDecimal divide, ParmStatementProcess statementProcess) {
        BigDecimal min = Optional.ofNullable(statementProcess.getMinInterest()).orElse(BigDecimal.ZERO);
        return divide.compareTo(min) >= 0 ? divide : min;
    }


    /**
     * 获取还款期数的计算数据
     * <p>
     * 1  ((1000*27) + (900 * 3)) * (0.25/365)   20.34
     * <p>
     * 2. @todo  有部分数据利息计算值大于最小还款额导致计算不出来还款期数, 待 Yvonne 确定
     */
    private ImmutablePair<Integer, BigDecimal> getPayOffPeriodAndAmountCharge(InterestBearing consume,
                                                                              AccountStatementInfo accountStatementInfo,
                                                                              ParmStatementProcess statementProcess) {

        if (accountStatementInfo.getCloseBalance().compareTo(BigDecimal.ZERO) <= 0) {
            return ImmutablePair.of(1, BigDecimal.ZERO);
        }

        if (accountStatementInfo.getTotalDueAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return ImmutablePair.of(1, BigDecimal.ZERO);
        }


        BigDecimal baseRate = consume.getBaseRate();

        LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate().plusMonths(1);

        Map<LocalDate, ImmutablePair<BigDecimal, BigDecimal>> termAmountMap = new LinkedHashMap<>();

        int dayOfMouth = paymentDueDate.getDayOfMonth();
        BigDecimal currentDueAmount = accountStatementInfo.getCloseBalance();
        BigDecimal mainPaymentAmount = accountStatementInfo.getTotalDueAmount();
        int term = 0;


        while (currentDueAmount.compareTo(mainPaymentAmount) >= 0) {

            int mouthDays = getCurrentMountDays(paymentDueDate);
            int yearDays = getCurrentYearDays(paymentDueDate);

            BigDecimal fee = currentDueAmount.multiply(new BigDecimal(dayOfMouth))
                    .add(currentDueAmount.subtract(mainPaymentAmount).multiply(new BigDecimal(mouthDays - dayOfMouth)))
                    .multiply(baseRate)
                    .divide(new BigDecimal(yearDays), 2, BigDecimal.ROUND_HALF_UP);

            if (currentDueAmount.compareTo(mainPaymentAmount) > 0) {
                fee = getMinFeeValue(fee, statementProcess);
            }

            if (mainPaymentAmount.compareTo(fee) <= 0) {
                break;
            }


            termAmountMap.put(paymentDueDate, ImmutablePair.of(currentDueAmount, fee));

            paymentDueDate = paymentDueDate.plusMonths(1);
            currentDueAmount = currentDueAmount.subtract(mainPaymentAmount).add(fee);
            term++;
        }

        BigDecimal reduce = termAmountMap.values().stream().map(ImmutablePair::getRight).reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        if (currentDueAmount.compareTo(mainPaymentAmount) < 0 && currentDueAmount.compareTo(BigDecimal.ZERO) > 0) {
            term = term + 1;
        }

        return ImmutablePair.of(term, reduce);
    }


    private static int getCurrentYearDays(LocalDate paymentDueDate) {
        LocalDate begin = paymentDueDate.with(TemporalAdjusters.firstDayOfYear());
        LocalDate end = paymentDueDate.with(TemporalAdjusters.lastDayOfYear());

        return (int) (end.toEpochDay() - begin.toEpochDay() + 1);
    }

    private static int getCurrentMountDays(LocalDate paymentDueDate) {
        LocalDate begin = paymentDueDate.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = paymentDueDate.with(TemporalAdjusters.lastDayOfMonth());

        return (int) (end.toEpochDay() - begin.toEpochDay() + 1);
    }


    /**
     * 通过客户的本期账单获取到客户所有的交易流水信息
     * 再通过入账交易码值去统计客户本期账单里的消费金额 取现金额 还款金额 以及费用金额
     */
    private void setCustomerTransData(StatementFileBodyContext statementFileBodyContext,
                                      StatementCustomerHeaderRecordDTO customerHeaderRecordDTO) {

        List<PostedTransaction> postedTransRecords = statementFileBodyContext.getPostedTransactions();

        //消费的借贷记交易
        customerHeaderRecordDTO.setRetailPurchases(getTransAmount(postedTransRecords,
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.CONSUME.getCode())),
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.CREDIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.CONSUME.getCode()))));

        customerHeaderRecordDTO.setRetailPurchasesSign(getAmountSign(customerHeaderRecordDTO.getRetailPurchases()));


        //取现的借贷记交易
        customerHeaderRecordDTO.setCashAdvances(getTransAmount(postedTransRecords,
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.CASH.getCode())),
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.CREDIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.CASH.getCode()))));
        customerHeaderRecordDTO.setCashAdvancesSign(getAmountSign(customerHeaderRecordDTO.getCashAdvances()));


        //费用的借贷记交易
        List<String> feeAttribute = Arrays.asList(TransactionAttributeEnum.CONSUME_FEE.getCode(),
                TransactionAttributeEnum.CASH_FEE.getCode(), TransactionAttributeEnum.CONSUME_INTEREST.getCode(),
                TransactionAttributeEnum.CASH_INTEREST.getCode(), TransactionAttributeEnum.INSTALL_FEE.getCode(),
                TransactionAttributeEnum.INSTALL_FEE_AMORTIZE.getCode());

        customerHeaderRecordDTO.setFeesAndDebits(getTransAmount(postedTransRecords,
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR,
                        feeAttribute),
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.CREDIT_INDICATOR,
                        feeAttribute)));
        customerHeaderRecordDTO.setFeesAndDebitsSign(getAmountSign(customerHeaderRecordDTO.getFeesAndDebits()));


        //还款的借贷记交易
        customerHeaderRecordDTO.setPaymentSandCredits(getTransAmount(postedTransRecords,
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.DEBIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.REPAYMENT.getCode())),
                getConsumeTransCodes(parmTransactionCodes, DebitCreditIndicatorEnum.CREDIT_INDICATOR, Collections.singletonList(TransactionAttributeEnum.REPAYMENT.getCode()))));

        customerHeaderRecordDTO.setPaymentSandCreditsSign(getAmountSign(customerHeaderRecordDTO.getPaymentSandCredits()));


    }


    /**
     * 根据借贷记标识 交易属性 查找对应交易类型的入账交易码
     *
     * @param parmTransactionCodes 入账交易码
     * @param debitIndicator       借贷记标识
     * @param transAttribute       交易属性集合
     * @return 交易码值集合
     */
    private List<String> getConsumeTransCodes(List<ParmTransactionCode> parmTransactionCodes,
                                              DebitCreditIndicatorEnum debitIndicator, List<String> transAttribute) {
        return parmTransactionCodes.stream()
                .filter(t -> Objects.equals(t.getDebitCreditIndicator(), debitIndicator.getCode())
                        && transAttribute.contains(t.getTransactionAttribute()))
                .map(ParmTransactionCode::getTransactionCode).collect(Collectors.toList());
    }


    /**
     * 根据实时入账交易码统计不同交易类型的金额流水
     */
    private BigDecimal getTransAmount(List<PostedTransaction> postedTransRecords,
                                      List<String> debitTransCode,
                                      List<String> creditTransCode) {

        Optional<BigDecimal> debitAmount = postedTransRecords.stream().filter(t -> debitTransCode.contains(t.getPostingTransactionCode())
                && Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), t.getDebitCreditIndcator()))
                .map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add);

        Optional<BigDecimal> creditAmount = postedTransRecords.stream().filter(t -> creditTransCode.contains(t.getPostingTransactionCode())
                && Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), t.getDebitCreditIndcator()))
                .map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add);

        return debitAmount.orElse(BigDecimal.ZERO).subtract(creditAmount.orElse(BigDecimal.ZERO));
    }


    private BigDecimal getMinimumPayment(Map<String, AccountStatementInfo> statementInfoMap) {
        return statementInfoMap.values()
                .stream()
                .map(AccountStatementInfo::getTotalDueAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    private List<StatementAccountBodyDTO> getStatementAccountBodyS(String customerId,
                                                                   StatementFileBodyContext statementFileBodyContext) {
        //@todo 多账户合并方式处理
        List<AccountManagementInfo> accountManagementInfos = Collections.singletonList(statementFileBodyContext.getAccountManagementInfo());

        Map<String, AccountStatementInfo> statementInfoMap = statementFileBodyContext.getStatementInfoMap();

        getCardAuthorizationInfo(statementFileBodyContext, customerId,
                cardAuthorizationInfoSelfMapper);
        AtomicInteger index = new AtomicInteger(1);
        return accountManagementInfos.stream().filter(t->!skipAccountProduct.contains(t.getProductNumber())).map(t -> {
            StatementAccountBodyDTO statementAccountBodyDto = new StatementAccountBodyDTO();
            StatementAccountHeaderDTO statementAccountHeaderDTO = new StatementAccountHeaderDTO();
            statementAccountHeaderDTO.setRecordType(StatementConstants.ACCOUNT_HEADER);
            statementAccountHeaderDTO.setAccountManagerId(t.getAccountManagementId());
            statementAccountHeaderDTO.setAccountProduct(t.getProductNumber());
            statementAccountHeaderDTO.setActive(getAccountActive(t));
            statementAccountHeaderDTO.setSequenceNumber(String.valueOf(index.getAndIncrement()));
            //设置卡片信息
            setCardInfo(statementAccountHeaderDTO, t, customerId, statementFileBodyContext);
            statementAccountHeaderDTO.setBillingCurrency(getCurrencyCode(t.getCurrency()));

            AccountStatementInfo accountStatementInfo = statementInfoMap.get(t.getAccountManagementId());
            statementAccountHeaderDTO.setMinimumPayment(accountStatementInfo.getTotalDueAmount());

            statementAccountHeaderDTO.setOutstandingBalance(Optional.ofNullable(accountStatementInfo.getCloseBalance())
                    .orElse(BigDecimal.ZERO));

            statementAccountHeaderDTO.setOutstandingBalanceSign(getAmountSign(statementAccountHeaderDTO.getOutstandingBalance()));
            statementAccountHeaderDTO.setCurrentAmountOverdue(t.getCurrentAmountOverdue());


            /*
               1. 从账单信息中获取期初积分余额
               2. 从积分流水表获取当期 已获得的积分数据
               3. 从积分流水表获取当期 已用的积分数据
               4. 期末积分余额 = 期初积分余额 + 获取的积分数据 - 使用的积分数据
             */
            boolean bonusFlag = setExcludeBonus(t.getProductNumber());
            statementAccountHeaderDTO.setRewardsIndicator(accountStatementInfo.getRewardsIndicator());
            //1 账单期初积分数量:  abs = false
            statementAccountHeaderDTO.setRewardsPointsOpeningBalance(bonusFlag?ON_BONUS_NA:(Optional.ofNullable(accountStatementInfo.getOpenBonusPoints()+"")
                    .orElse(BigDecimal.ZERO+"")));

            //3 本月赎回的积分数量
            BigDecimal pointsRedeemed = accountStatementInfo.getRewardsPointsRedeemed().setScale(0, BigDecimal.ROUND_DOWN);
            statementAccountHeaderDTO.setRewardsPointsRedeemedThisMonth(bonusFlag?ON_BONUS_NA:pointsRedeemed+"");
            statementAccountHeaderDTO.setRewardsPointsRedeemedThisMonthSign(bonusFlag?NO_BONUS_SIGN:
                    getAmountSign(new BigDecimal(statementAccountHeaderDTO.getRewardsPointsRedeemedThisMonth()))
            );

            //2 本期获取的积分数据
            BigDecimal earnPoint = accountStatementInfo.getCloseBonusPoints().subtract(accountStatementInfo.getOpenBonusPoints())
                    .add(pointsRedeemed);
            statementAccountHeaderDTO.setRewardsPointsEarnedThisMonth(bonusFlag?ON_BONUS_NA:earnPoint+"");
            statementAccountHeaderDTO.setRewardsPointsEarnedThisMonthSign(bonusFlag?NO_BONUS_SIGN:
                    getAmountSign(new BigDecimal(statementAccountHeaderDTO.getRewardsPointsEarnedThisMonth())));


            //本月过期里程数
            statementAccountHeaderDTO.setMilePointsExpiredThisMonth(bonusFlag?ON_BONUS_NA+"":accountStatementInfo.getRewardsMilesExpiredPreMonth()+"");
            statementAccountHeaderDTO.setRewardsPointsExpiredThisMonthSign(bonusFlag?NO_BONUS_SIGN:getAmountSign(
                    StringUtils.isEmpty(statementAccountHeaderDTO.getMilePointsExpiredThisMonth())?BigDecimal.ZERO:new BigDecimal(statementAccountHeaderDTO.getMilePointsExpiredThisMonth())));
            //从他人那里获取的积分数量
            statementAccountHeaderDTO.setRewardsPointsMemberGetMember(BigDecimal.ZERO);
            statementAccountHeaderDTO.setRewardsPointsMemberGetMemberSign(
                    getAmountSign(statementAccountHeaderDTO.getRewardsPointsMemberGetMember())
            );


            //账单期末的积分数量, abs= false
            statementAccountHeaderDTO.setRewardsPointsClosingBalance(bonusFlag?ON_BONUS_NA:
                    (Optional.ofNullable(accountStatementInfo.getCloseBonusPoints()+"").orElse(BigDecimal.ZERO+"")));

            //额度信息
            setAccountLimitInfo(statementAccountHeaderDTO, t, customerId);

            statementAccountHeaderDTO.setRewardsPointsExpiry1("204912");
            statementAccountHeaderDTO.setRewardsPointsExpiring1(BigDecimal.ZERO);
            statementAccountHeaderDTO.setRewardsPointsExpiry2("204912");
            statementAccountHeaderDTO.setRewardsPointsExpiring2(BigDecimal.ZERO);
            statementAccountHeaderDTO.setRewardsPointsExpiry3("204912");
            statementAccountHeaderDTO.setRewardsPointsExpiring3(BigDecimal.ZERO);
            statementAccountHeaderDTO.setRewardsPointsExpiry4("204912");
            statementAccountHeaderDTO.setRewardsPointsExpiring4(BigDecimal.ZERO);

            statementAccountBodyDto.setAccountHeaderDTO(statementAccountHeaderDTO);
            return statementAccountBodyDto;
        }).collect(Collectors.toList());
    }


    private void setAccountLimitInfo(StatementAccountHeaderDTO statementAccountHeaderDTO,
                                     AccountManagementInfo accountManagementInfo,
                                     String customerId) {

        List<LimitCustCreditInfo> custCreditInfos = limitCustCreditInfoMapper.selectByOrgNumberAndCustomerIdAndTypeCodes(
                customerId, accountManagementInfo.getOrganizationNumber(), Arrays.asList("SC01", "SC04", "SC99"));

        Optional<LimitCustCreditInfo> sc01 = custCreditInfos.stream()
                .filter(t -> Objects.equals("SC01", t.getLimitTypeCode())
                        && Objects.equals(t.getAccountProductCode(), accountManagementInfo.getProductNumber())).findFirst();
        if (sc01.isPresent()) {
            statementAccountHeaderDTO.setCreditLimit(sc01.get().getFixLimitAmount());
            return;
        }

        Optional<LimitCustCreditInfo> sc04 = custCreditInfos.stream()
                .filter(t -> Objects.equals("SC04", t.getLimitTypeCode())
                        && Objects.equals(t.getAccountProductCode(), accountManagementInfo.getProductNumber())).findFirst();

        if (sc04.isPresent()) {
            statementAccountHeaderDTO.setCreditLimit(sc04.get().getFixLimitAmount());
            return;
        }

        Optional<LimitCustCreditInfo> sc99 = custCreditInfos.stream().filter(
                t -> Objects.equals("SC99", t.getLimitTypeCode())).findFirst();
        sc99.ifPresent(t -> statementAccountHeaderDTO.setCreditLimit(t.getFixLimitAmount()));

    }


    private Boolean getAccountActive(AccountManagementInfo t) {
        return Objects.equals(t.getAccountStatus(), "");
    }


    private String getAmountSign(BigDecimal amount) {
        return amount.compareTo(BigDecimal.ZERO) < 0 ? "-" : "+";
    }


    /**
     * 通过卡产品上的主账产品, 获取该账产品对应的卡产品
     * 先获取主客户或者附客户是该客户的所有卡片信息,再过滤出来对应卡产品的数据
     * 然后再根据主卡或者附卡再过滤对应客户的卡片数据信息
     */
    private void setCardInfo(StatementAccountHeaderDTO statementAccountHeaderDTO,
                             AccountManagementInfo accountManagementInfo, String customerId,
                             StatementFileBodyContext statementFileBodyContext) {

        ParmAcctProductMainInfo parmAcctProductMainInfo = statementFileBodyContext.getParmAcctProductMainInfo();

        ParmCardProductInfo parmCardProductInfo = getCardProductNumberByAccountProductNumber(accountManagementInfo,
                cardProductInfoSelfMapper);


        CardAuthorizationInfo cardAuthorizationInfo = getValidCardAuthorizationInfo(statementFileBodyContext,
                customerId, parmCardProductInfo);

        statementAccountHeaderDTO.setCardholderNumber(cardAuthorizationInfo.getCardNumber());
        statementAccountHeaderDTO.setCardDescriptionLine1(parmAcctProductMainInfo.getDescription());

        statementAccountHeaderDTO.setCardProductType(getCardProductType(parmAcctProductMainInfo));

        statementAccountHeaderDTO.setCardProductGroup(cardAuthorizationInfo.getProductNumber()
                .substring(cardAuthorizationInfo.getProductNumber().length() - 4));
        statementAccountHeaderDTO.setCardAuthorizationInfo(cardAuthorizationInfo);
    }


    private String getCardProductType(ParmAcctProductMainInfo parmAcctProductMainInfo) {
        String attribute = parmAcctProductMainInfo.getAttribute();

        if (Arrays.asList("D", "G", "C", "E").contains(attribute)) {
            return "R";
        }
        return "C";
    }
    private boolean setExcludeBonus(String accProduct){
        boolean flag = false;
        if (accProductList != null && accProductList.contains(accProduct)){
            flag=true;
        }
        return flag;
    }
}
